@echo off
chcp 65001 >nul
title تثبيت وتشغيل تطبيق قطع غيار السيارات

echo ================================================================
echo 🚗 مثبت تطبيق ربط محلات قطع غيار السيارات
echo    Auto Parts Shops Connection System Installer
echo ================================================================
echo.

echo 🔍 جاري التحقق من متطلبات النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Python أولاً:
    echo    1. اذهب إلى: https://python.org/downloads
    echo    2. حمل أحدث إصدار من Python
    echo    3. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo    4. أعد تشغيل هذا الملف بعد التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
python --version
echo.

REM التحقق من pip
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 📥 جاري تثبيت pip...
    python -m ensurepip --upgrade
)

echo ✅ pip متوفر
echo.

echo 📦 جاري تثبيت المتطلبات...
echo    هذا قد يستغرق بضع دقائق...
echo.

REM تحديث pip
python -m pip install --upgrade pip

REM تثبيت المتطلبات
python -m pip install Flask==2.3.3
python -m pip install Flask-SQLAlchemy==3.0.5
python -m pip install Werkzeug==2.3.7

if errorlevel 1 (
    echo ⚠️ حدثت مشكلة في تثبيت بعض المتطلبات
    echo 🔄 جاري المحاولة مرة أخرى...
    python -m pip install Flask Flask-SQLAlchemy Werkzeug
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح
echo.

echo 🚀 جاري تشغيل التطبيق...
echo.
echo ================================================================
echo 🌐 التطبيق سيعمل على: http://localhost:5000
echo 🌐 أو: http://127.0.0.1:5000
echo.
echo 👤 حساب المدير الافتراضي:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 📋 الميزات:
echo    ✅ صفحة رئيسية تعرض أحدث القطع
echo    ✅ نظام تسجيل دخول للمحلات والمدير
echo    ✅ لوحة تحكم للمحلات لإدارة المنتجات
echo    ✅ نظام بحث متقدم
echo    ✅ نظام تقييم للمحلات
echo.
echo 🛑 لإيقاف التطبيق: اضغط Ctrl+C في نافذة Python
echo ================================================================
echo.

REM تشغيل التطبيق
python run.py

echo.
echo ✅ تم إغلاق التطبيق بنجاح
echo 📝 شكراً لاستخدام تطبيق ربط محلات قطع غيار السيارات
pause
