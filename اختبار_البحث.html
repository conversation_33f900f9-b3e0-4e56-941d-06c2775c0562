<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث - قطع غيار السيارات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-car me-2"></i>
                اختبار البحث - قطع غيار السيارات
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Search Section -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث في المنتجات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="searchName" placeholder="اسم المنتج">
                            <label for="searchName">اسم المنتج</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="searchCategory">
                                <option value="">جميع الفئات</option>
                                <option value="فلاتر">فلاتر</option>
                                <option value="فرامل">فرامل</option>
                                <option value="زيوت">زيوت</option>
                                <option value="كهرباء">كهرباء</option>
                                <option value="إطارات">إطارات</option>
                                <option value="محرك">محرك</option>
                            </select>
                            <label for="searchCategory">الفئة</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="searchBrand" placeholder="الماركة">
                            <label for="searchBrand">الماركة</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100 h-100" onclick="searchProducts()">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="fas fa-times me-1"></i>
                            مسح البحث
                        </button>
                        <button class="btn btn-outline-success btn-sm ms-2" onclick="loadTestData()">
                            <i class="fas fa-plus me-1"></i>
                            تحميل بيانات تجريبية
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <span id="searchResults" class="text-muted small"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    <span id="tableTitle">جدول قطع الغيار والمحلات</span>
                </h5>
                <span class="badge bg-success" id="productCount">0 منتج</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم القطعة</th>
                                <th>الفئة</th>
                                <th>الماركة</th>
                                <th>المحل</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        اضغط "تحميل بيانات تجريبية" لبدء الاختبار
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test data
        let products = [];
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;
            
            container.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
        
        // Load test data
        function loadTestData() {
            products = [
                {
                    id: 1,
                    name: 'فلتر هواء تويوتا كامري',
                    category: 'فلاتر',
                    brand: 'تويوتا',
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    price: 85.0,
                    quantity: 15
                },
                {
                    id: 2,
                    name: 'تيل فرامل هوندا أكورد',
                    category: 'فرامل',
                    brand: 'هوندا',
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    price: 120.0,
                    quantity: 8
                },
                {
                    id: 3,
                    name: 'زيت محرك موبيل 1',
                    category: 'زيوت',
                    brand: 'موبيل',
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    price: 95.0,
                    quantity: 25
                },
                {
                    id: 4,
                    name: 'بطارية نيسان التيما',
                    category: 'كهرباء',
                    brand: 'نيسان',
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    price: 280.0,
                    quantity: 5
                },
                {
                    id: 5,
                    name: 'فلتر زيت تويوتا كورولا',
                    category: 'فلاتر',
                    brand: 'تويوتا',
                    shopName: 'محل النجمة لقطع الغيار',
                    price: 45.0,
                    quantity: 20
                },
                {
                    id: 6,
                    name: 'إطار ميشلان',
                    category: 'إطارات',
                    brand: 'ميشلان',
                    shopName: 'محل النجمة لقطع الغيار',
                    price: 350.0,
                    quantity: 12
                }
            ];
            
            displayProducts(products);
            showNotification(`تم تحميل ${products.length} منتج تجريبي بنجاح!`, 'success');
        }
        
        // Display products
        function displayProducts(productsToShow) {
            const tableBody = document.getElementById('productsTableBody');
            const productCount = document.getElementById('productCount');
            
            tableBody.innerHTML = '';
            productCount.textContent = `${productsToShow.length} منتج`;
            
            if (productsToShow.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-search me-2"></i>
                                لم يتم العثور على منتجات تطابق معايير البحث
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }
            
            productsToShow.forEach(product => {
                const row = `
                    <tr>
                        <td><strong>${product.name}</strong></td>
                        <td><span class="badge bg-secondary">${product.category}</span></td>
                        <td><span class="badge bg-info">${product.brand}</span></td>
                        <td>${product.shopName}</td>
                        <td><strong class="text-success">${product.price} ريال</strong></td>
                        <td><span class="badge ${product.quantity > 0 ? 'bg-success' : 'bg-danger'}">${product.quantity}</span></td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }
        
        // Search products
        function searchProducts() {
            const searchName = document.getElementById('searchName').value.trim().toLowerCase();
            const searchCategory = document.getElementById('searchCategory').value;
            const searchBrand = document.getElementById('searchBrand').value.trim().toLowerCase();
            
            if (products.length === 0) {
                showNotification('يرجى تحميل البيانات التجريبية أولاً', 'warning');
                return;
            }
            
            let filteredProducts = products;
            
            // Apply filters
            if (searchName) {
                filteredProducts = filteredProducts.filter(product =>
                    product.name.toLowerCase().includes(searchName)
                );
            }
            
            if (searchCategory) {
                filteredProducts = filteredProducts.filter(product =>
                    product.category === searchCategory
                );
            }
            
            if (searchBrand) {
                filteredProducts = filteredProducts.filter(product =>
                    product.brand.toLowerCase().includes(searchBrand)
                );
            }
            
            // Update table title and results count
            const tableTitle = document.getElementById('tableTitle');
            const searchResults = document.getElementById('searchResults');
            
            if (searchName || searchCategory || searchBrand) {
                tableTitle.textContent = 'نتائج البحث';
                searchResults.textContent = `تم العثور على ${filteredProducts.length} منتج من أصل ${products.length}`;
                
                if (filteredProducts.length === 0) {
                    showNotification('لم يتم العثور على منتجات تطابق معايير البحث', 'warning');
                } else {
                    showNotification(`تم العثور على ${filteredProducts.length} منتج`, 'success');
                }
            } else {
                tableTitle.textContent = 'جدول قطع الغيار والمحلات';
                searchResults.textContent = '';
            }
            
            displayProducts(filteredProducts);
        }
        
        // Clear search
        function clearSearch() {
            document.getElementById('searchName').value = '';
            document.getElementById('searchCategory').value = '';
            document.getElementById('searchBrand').value = '';
            document.getElementById('tableTitle').textContent = 'جدول قطع الغيار والمحلات';
            document.getElementById('searchResults').textContent = '';
            
            displayProducts(products);
            showNotification('تم مسح البحث', 'info');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add search event listeners
            document.getElementById('searchName').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchProducts();
                }
            });
            
            document.getElementById('searchBrand').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchProducts();
                }
            });
            
            document.getElementById('searchCategory').addEventListener('change', function() {
                if (this.value && products.length > 0) {
                    searchProducts();
                }
            });
            
            showNotification('مرحباً بك في اختبار البحث! اضغط "تحميل بيانات تجريبية" للبدء', 'info');
        });
    </script>
</body>
</html>
