🚗 تطبيق ربط محلات قطع غيار السيارات
=======================================

📋 طرق التشغيل:

الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف "install_and_run.bat"
2. انتظر حتى يتم تثبيت المتطلبات
3. افتح المتصفح واذهب إلى: http://localhost:5000

الطريقة الثانية:
1. انقر نقراً مزدوجاً على ملف "start_app.bat"
2. افتح المتصفح واذهب إلى: http://localhost:5000

الطريقة الثالثة (يدوياً):
1. افتح Command Prompt في مجلد التطبيق
2. اكتب: python -m pip install -r requirements.txt
3. اكتب: python run.py
4. افتح المتصفح واذهب إلى: http://localhost:5000

🔐 الحسابات الافتراضية:

المدير:
- اسم المستخدم: admin
- كلمة المرور: admin123

📱 الوصول للتطبيق:
- محلياً: http://localhost:5000
- الشبكة المحلية: http://[عنوان-IP]:5000

✨ الميزات:
✅ صفحة رئيسية تعرض أحدث القطع
✅ نظام تسجيل دخول للمحلات والمدير
✅ لوحة تحكم للمحلات لإدارة المنتجات
✅ لوحة تحكم للمدير لإدارة المحلات
✅ نظام بحث متقدم حسب الماركة والموديل
✅ نظام تقييم للمحلات والمنتجات
✅ إدارة الأسعار والكميات

🛠️ متطلبات النظام:
- Windows 7 أو أحدث
- Python 3.7 أو أحدث
- اتصال بالإنترنت (لتثبيت المتطلبات)

🔧 حل المشاكل الشائعة:

مشكلة: Python غير موجود
الحل: حمل وثبت Python من https://python.org

مشكلة: خطأ في تثبيت المتطلبات
الحل: تأكد من الاتصال بالإنترنت وأعد المحاولة

مشكلة: لا يمكن الوصول للتطبيق
الحل: تأكد من أن المنفذ 5000 غير مستخدم

🛑 إيقاف التطبيق:
اضغط Ctrl+C في نافذة Python أو أغلق النافذة

📞 للمساعدة:
راجع ملف README.md للتفاصيل الكاملة
