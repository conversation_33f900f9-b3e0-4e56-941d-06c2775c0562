@echo off
chcp 65001 >nul
title تشخيص مشاكل التطبيق

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                    تشخيص مشاكل التطبيق                     █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 فحص Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 الحلول المقترحة:
    echo 1. تثبيت Python من: https://python.org/downloads
    echo 2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo 3. أعد تشغيل الكمبيوتر بعد التثبيت
    echo.
    goto :end
) else (
    echo ✅ Python مثبت
)

echo.
echo 🔍 فحص pip...
python -m pip --version
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 🔧 جاري محاولة إصلاح pip...
    python -m ensurepip --upgrade
) else (
    echo ✅ pip متوفر
)

echo.
echo 🔍 فحص Flask...
python -c "import flask; print('Flask version:', flask.__version__)"
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    echo 📦 جاري تثبيت Flask...
    python -m pip install Flask
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        echo 🔧 جرب: python -m pip install --user Flask
        goto :end
    )
) else (
    echo ✅ Flask مثبت
)

echo.
echo 🔍 فحص المنفذ 5000...
netstat -an | findstr :5000
if not errorlevel 1 (
    echo ⚠️ المنفذ 5000 مستخدم بالفعل
    echo 🔧 أغلق التطبيق الذي يستخدم المنفذ أو استخدم منفذ آخر
)

echo.
echo 🔍 فحص ملفات التطبيق...
if exist app.py (
    echo ✅ app.py موجود
) else (
    echo ❌ app.py مفقود
)

if exist templates (
    echo ✅ مجلد templates موجود
) else (
    echo ❌ مجلد templates مفقود
)

if exist static (
    echo ✅ مجلد static موجود
) else (
    echo ❌ مجلد static مفقود
)

echo.
echo 🧪 اختبار تشغيل Python بسيط...
python -c "print('Python يعمل بشكل طبيعي')"
if errorlevel 1 (
    echo ❌ مشكلة في تشغيل Python
    goto :end
)

echo.
echo 🧪 اختبار Flask بسيط...
python -c "from flask import Flask; app = Flask(__name__); print('Flask يعمل بشكل طبيعي')"
if errorlevel 1 (
    echo ❌ مشكلة في Flask
    echo 📦 جاري إعادة تثبيت Flask...
    python -m pip uninstall -y Flask
    python -m pip install Flask
    goto :end
)

echo.
echo ████████████████████████████████████████████████████████████████
echo █                      نتائج التشخيص                         █
echo ████████████████████████████████████████████████████████████████
echo.
echo ✅ جميع المتطلبات متوفرة
echo 🚀 يمكنك الآن تشغيل التطبيق
echo.
echo 📋 طرق التشغيل:
echo 1. python app.py
echo 2. python simple_test.py (للاختبار)
echo 3. تشغيل_التطبيق.bat
echo.

:end
echo.
pause
