#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
"""

try:
    from flask import Flask, render_template, request, redirect, url_for, flash, session
    from flask_sqlalchemy import SQLAlchemy
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime
    import os
    import secrets
    
    print("✅ تم استيراد جميع المكتبات بنجاح")
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-secret-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_auto_parts.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db = SQLAlchemy(app)
    
    # نموذج بسيط للاختبار
    class User(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password_hash = db.Column(db.String(120), nullable=False)
        role = db.Column(db.String(20), default='shop')
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>اختبار تطبيق قطع غيار السيارات</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 0; }
            </style>
        </head>
        <body>
            <div class="hero text-center">
                <div class="container">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-car me-3"></i>
                        تطبيق ربط محلات قطع غيار السيارات
                    </h1>
                    <p class="lead mb-4">التطبيق يعمل بنجاح! 🎉</p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body text-dark">
                                    <h3 class="text-primary mb-4">الحسابات المتوفرة</h3>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body">
                                                    <h5><i class="fas fa-user-shield me-2"></i>المدير</h5>
                                                    <p class="mb-1">اسم المستخدم: <strong>admin</strong></p>
                                                    <p class="mb-0">كلمة المرور: <strong>admin123</strong></p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body">
                                                    <h5><i class="fas fa-store me-2"></i>محل تجريبي</h5>
                                                    <p class="mb-1">اسم المستخدم: <strong>shop1</strong></p>
                                                    <p class="mb-0">كلمة المرور: <strong>shop123</strong></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <a href="/login" class="btn btn-primary btn-lg me-3">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            تسجيل الدخول
                                        </a>
                                        <a href="/full_app" class="btn btn-success btn-lg">
                                            <i class="fas fa-rocket me-2"></i>
                                            التطبيق الكامل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="container py-5">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-search fa-3x text-primary mb-3"></i>
                        <h4>بحث متقدم</h4>
                        <p class="text-muted">ابحث عن قطع الغيار حسب الماركة والموديل</p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h4>تقييمات موثوقة</h4>
                        <p class="text-muted">تقييمات حقيقية من العملاء</p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h4>محلات معتمدة</h4>
                        <p class="text-muted">جميع المحلات مراجعة ومعتمدة</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/login')
    def login():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تسجيل الدخول</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>body { font-family: 'Cairo', sans-serif; }</style>
        </head>
        <body class="bg-light">
            <div class="container py-5">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card shadow">
                            <div class="card-body p-5">
                                <h3 class="text-center mb-4">تسجيل الدخول</h3>
                                <div class="alert alert-info">
                                    <h5>الحسابات المتوفرة:</h5>
                                    <p><strong>المدير:</strong> admin / admin123</p>
                                    <p><strong>المحل:</strong> shop1 / shop123</p>
                                </div>
                                <form>
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" value="admin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" value="admin123">
                                    </div>
                                    <button type="button" class="btn btn-primary w-100" onclick="alert('هذا مجرد اختبار! استخدم التطبيق الكامل للدخول الفعلي')">
                                        تسجيل الدخول
                                    </button>
                                </form>
                                <div class="text-center mt-3">
                                    <a href="/" class="btn btn-outline-secondary">العودة للرئيسية</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/full_app')
    def full_app():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>التطبيق الكامل</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>body { font-family: 'Cairo', sans-serif; }</style>
        </head>
        <body class="bg-light">
            <div class="container py-5">
                <div class="text-center">
                    <h2 class="mb-4">التطبيق الكامل</h2>
                    <div class="alert alert-warning">
                        <h5>لتشغيل التطبيق الكامل:</h5>
                        <ol class="text-start">
                            <li>تأكد من تثبيت المتطلبات: <code>pip install -r requirements.txt</code></li>
                            <li>شغل التطبيق الكامل: <code>python app.py</code></li>
                            <li>أو استخدم الملف التنفيذي: <code>تشغيل_التطبيق.bat</code></li>
                        </ol>
                    </div>
                    <a href="/" class="btn btn-primary">العودة للرئيسية</a>
                </div>
            </div>
        </body>
        </html>
        '''
    
    if __name__ == '__main__':
        print("🚀 تشغيل التطبيق التجريبي...")
        print("🌐 افتح المتصفح على: http://localhost:5000")
        
        with app.app_context():
            db.create_all()
            
            # إنشاء مدير تجريبي
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin'
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء حساب المدير التجريبي")
        
        app.run(debug=True, host='0.0.0.0', port=5000)

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n🔧 لحل هذه المشكلة:")
    print("1. تثبيت المتطلبات: pip install Flask Flask-SQLAlchemy Werkzeug")
    print("2. أو استخدم: python -m pip install Flask Flask-SQLAlchemy Werkzeug")
    input("\nاضغط Enter للخروج...")

except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")
    input("\nاضغط Enter للخروج...")
