{% extends "base.html" %}

{% block title %}لوحة تحكم المدير - نظام ربط محلات قطع غيار السيارات{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="fw-bold">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة تحكم المدير
            </h2>
            <p class="text-muted">مرحباً {{ session.username }}، إليك نظرة عامة على النظام</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ total_shops }}</h4>
                            <p class="mb-0">إجمالي المحلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-store fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ total_products }}</h4>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cog fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ total_users }}</h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ recent_shops|length }}</h4>
                            <p class="mb-0">محلات جديدة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('admin_add_shop') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة محل جديد
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('admin_shops') }}" class="btn btn-info w-100">
                                <i class="fas fa-store me-2"></i>
                                إدارة المحلات
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('admin_users') }}" class="btn btn-success w-100">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('admin_categories') }}" class="btn btn-warning w-100">
                                <i class="fas fa-tags me-2"></i>
                                إدارة الفئات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Shops -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث المحلات المسجلة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_shops %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المحل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop in recent_shops %}
                                <tr>
                                    <td>
                                        <strong>{{ shop.name }}</strong>
                                    </td>
                                    <td>{{ shop.email or '-' }}</td>
                                    <td>{{ shop.phone or '-' }}</td>
                                    <td>{{ shop.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if shop.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-danger">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('admin_edit_shop', shop_id=shop.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('shop_products', shop_id=shop.id) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-store fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد محلات مسجلة حالياً</h5>
                        <a href="{{ url_for('admin_add_shop') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول محل
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
