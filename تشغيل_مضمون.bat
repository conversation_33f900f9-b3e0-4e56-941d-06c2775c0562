@echo off
chcp 65001 >nul
title تطبيق قطع غيار السيارات - نسخة مضمونة

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █  🚗 تطبيق قطع غيار السيارات - النسخة المضمونة             █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 حمل Python من: https://python.org/downloads
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📦 تثبيت Flask فقط (المطلوب الوحيد)...
python -m pip install --quiet Flask

if errorlevel 1 (
    echo ⚠️ مشكلة في تثبيت Flask، جاري المحاولة بطريقة أخرى...
    python -m pip install --user Flask
)

echo ✅ تم تثبيت Flask
echo.

echo 🚀 تشغيل التطبيق المضمون...
echo.
echo ████████████████████████████████████████████████████████████████
echo █                      معلومات التطبيق                       █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🌐 الرابط: http://localhost:5000
echo.
echo 👤 الحسابات المتوفرة:
echo    🔑 المدير: admin / admin123
echo    🏪 المحل: shop1 / shop123
echo.
echo 📋 الميزات المتوفرة:
echo    ✅ صفحة رئيسية تفاعلية
echo    ✅ تسجيل دخول يعمل
echo    ✅ لوحة تحكم المدير
echo    ✅ لوحة تحكم المحل
echo    ✅ عرض المنتجات
echo    ✅ قاعدة بيانات SQLite
echo.
echo 🛑 لإيقاف التطبيق: اضغط Ctrl+C
echo ████████████████████████████████████████████████████████████████
echo.

python working_app.py

echo.
echo ✅ تم إغلاق التطبيق
pause
