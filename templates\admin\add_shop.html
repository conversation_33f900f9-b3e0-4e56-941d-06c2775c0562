{% extends "base.html" %}

{% block title %}إضافة محل جديد - لوحة تحكم المدير{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="fw-bold">
                <i class="fas fa-plus me-2"></i>
                إضافة محل جديد
            </h2>
            <p class="text-muted">إضافة محل جديد مع إنشاء حساب مستخدم</p>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        بيانات المحل والمستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- بيانات المستخدم -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    بيانات المستخدم
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">سيستخدم لتسجيل الدخول</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                        
                        <!-- بيانات المحل -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات المحل
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="shop_name" class="form-label">اسم المحل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="shop_name" name="shop_name" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+966 50 123 4567">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">وصف المحل</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="اكتب وصفاً مختصراً عن المحل..."></textarea>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2" placeholder="العنوان الكامل للمحل..."></textarea>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin_shops') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                إضافة المحل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});

// التحقق من طول كلمة المرور
document.getElementById('password').addEventListener('input', function() {
    if (this.value.length < 6 && this.value.length > 0) {
        this.setCustomValidity('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    } else {
        this.setCustomValidity('');
    }
});

// تنسيق رقم الهاتف
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    
    if (value.startsWith('966')) {
        value = '+' + value;
    } else if (value.startsWith('05')) {
        value = '+966' + value.substring(1);
    } else if (value.startsWith('5')) {
        value = '+966' + value;
    }
    
    this.value = value;
});
</script>
{% endblock %}
