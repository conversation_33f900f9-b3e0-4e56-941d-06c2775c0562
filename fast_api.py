#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API سريع لتطبيق قطع غيار السيارات
Fast API for Auto Parts Shop System
"""

from flask import Flask, request, jsonify
import sqlite3
import os

app = Flask(__name__)

# إضافة CORS headers يدوياً
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

def get_db_connection():
    """الحصول على اتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect('auto_parts_simple.db')
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def init_database():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL,
                name TEXT NOT NULL,
                shop_id INTEGER
            )
        ''')
        
        # جدول المحلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shops (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT NOT NULL,
                phone TEXT NOT NULL,
                email TEXT,
                address TEXT NOT NULL,
                city TEXT NOT NULL,
                specialty TEXT,
                rating REAL DEFAULT 0.0
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                part_number TEXT,
                category TEXT,
                brand TEXT,
                model TEXT
            )
        ''')
        
        # جدول تفاصيل المنتجات في المحلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shop_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shop_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                price REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                UNIQUE(shop_id, product_id)
            )
        ''')
        
        # إدراج البيانات الأولية إذا لم تكن موجودة
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            # إدراج المدير
            cursor.execute('''
                INSERT INTO users (username, password, role, name)
                VALUES (?, ?, ?, ?)
            ''', ('admin', 'admin123', 'admin', 'المدير'))
            
            # إدراج محل تجريبي
            cursor.execute('''
                INSERT INTO shops (name, description, phone, email, address, city, specialty, rating)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'محل الأصيل لقطع غيار السيارات',
                'محل متخصص في قطع غيار السيارات اليابانية والكورية',
                '+966501234567',
                '<EMAIL>',
                'شارع الملك فهد، الرياض',
                'الرياض',
                'قطع غيار يابانية',
                4.5
            ))
            
            shop_id = cursor.lastrowid
            
            # إدراج مستخدم المحل
            cursor.execute('''
                INSERT INTO users (username, password, role, name, shop_id)
                VALUES (?, ?, ?, ?, ?)
            ''', ('shop1', 'shop123', 'shop', 'محل الأصيل لقطع غيار السيارات', shop_id))
            
            # إدراج منتجات تجريبية
            products_data = [
                ('فلتر هواء تويوتا كامري', 'فلتر هواء أصلي لسيارة تويوتا كامري', 'TOY-AF-001', 'فلاتر', 'تويوتا', 'كامري'),
                ('تيل فرامل هوندا أكورد', 'تيل فرامل عالي الجودة لسيارة هوندا أكورد', 'HON-BP-002', 'فرامل', 'هوندا', 'أكورد'),
                ('زيت محرك موبيل 1', 'زيت محرك صناعي بالكامل', 'MOB-OIL-003', 'زيوت', 'موبيل', ''),
                ('بطارية نيسان التيما', 'بطارية أصلية لسيارة نيسان التيما', 'NIS-BAT-004', 'كهرباء', 'نيسان', 'التيما')
            ]
            
            prices = [85.0, 120.0, 95.0, 280.0]
            quantities = [15, 8, 25, 5]
            
            for i, product_data in enumerate(products_data):
                cursor.execute('''
                    INSERT INTO products (name, description, part_number, category, brand, model)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', product_data)
                
                product_id = cursor.lastrowid
                
                cursor.execute('''
                    INSERT INTO shop_products (shop_id, product_id, price, quantity)
                    VALUES (?, ?, ?, ?)
                ''', (shop_id, product_id, prices[i], quantities[i]))
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {e}")
        conn.rollback()
        conn.close()
        return False

# ===== API Routes =====

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return '''
    <h1>🚗 تطبيق قطع غيار السيارات - API سريع</h1>
    <p>قاعدة البيانات تعمل!</p>
    <ul>
        <li><a href="/static/app.html">التطبيق الكامل</a></li>
        <li><a href="/api/products">API المنتجات</a></li>
        <li><a href="/api/shops">API المحلات</a></li>
    </ul>
    '''

@app.route('/api/products', methods=['GET'])
def get_products():
    """الحصول على جميع المنتجات"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'خطأ في الاتصال بقاعدة البيانات'}), 500
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT p.*, sp.price, sp.quantity, s.name as shop_name, s.id as shop_id
            FROM products p
            JOIN shop_products sp ON p.id = sp.product_id
            JOIN shops s ON sp.shop_id = s.id
            ORDER BY p.id, s.name
        ''')
        
        products_dict = {}
        for row in cursor.fetchall():
            product_id = row['id']
            if product_id not in products_dict:
                products_dict[product_id] = {
                    'id': row['id'],
                    'name': row['name'],
                    'description': row['description'],
                    'partNumber': row['part_number'],
                    'category': row['category'],
                    'brand': row['brand'],
                    'model': row['model'],
                    'shopDetails': []
                }
            
            products_dict[product_id]['shopDetails'].append({
                'shopName': row['shop_name'],
                'shopId': row['shop_id'],
                'price': row['price'],
                'quantity': row['quantity']
            })
        
        products = list(products_dict.values())
        conn.close()
        return jsonify(products)
        
    except Exception as e:
        print(f"خطأ في تحميل المنتجات: {e}")
        conn.close()
        return jsonify({'error': f'خطأ في تحميل المنتجات: {str(e)}'}), 500

@app.route('/api/shops', methods=['GET'])
def get_shops():
    """الحصول على جميع المحلات"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'خطأ في الاتصال بقاعدة البيانات'}), 500
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.*, COUNT(sp.product_id) as products_count
            FROM shops s
            LEFT JOIN shop_products sp ON s.id = sp.shop_id
            GROUP BY s.id
            ORDER BY s.id
        ''')
        
        shops = []
        for row in cursor.fetchall():
            shop = {
                'id': row['id'],
                'name': row['name'],
                'description': row['description'],
                'phone': row['phone'],
                'email': row['email'],
                'address': row['address'],
                'city': row['city'],
                'specialty': row['specialty'],
                'rating': row['rating'],
                'products_count': row['products_count'],
                'joinDate': '2024-01-01'
            }
            shops.append(shop)
        
        conn.close()
        return jsonify(shops)
        
    except Exception as e:
        print(f"خطأ في تحميل المحلات: {e}")
        conn.close()
        return jsonify({'error': f'خطأ في تحميل المحلات: {str(e)}'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    """تسجيل الدخول"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': 'لا توجد بيانات'}), 400
    
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': 'اسم المستخدم وكلمة المرور مطلوبان'})
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': 'خطأ في الاتصال بقاعدة البيانات'}), 500
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT u.*, s.name as shop_name 
            FROM users u 
            LEFT JOIN shops s ON u.shop_id = s.id 
            WHERE u.username = ? AND u.password = ?
        ''', (username, password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            user_data = {
                'id': user['id'],
                'username': user['username'],
                'role': user['role'],
                'name': user['shop_name'] if user['role'] == 'shop' else user['name'],
                'shop_id': user['shop_id']
            }
            return jsonify({'success': True, 'user': user_data})
        else:
            return jsonify({'success': False, 'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'})
            
    except Exception as e:
        print(f"خطأ في تسجيل الدخول: {e}")
        conn.close()
        return jsonify({'success': False, 'message': f'خطأ في تسجيل الدخول: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 تشغيل API سريع...")
    
    # إنشاء قاعدة البيانات
    if init_database():
        print("✅ قاعدة البيانات جاهزة!")
    else:
        print("❌ خطأ في إنشاء قاعدة البيانات!")
    
    print("🌐 الروابط:")
    print("- التطبيق: http://localhost:5000/static/app.html")
    print("- API: http://localhost:5000/api/products")
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
