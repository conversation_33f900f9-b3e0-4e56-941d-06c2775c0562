#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 اختبار Python...")

try:
    import sys
    print(f"✅ Python {sys.version}")
    
    print("🔍 اختبار Flask...")
    from flask import Flask
    print("✅ Flask متوفر")
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return '''
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار التطبيق</title>
            <style>
                body { font-family: Arial; text-align: center; padding: 50px; background: #f0f0f0; }
                .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .success { color: #28a745; font-size: 24px; margin: 20px 0; }
                .info { color: #007bff; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚗 تطبيق قطع غيار السيارات</h1>
                <div class="success">✅ التطبيق يعمل بنجاح!</div>
                <div class="info">🌐 الرابط: http://localhost:5000</div>
                <div class="info">👤 المدير: admin / admin123</div>
                <div class="info">🏪 المحل: shop1 / shop123</div>
                <hr>
                <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن:</p>
                <ul style="text-align: right; display: inline-block;">
                    <li>Python مثبت بشكل صحيح</li>
                    <li>Flask يعمل بشكل طبيعي</li>
                    <li>الخادم يعمل على المنفذ 5000</li>
                </ul>
                <hr>
                <p><strong>الخطوة التالية:</strong> تشغيل التطبيق الكامل</p>
                <p>استخدم الأمر: <code>python app.py</code></p>
            </div>
        </body>
        </html>
        '''
    
    print("🚀 تشغيل الخادم...")
    print("🌐 افتح المتصفح على: http://localhost:5000")
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    print("-" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("🔧 قم بتثبيت Flask: pip install Flask")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    
finally:
    input("\nاضغط Enter للخروج...")
