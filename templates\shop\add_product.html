{% extends "base.html" %}

{% block title %}إضافة منتج جديد - {{ shop.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="fw-bold">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </h2>
            <p class="text-muted">إضافة منتج جديد لمحل {{ shop.name }}</p>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        بيانات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="part_number" class="form-label">رقم القطعة</label>
                                <input type="text" class="form-control" id="part_number" name="part_number">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="اكتب وصفاً مفصلاً للمنتج..."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="price" class="form-label">السعر (ريال) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="0" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="brand_id" class="form-label">الماركة</label>
                                <select class="form-select" id="brand_id" name="brand_id">
                                    <option value="">اختر الماركة</option>
                                    {% for brand in brands %}
                                    <option value="{{ brand.id }}">{{ brand.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="car_model_id" class="form-label">موديل السيارة</label>
                                <select class="form-select" id="car_model_id" name="car_model_id">
                                    <option value="">اختر الموديل</option>
                                    {% for model in car_models %}
                                    <option value="{{ model.id }}">{{ model.brand.name }} {{ model.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="image_url" class="form-label">رابط الصورة</label>
                            <input type="url" class="form-control" id="image_url" name="image_url" placeholder="https://example.com/image.jpg">
                            <div class="form-text">يمكنك إضافة رابط صورة المنتج (اختياري)</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('shop_products_manage') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ المنتج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث قائمة الموديلات عند تغيير الماركة
document.getElementById('brand_id').addEventListener('change', function() {
    const brandId = this.value;
    const modelSelect = document.getElementById('car_model_id');
    
    // مسح الخيارات الحالية
    modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
    
    if (brandId) {
        // هنا يمكن إضافة AJAX لجلب الموديلات المرتبطة بالماركة
        // لكن للبساطة سنعرض جميع الموديلات
        {% for model in car_models %}
        if ({{ model.brand_id }} == brandId) {
            const option = document.createElement('option');
            option.value = {{ model.id }};
            option.textContent = '{{ model.name }}';
            modelSelect.appendChild(option);
        }
        {% endfor %}
    }
});

// معاينة الصورة
document.getElementById('image_url').addEventListener('input', function() {
    const url = this.value;
    const preview = document.getElementById('image_preview');
    
    if (preview) {
        preview.remove();
    }
    
    if (url) {
        const img = document.createElement('img');
        img.id = 'image_preview';
        img.src = url;
        img.className = 'img-thumbnail mt-2';
        img.style.maxWidth = '200px';
        img.style.maxHeight = '200px';
        
        img.onerror = function() {
            this.remove();
        };
        
        this.parentNode.appendChild(img);
    }
});
</script>
{% endblock %}
