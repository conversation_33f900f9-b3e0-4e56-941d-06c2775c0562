from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime
import os
import secrets

app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///auto_parts.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='shop')  # admin, shop
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Shop(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    rating = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref=db.backref('shop', uselist=False))

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)

class Brand(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)

class CarModel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    brand_id = db.Column(db.Integer, db.ForeignKey('brand.id'), nullable=False)
    year_from = db.Column(db.Integer)
    year_to = db.Column(db.Integer)
    
    brand = db.relationship('Brand', backref='models')

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    part_number = db.Column(db.String(100))
    price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, default=0)
    image_url = db.Column(db.String(200))
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    brand_id = db.Column(db.Integer, db.ForeignKey('brand.id'))
    car_model_id = db.Column(db.Integer, db.ForeignKey('car_model.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    shop = db.relationship('Shop', backref='products')
    category = db.relationship('Category', backref='products')
    brand = db.relationship('Brand', backref='products')
    car_model = db.relationship('CarModel', backref='products')

class Review(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5
    comment = db.Column(db.Text)
    reviewer_name = db.Column(db.String(100), nullable=False)
    reviewer_email = db.Column(db.String(120))
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'))
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    product = db.relationship('Product', backref='reviews')
    shop = db.relationship('Shop', backref='reviews')

# دوال مساعدة
def login_required(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        user = User.query.get(session['user_id'])
        if not user or user.role != 'admin':
            flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

def shop_required(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        user = User.query.get(session['user_id'])
        if not user or user.role not in ['admin', 'shop']:
            flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# الصفحة الرئيسية
@app.route('/')
def index():
    # أحدث المنتجات
    latest_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(12).all()
    
    # أفضل المحلات المقيمة
    top_shops = Shop.query.filter_by(is_active=True).order_by(Shop.rating.desc()).limit(6).all()
    
    return render_template('index.html', 
                         latest_products=latest_products, 
                         top_shops=top_shops)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username, is_active=True).first()
        
        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            
            if user.role == 'admin':
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('shop_dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('index'))

# البحث
@app.route('/search')
def search():
    query = request.args.get('q', '')
    brand = request.args.get('brand', '')
    part_number = request.args.get('part_number', '')
    category = request.args.get('category', '')

    products = Product.query.filter_by(is_active=True)

    if query:
        products = products.filter(Product.name.contains(query))

    if brand:
        products = products.join(Brand).filter(Brand.name.ilike(f'%{brand}%'))

    if part_number:
        products = products.filter(Product.part_number.contains(part_number))

    if category:
        products = products.join(Category).filter(Category.name.ilike(f'%{category}%'))

    products = products.order_by(Product.created_at.desc()).all()

    # الحصول على البيانات للفلاتر
    brands = Brand.query.all()
    categories = Category.query.all()

    return render_template('search.html',
                         products=products,
                         brands=brands,
                         categories=categories,
                         query=query,
                         selected_brand=brand,
                         selected_category=category,
                         part_number=part_number)

# المحلات
@app.route('/shops')
def shops():
    shops = Shop.query.filter_by(is_active=True).order_by(Shop.rating.desc()).all()
    return render_template('shops.html', shops=shops)

# منتجات محل معين
@app.route('/shop/<int:shop_id>')
def shop_products(shop_id):
    shop = Shop.query.get_or_404(shop_id)
    products = Product.query.filter_by(shop_id=shop_id, is_active=True).order_by(Product.created_at.desc()).all()
    return render_template('shop_products.html', shop=shop, products=products)

# لوحة تحكم المدير
@app.route('/admin')
@admin_required
def admin_dashboard():
    total_shops = Shop.query.count()
    total_products = Product.query.count()
    total_users = User.query.count()
    recent_shops = Shop.query.order_by(Shop.created_at.desc()).limit(5).all()

    return render_template('admin/dashboard.html',
                         total_shops=total_shops,
                         total_products=total_products,
                         total_users=total_users,
                         recent_shops=recent_shops)

# إدارة المحلات - المدير
@app.route('/admin/shops')
@admin_required
def admin_shops():
    shops = Shop.query.order_by(Shop.created_at.desc()).all()
    return render_template('admin/shops.html', shops=shops)

# إضافة محل جديد
@app.route('/admin/shops/add', methods=['GET', 'POST'])
@admin_required
def admin_add_shop():
    if request.method == 'POST':
        # إنشاء مستخدم جديد للمحل
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']

        # التحقق من عدم وجود المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('admin/add_shop.html')

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('admin/add_shop.html')

        # إنشاء المستخدم
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            role='shop'
        )
        db.session.add(user)
        db.session.flush()  # للحصول على ID المستخدم

        # إنشاء المحل
        shop = Shop(
            name=request.form['shop_name'],
            description=request.form['description'],
            address=request.form['address'],
            phone=request.form['phone'],
            email=email,
            user_id=user.id
        )
        db.session.add(shop)
        db.session.commit()

        flash('تم إضافة المحل بنجاح', 'success')
        return redirect(url_for('admin_shops'))

    return render_template('admin/add_shop.html')

# لوحة تحكم المحل
@app.route('/shop')
@shop_required
def shop_dashboard():
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    total_products = Product.query.filter_by(shop_id=shop.id).count()
    active_products = Product.query.filter_by(shop_id=shop.id, is_active=True).count()
    recent_products = Product.query.filter_by(shop_id=shop.id).order_by(Product.created_at.desc()).limit(5).all()

    return render_template('shop/dashboard.html',
                         shop=shop,
                         total_products=total_products,
                         active_products=active_products,
                         recent_products=recent_products)

# منتجات المحل
@app.route('/shop/products')
@shop_required
def shop_products_manage():
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    products = Product.query.filter_by(shop_id=shop.id).order_by(Product.created_at.desc()).all()
    return render_template('shop/products.html', shop=shop, products=products)

# إضافة منتج جديد
@app.route('/shop/products/add', methods=['GET', 'POST'])
@shop_required
def shop_add_product():
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        product = Product(
            name=request.form['name'],
            description=request.form['description'],
            part_number=request.form['part_number'],
            price=float(request.form['price']),
            quantity=int(request.form['quantity']),
            shop_id=shop.id,
            category_id=request.form['category_id'] if request.form['category_id'] else None,
            brand_id=request.form['brand_id'] if request.form['brand_id'] else None,
            car_model_id=request.form['car_model_id'] if request.form['car_model_id'] else None
        )

        db.session.add(product)
        db.session.commit()

        flash('تم إضافة المنتج بنجاح', 'success')
        return redirect(url_for('shop_products_manage'))

    categories = Category.query.all()
    brands = Brand.query.all()
    car_models = CarModel.query.all()

    return render_template('shop/add_product.html',
                         shop=shop,
                         categories=categories,
                         brands=brands,
                         car_models=car_models)

# تعديل منتج
@app.route('/shop/products/edit/<int:product_id>', methods=['GET', 'POST'])
@shop_required
def shop_edit_product(product_id):
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    product = Product.query.filter_by(id=product_id, shop_id=shop.id).first_or_404()

    if request.method == 'POST':
        product.name = request.form['name']
        product.description = request.form['description']
        product.part_number = request.form['part_number']
        product.price = float(request.form['price'])
        product.quantity = int(request.form['quantity'])
        product.category_id = request.form['category_id'] if request.form['category_id'] else None
        product.brand_id = request.form['brand_id'] if request.form['brand_id'] else None
        product.car_model_id = request.form['car_model_id'] if request.form['car_model_id'] else None
        product.updated_at = datetime.utcnow()

        db.session.commit()

        flash('تم تحديث المنتج بنجاح', 'success')
        return redirect(url_for('shop_products_manage'))

    categories = Category.query.all()
    brands = Brand.query.all()
    car_models = CarModel.query.all()

    return render_template('shop/edit_product.html',
                         shop=shop,
                         product=product,
                         categories=categories,
                         brands=brands,
                         car_models=car_models)

# حذف منتج
@app.route('/shop/products/delete/<int:product_id>', methods=['POST'])
@shop_required
def shop_delete_product(product_id):
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    product = Product.query.filter_by(id=product_id, shop_id=shop.id).first_or_404()

    db.session.delete(product)
    db.session.commit()

    flash('تم حذف المنتج بنجاح', 'success')
    return redirect(url_for('shop_products_manage'))

# إدارة المستخدمين - المدير
@app.route('/admin/users')
@admin_required
def admin_users():
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('admin/users.html', users=users)

# إدارة الفئات - المدير
@app.route('/admin/categories')
@admin_required
def admin_categories():
    categories = Category.query.all()
    brands = Brand.query.all()
    return render_template('admin/categories.html', categories=categories, brands=brands)

# تعديل محل - المدير
@app.route('/admin/shops/edit/<int:shop_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_shop(shop_id):
    shop = Shop.query.get_or_404(shop_id)

    if request.method == 'POST':
        shop.name = request.form['shop_name']
        shop.description = request.form['description']
        shop.address = request.form['address']
        shop.phone = request.form['phone']
        shop.email = request.form['email']
        shop.is_active = 'is_active' in request.form

        db.session.commit()
        flash('تم تحديث بيانات المحل بنجاح', 'success')
        return redirect(url_for('admin_shops'))

    return render_template('admin/edit_shop.html', shop=shop)

# تعديل بيانات المحل
@app.route('/shop/profile', methods=['GET', 'POST'])
@shop_required
def shop_profile():
    user = User.query.get(session['user_id'])
    shop = user.shop

    if not shop:
        flash('لم يتم العثور على بيانات المحل', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        shop.name = request.form['shop_name']
        shop.description = request.form['description']
        shop.address = request.form['address']
        shop.phone = request.form['phone']
        shop.email = request.form['email']

        db.session.commit()
        flash('تم تحديث بيانات المحل بنجاح', 'success')
        return redirect(url_for('shop_dashboard'))

    return render_template('shop/profile.html', shop=shop)

# صفحة منتجات محل معين
@app.route('/shop/<int:shop_id>/products')
def shop_products(shop_id):
    shop = Shop.query.get_or_404(shop_id)
    products = Product.query.filter_by(shop_id=shop_id, is_active=True).order_by(Product.created_at.desc()).all()
    return render_template('shop_products.html', shop=shop, products=products)

# إضافة تقييم
@app.route('/review/add', methods=['POST'])
def add_review():
    rating = int(request.form['rating'])
    comment = request.form['comment']
    reviewer_name = request.form['reviewer_name']
    reviewer_email = request.form.get('reviewer_email', '')
    shop_id = request.form.get('shop_id')
    product_id = request.form.get('product_id')

    review = Review(
        rating=rating,
        comment=comment,
        reviewer_name=reviewer_name,
        reviewer_email=reviewer_email,
        shop_id=shop_id if shop_id else None,
        product_id=product_id if product_id else None
    )

    db.session.add(review)

    # تحديث تقييم المحل
    if shop_id:
        shop = Shop.query.get(shop_id)
        if shop:
            reviews = Review.query.filter_by(shop_id=shop_id).all()
            if reviews:
                shop.rating = sum(r.rating for r in reviews) / len(reviews)
                db.session.commit()

    flash('تم إضافة التقييم بنجاح', 'success')

    if shop_id:
        return redirect(url_for('shop_products', shop_id=shop_id))
    else:
        return redirect(url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # إنشاء مدير افتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء حساب المدير: admin / admin123")

        # إنشاء بيانات تجريبية
        if Brand.query.count() == 0:
            brands = ['تويوتا', 'هوندا', 'نيسان', 'هيونداي', 'كيا', 'مازدا', 'ميتسوبيشي']
            for brand_name in brands:
                brand = Brand(name=brand_name)
                db.session.add(brand)

            categories = ['فرامل', 'محرك', 'تكييف', 'كهرباء', 'عجلات', 'زيوت', 'فلاتر']
            for cat_name in categories:
                category = Category(name=cat_name, description=f'قطع غيار {cat_name}')
                db.session.add(category)

            db.session.commit()
            print("تم إنشاء البيانات التجريبية")

    app.run(debug=True, host='0.0.0.0', port=5000)
