<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق قطع غيار السيارات - يعمل 100%</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
        }
        
        .product-card {
            transition: transform 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات - يعمل 100%
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="resetData()" title="إعادة تعيين البيانات">
                    <i class="fas fa-redo text-warning"></i>
                </a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showPage('dashboard')" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section text-center" id="heroSection">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
            <p class="lead mb-4">تطبيق يعمل 100% مع جميع الوظائف!</p>
            <div class="alert alert-light d-inline-block">
                <h5><i class="fas fa-check-circle me-2 text-success"></i>يعمل بشكل مثالي</h5>
                <p class="mb-0">جميع الوظائف تعمل - إضافة، تعديل، حذف، حفظ دائم</p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Home Page -->
        <div id="home" class="page-content active">
            <!-- Search Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث في المنتجات
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Search Instructions -->
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-info-circle me-2"></i>طريقة البحث</h6>
                        <p class="mb-0 small">
                            <strong>ملاحظة:</strong> كل نوع بحث يعمل منفصلاً - استخدم نوع واحد فقط في كل مرة للحصول على أفضل النتائج
                        </p>
                    </div>

                    <!-- First Row - Text Search and Category -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن منتج..." onkeyup="clearOtherSearches('text'); performSearch()">
                                <label for="searchInput">🔍 البحث النصي (اسم، وصف)</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="partNumberSearch" placeholder="ابحث برقم القطعة..." onkeyup="clearOtherSearches('partNumber'); performSearch()">
                                <label for="partNumberSearch">🏷️ رقم القطعة / كود الصنف</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="categoryFilter" onchange="clearOtherSearches('category'); performSearch()">
                                    <option value="">جميع الفئات</option>
                                </select>
                                <label for="categoryFilter">📂 فئة القطعة</label>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row - Car Information -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="carBrandSearch" onchange="clearOtherSearches('car'); updateSearchCarModels(); performSearch()">
                                    <option value="">جميع أنواع السيارات</option>
                                </select>
                                <label for="carBrandSearch">🚗 نوع السيارة</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="carModelSearch" onchange="clearOtherSearches('car'); performSearch()">
                                    <option value="">جميع الماركات</option>
                                </select>
                                <label for="carModelSearch">🏷️ ماركة السيارة</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="carYearSearch" onchange="clearOtherSearches('car'); performSearch()">
                                    <option value="">جميع الموديلات</option>
                                </select>
                                <label for="carYearSearch">📅 موديل السيارة (السنة)</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group w-100 h-100" role="group">
                                <button class="btn btn-primary" onclick="performSearch()" title="بحث">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearAllSearches()" title="مسح جميع المرشحات">
                                    <i class="fas fa-times me-1"></i>
                                    مسح الكل
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <span id="searchResults" class="text-muted small"></span>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-info btn-sm me-1" onclick="showAllCategories()">
                                <i class="fas fa-tags me-1"></i>
                                الفئات
                            </button>
                            <button class="btn btn-outline-success btn-sm me-1" onclick="showAllBrands()">
                                <i class="fas fa-car me-1"></i>
                                الماركات
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="showAllModels()">
                                <i class="fas fa-cogs me-1"></i>
                                الموديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Overview -->
            <div class="card mb-4" id="categoriesCard">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        الفئات المتوفرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="categoriesContainer">
                        <!-- الفئات ستظهر هنا -->
                    </div>
                </div>
            </div>

            <!-- Brands Overview -->
            <div class="card mb-4" id="brandsCard" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-car me-2"></i>
                        ماركات السيارات المتوفرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="brandsContainer">
                        <!-- الماركات ستظهر هنا -->
                    </div>
                </div>
            </div>

            <!-- Models Overview -->
            <div class="card mb-4" id="modelsCard" style="display: none;">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        موديلات السيارات المتوفرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modelsContainer">
                        <!-- الموديلات ستظهر هنا -->
                    </div>
                </div>
            </div>

            <!-- Products Table -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول قطع الغيار والمحلات
                    </h5>
                    <div>
                        <span class="badge bg-success me-2" id="dataStatus">
                            <i class="fas fa-save me-1"></i>
                            محفوظ
                        </span>
                        <span class="badge bg-info me-2" id="lastUpdate">
                            <i class="fas fa-clock me-1"></i>
                            <span id="updateTime">الآن</span>
                        </span>
                        <button class="btn btn-light btn-sm" onclick="forceRefreshData()" title="تحديث فوري للبيانات">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث فوري
                        </button>
                        <button class="btn btn-outline-primary btn-sm ms-1" onclick="autoRefreshData()" title="تحديث تلقائي">
                            <i class="fas fa-refresh me-1"></i>
                            تحديث تلقائي
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم القطعة</th>
                                    <th>رقم القطعة</th>
                                    <th>نوع السيارة</th>
                                    <th>ماركة السيارة</th>
                                    <th>موديل السيارة</th>
                                    <th>المحلات والأسعار</th>
                                    <th>أقل سعر</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- البيانات ستحمل هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white text-center">
                        <div class="card-body">
                            <h4 id="totalProducts">0</h4>
                            <p class="mb-0">إجمالي القطع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <h4 id="totalShops">0</h4>
                            <p class="mb-0">إجمالي المحلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <h4 id="totalQuantity">0</h4>
                            <p class="mb-0">إجمالي الكميات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white text-center">
                        <div class="card-body">
                            <h4 id="averagePrice">0 ريال</h4>
                            <p class="mb-0">متوسط الأسعار</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shops Page -->
        <div id="shops" class="page-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">جميع المحلات</h2>
                <div>
                    <button class="btn btn-success" onclick="showAddShopModal()" id="addShopBtn">
                        <i class="fas fa-plus me-2"></i>
                        إضافة محل جديد
                    </button>
                    <button class="btn btn-outline-info ms-2" onclick="loadShops()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                </div>
            </div>
            
            <div class="row" id="shopsContainer">
                <!-- المحلات ستظهر هنا -->
            </div>
        </div>

        <!-- Login Page -->
        <div id="login" class="page-content">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="loginForm">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                                    <label for="username">اسم المستخدم</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                                    <label for="password">كلمة المرور</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    دخول
                                </button>
                                
                                <div id="loginAlert" class="alert" style="display: none;"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboard" class="page-content">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h2 class="mb-3" id="dashboardTitle">لوحة التحكم</h2>
                            <p class="lead mb-0" id="dashboardMessage">مرحباً بك</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardProducts">0</h3>
                            <p class="mb-0">المنتجات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardShops">0</h3>
                            <p class="mb-0">المحلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardValue">0</h3>
                            <p class="mb-0">إجمالي القيمة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">إدارة المنتجات</h5>
                    <button class="btn btn-success" onclick="showAddProductModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </button>
                </div>
                <div class="card-body">
                    <div class="row" id="dashboardProductsContainer">
                        <!-- منتجات المحل ستظهر هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modalsContainer"></div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentUser = null;
        let nextShopId = 2;
        let nextProductId = 5;

        // Default data
        const defaultUsers = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل لقطع غيار السيارات', shopId: 1 }
        };

        const defaultShops = [
            {
                id: 1,
                name: 'محل الأصيل لقطع غيار السيارات',
                description: 'محل متخصص في قطع غيار السيارات اليابانية والكورية',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'شارع الملك فهد، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار يابانية',
                rating: 4.5,
                joinDate: '2024-01-01'
            }
        ];

        const defaultProducts = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري 2015',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015',
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري 2015',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 85.0, quantity: 15 }]
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد 2018',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد موديل 2018',
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد 2018',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 120.0, quantity: 8 }]
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات',
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: 'موبيل',
                model: 'عام',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 95.0, quantity: 25 }]
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما 2020',
                description: 'بطارية أصلية لسيارة نيسان التيما موديل 2020',
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما 2020',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 280.0, quantity: 5 }]
            },
            {
                id: 5,
                name: 'فلتر زيت تويوتا يارس 2017',
                description: 'فلتر زيت أصلي لسيارة تويوتا يارس موديل 2017',
                partNumber: 'TOY-OF-005',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'يارس 2017',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 35.0, quantity: 20 }]
            },
            {
                id: 6,
                name: 'إطار ميشلان هوندا سيفيك 2019',
                description: 'إطار ميشلان مقاس 195/65R15 لهوندا سيفيك 2019',
                partNumber: 'MIC-TIR-006',
                category: 'إطارات',
                brand: 'هوندا',
                model: 'سيفيك 2019',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 320.0, quantity: 12 }]
            }
        ];

        // Working data (loaded from localStorage or defaults)
        let users = {};
        let shops = [];
        let products = [];

        // LocalStorage functions
        function saveToStorage() {
            try {
                localStorage.setItem('autoparts_users', JSON.stringify(users));
                localStorage.setItem('autoparts_shops', JSON.stringify(shops));
                localStorage.setItem('autoparts_products', JSON.stringify(products));
                localStorage.setItem('autoparts_nextShopId', nextShopId.toString());
                localStorage.setItem('autoparts_nextProductId', nextProductId.toString());

                // Update status indicator
                const statusElement = document.getElementById('dataStatus');
                if (statusElement) {
                    statusElement.className = 'badge bg-success me-2';
                    statusElement.innerHTML = '<i class="fas fa-save me-1"></i>محفوظ';
                }

                // Update last update time
                updateLastUpdateTime();

                console.log('✅ تم حفظ البيانات في localStorage');
            } catch (error) {
                console.error('❌ خطأ في حفظ البيانات:', error);
                showNotification('خطأ في حفظ البيانات', 'warning');
            }
        }

        function loadFromStorage() {
            try {
                const savedUsers = localStorage.getItem('autoparts_users');
                const savedShops = localStorage.getItem('autoparts_shops');
                const savedProducts = localStorage.getItem('autoparts_products');
                const savedNextShopId = localStorage.getItem('autoparts_nextShopId');
                const savedNextProductId = localStorage.getItem('autoparts_nextProductId');

                if (savedUsers && savedShops && savedProducts) {
                    users = JSON.parse(savedUsers);
                    shops = JSON.parse(savedShops);
                    products = JSON.parse(savedProducts);
                    nextShopId = savedNextShopId ? parseInt(savedNextShopId) : 2;
                    nextProductId = savedNextProductId ? parseInt(savedNextProductId) : 5;
                    console.log('✅ تم تحميل البيانات من localStorage');
                    return true;
                } else {
                    console.log('📝 لا توجد بيانات محفوظة، استخدام البيانات الافتراضية');
                    return false;
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                return false;
            }
        }

        function initializeData() {
            const dataLoaded = loadFromStorage();

            if (!dataLoaded) {
                // Use default data
                users = { ...defaultUsers };
                shops = [...defaultShops];
                products = [...defaultProducts];

                // Save initial data
                saveToStorage();
                console.log('💾 تم حفظ البيانات الافتراضية');
            }
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم فقدان جميع التغييرات!')) {
                localStorage.removeItem('autoparts_users');
                localStorage.removeItem('autoparts_shops');
                localStorage.removeItem('autoparts_products');
                localStorage.removeItem('autoparts_nextShopId');
                localStorage.removeItem('autoparts_nextProductId');

                // Reset to defaults
                users = { ...defaultUsers };
                shops = [...defaultShops];
                products = [...defaultProducts];
                nextShopId = 2;
                nextProductId = 5;

                saveToStorage();
                showNotification('تم إعادة تعيين البيانات بنجاح!', 'success');

                // Refresh current page
                if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                } else if (document.getElementById('shops').classList.contains('active')) {
                    loadShops();
                } else if (document.getElementById('dashboard').classList.contains('active')) {
                    loadDashboard();
                }
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            container.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide notification after 4 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Show page
        function showPage(pageId) {
            console.log(`📄 تغيير الصفحة إلى: ${pageId}`);

            // Hide hero section for other pages
            const heroSection = document.getElementById('heroSection');
            if (pageId !== 'home') {
                heroSection.style.display = 'none';
            } else {
                heroSection.style.display = 'block';
            }

            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Refresh data from localStorage before loading content
            loadFromStorage();

            // Load content based on page with immediate refresh
            setTimeout(() => {
                if (pageId === 'home') {
                    loadData();
                    console.log('🏠 تم تحديث وتحميل الصفحة الرئيسية');
                } else if (pageId === 'shops') {
                    loadShops();
                    console.log('🏪 تم تحديث وتحميل صفحة المحلات');
                } else if (pageId === 'dashboard') {
                    loadDashboard();
                    console.log('📊 تم تحديث وتحميل لوحة التحكم');
                }
            }, 50);
        }

        // Load data
        function loadData() {
            console.log('📊 تحميل البيانات...');
            displayProducts(products);
            loadCategories();
            loadBrands();
            loadModels();
            loadSearchCarBrands();
            loadSearchCarYears();
            updateSummaryCards();

            // Clear any existing search
            clearAllSearches();

            showNotification('تم تحميل البيانات بنجاح!', 'success');
            console.log('✅ تم تحميل البيانات بنجاح');
        }

        // Display products (with optional filtering)
        function displayProducts(productsToShow) {
            const tableBody = document.getElementById('productsTableBody');
            tableBody.innerHTML = '';

            if (productsToShow.length === 0) {
                const isSearchActive = document.getElementById('searchInput').value.trim() ||
                                     document.getElementById('categoryFilter').value ||
                                     (document.getElementById('partNumberSearch') && document.getElementById('partNumberSearch').value.trim()) ||
                                     (document.getElementById('carBrandSearch') && document.getElementById('carBrandSearch').value) ||
                                     (document.getElementById('carModelSearch') && document.getElementById('carModelSearch').value) ||
                                     (document.getElementById('carYearSearch') && document.getElementById('carYearSearch').value);

                tableBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="alert ${isSearchActive ? 'alert-warning' : 'alert-info'} mb-0">
                                <i class="fas fa-${isSearchActive ? 'search' : 'info-circle'} me-2"></i>
                                ${isSearchActive ? 'لم يتم العثور على منتجات تطابق البحث' : 'لا توجد منتجات'}
                                ${isSearchActive ? `
                                    <br>
                                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="clearSearch()">
                                        <i class="fas fa-times me-1"></i>
                                        مسح البحث
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            productsToShow.forEach(product => {
                const prices = product.shopDetails.map(shop => shop.price);
                const minPrice = Math.min(...prices);
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);

                const shopsList = product.shopDetails.map(shopDetail => {
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                // Extract car information from product
                const carBrand = product.brand || '-';
                let carModel = '-';
                let carYear = '-';

                if (product.model && product.model.trim()) {
                    const modelText = product.model.trim();
                    // Try to extract year from model
                    const yearMatch = modelText.match(/\d{4}/);
                    if (yearMatch) {
                        carYear = yearMatch[0];
                        carModel = modelText.replace(/\s*\d{4}\s*/, '').trim() || '-';
                    } else {
                        carModel = modelText;
                    }
                }

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div class="text-center">
                                ${carBrand !== '-' ? `
                                    <span class="badge bg-primary">${carBrand}</span>
                                ` : `
                                    <span class="text-muted">-</span>
                                `}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                ${carModel !== '-' ? `
                                    <span class="badge bg-info">${carModel}</span>
                                ` : `
                                    <span class="text-muted">-</span>
                                `}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                ${carYear !== '-' ? `
                                    <span class="badge bg-warning text-dark">${carYear}</span>
                                ` : `
                                    <span class="text-muted">-</span>
                                `}
                            </div>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل |
                                إجمالي الكمية: <span class="fw-bold">${totalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${currentUser && currentUser.role === 'admin' ? `
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct(${product.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // Load categories
        function loadCategories() {
            const categoryFilter = document.getElementById('categoryFilter');
            const categoriesContainer = document.getElementById('categoriesContainer');

            // Get unique categories
            const categories = {};
            products.forEach(product => {
                if (product.category) {
                    if (!categories[product.category]) {
                        categories[product.category] = {
                            name: product.category,
                            count: 0,
                            totalQuantity: 0,
                            avgPrice: 0,
                            products: []
                        };
                    }
                    categories[product.category].count++;
                    categories[product.category].products.push(product);

                    // Calculate total quantity and average price
                    product.shopDetails.forEach(shop => {
                        categories[product.category].totalQuantity += shop.quantity;
                        categories[product.category].avgPrice += shop.price;
                    });
                }
            });

            // Calculate average prices
            Object.keys(categories).forEach(categoryName => {
                const category = categories[categoryName];
                const totalPrices = category.products.reduce((sum, product) => {
                    return sum + product.shopDetails.reduce((pSum, shop) => pSum + shop.price, 0);
                }, 0);
                const totalShops = category.products.reduce((sum, product) => sum + product.shopDetails.length, 0);
                category.avgPrice = totalShops > 0 ? Math.round(totalPrices / totalShops) : 0;
            });

            // Update category filter dropdown
            categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';
            Object.keys(categories).sort().forEach(categoryName => {
                const option = document.createElement('option');
                option.value = categoryName;
                option.textContent = `${categoryName} (${categories[categoryName].count})`;
                categoryFilter.appendChild(option);
            });

            // Update categories display
            categoriesContainer.innerHTML = '';
            if (Object.keys(categories).length === 0) {
                categoriesContainer.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد فئات محددة للمنتجات
                        </div>
                    </div>
                `;
                return;
            }

            Object.keys(categories).sort().forEach(categoryName => {
                const category = categories[categoryName];
                const categoryCard = `
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="card product-card h-100" style="cursor: pointer;" onclick="filterByCategory('${categoryName}')">
                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <i class="fas fa-tag fa-2x text-primary"></i>
                                </div>
                                <h6 class="card-title text-primary">${categoryName}</h6>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">المنتجات</small>
                                        <div class="fw-bold text-success">${category.count}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">الكمية</small>
                                        <div class="fw-bold text-info">${category.totalQuantity}</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">متوسط السعر</small>
                                    <div class="fw-bold text-warning">${category.avgPrice} ريال</div>
                                </div>
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); filterByCategory('${categoryName}')">
                                        <i class="fas fa-search me-1"></i>
                                        عرض المنتجات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                categoriesContainer.innerHTML += categoryCard;
            });
        }

        // Load brands
        function loadBrands() {
            const brandFilter = document.getElementById('brandFilter');

            // Get unique brands
            const brands = {};
            products.forEach(product => {
                if (product.brand && product.brand.trim()) {
                    const brandName = product.brand.trim();
                    if (!brands[brandName]) {
                        brands[brandName] = {
                            name: brandName,
                            count: 0,
                            models: new Set()
                        };
                    }
                    brands[brandName].count++;
                    if (product.model && product.model.trim()) {
                        brands[brandName].models.add(product.model.trim());
                    }
                }
            });

            // Update brand filter dropdown
            brandFilter.innerHTML = '<option value="">جميع الماركات</option>';
            Object.keys(brands).sort().forEach(brandName => {
                const brand = brands[brandName];
                const option = document.createElement('option');
                option.value = brandName;
                option.textContent = `${brandName} (${brand.count})`;
                brandFilter.appendChild(option);
            });
        }

        // Load models based on selected brand
        function loadModels() {
            const brandFilter = document.getElementById('brandFilter');
            const modelFilter = document.getElementById('modelFilter');
            const selectedBrand = brandFilter.value;

            // Clear models
            modelFilter.innerHTML = '<option value="">جميع الموديلات</option>';

            if (!selectedBrand) {
                // If no brand selected, show all models
                const allModels = new Set();
                products.forEach(product => {
                    if (product.model && product.model.trim()) {
                        allModels.add(product.model.trim());
                    }
                });

                Array.from(allModels).sort().forEach(modelName => {
                    const option = document.createElement('option');
                    option.value = modelName;
                    option.textContent = modelName;
                    modelFilter.appendChild(option);
                });
            } else {
                // Show models for selected brand
                const brandModels = new Set();
                products.forEach(product => {
                    if (product.brand === selectedBrand && product.model && product.model.trim()) {
                        brandModels.add(product.model.trim());
                    }
                });

                Array.from(brandModels).sort().forEach(modelName => {
                    const option = document.createElement('option');
                    option.value = modelName;
                    option.textContent = modelName;
                    modelFilter.appendChild(option);
                });
            }
        }

        // Load search car brands
        function loadSearchCarBrands() {
            console.log('🚗 تحميل أنواع السيارات للبحث...');
            const carBrandSearch = document.getElementById('carBrandSearch');
            if (!carBrandSearch) {
                console.log('❌ عنصر carBrandSearch غير موجود');
                return;
            }

            // Get unique car brands from products
            const carBrands = new Set();
            products.forEach(product => {
                if (product.brand && product.brand.trim() && product.brand !== 'موبيل') {
                    carBrands.add(product.brand.trim());
                    console.log('🏷️ إضافة نوع سيارة من المنتجات:', product.brand.trim());
                }
            });

            // Add all available car brands from carModels
            Object.keys(carModels).forEach(brand => {
                carBrands.add(brand);
                console.log('🏷️ إضافة نوع سيارة من carModels:', brand);
            });

            // Update car brand search dropdown
            carBrandSearch.innerHTML = '<option value="">جميع أنواع السيارات</option>';
            const sortedBrands = Array.from(carBrands).sort();
            console.log('📋 أنواع السيارات المرتبة:', sortedBrands);

            sortedBrands.forEach(brand => {
                const option = document.createElement('option');
                option.value = brand;
                option.textContent = brand;
                carBrandSearch.appendChild(option);
            });

            console.log(`✅ تم تحميل ${sortedBrands.length} نوع سيارة`);
        }

        // Load search car years
        function loadSearchCarYears() {
            console.log('📅 تحميل موديلات السيارات للبحث...');
            const carYearSearch = document.getElementById('carYearSearch');
            if (!carYearSearch) {
                console.log('❌ عنصر carYearSearch غير موجود');
                return;
            }

            // Get unique years from products
            const years = new Set();
            products.forEach(product => {
                if (product.model && product.model.trim() && product.model !== 'عام') {
                    // Extract year from model if it contains numbers
                    const yearMatch = product.model.match(/\d{4}/);
                    if (yearMatch) {
                        years.add(yearMatch[0]);
                        console.log('📅 إضافة سنة من المنتجات:', yearMatch[0]);
                    }
                }
            });

            // Add common years
            for (let year = 2024; year >= 2000; year--) {
                years.add(year.toString());
            }
            years.add('عام');

            // Update car year search dropdown
            carYearSearch.innerHTML = '<option value="">جميع الموديلات</option>';
            const sortedYears = Array.from(years).sort((a, b) => {
                if (a === 'عام') return 1;
                if (b === 'عام') return -1;
                return b.localeCompare(a);
            });

            console.log('📋 الموديلات المرتبة:', sortedYears.slice(0, 10), '...');

            sortedYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                carYearSearch.appendChild(option);
            });

            console.log(`✅ تم تحميل ${sortedYears.length} موديل`);
        }

        // Update search car models based on selected brand
        function updateSearchCarModels() {
            console.log('🔄 تحديث ماركات السيارات...');
            const carBrandSearch = document.getElementById('carBrandSearch');
            const carModelSearch = document.getElementById('carModelSearch');

            if (!carBrandSearch || !carModelSearch) {
                console.log('❌ عناصر البحث بالسيارة غير موجودة');
                return;
            }

            const selectedBrand = carBrandSearch.value;
            console.log('🚗 نوع السيارة المختار:', selectedBrand);

            // Clear existing options
            carModelSearch.innerHTML = '<option value="">جميع الماركات</option>';

            if (!selectedBrand) {
                console.log('📋 عرض جميع الماركات من المنتجات');
                // If no brand selected, show all models from products
                const allModels = new Set();
                products.forEach(product => {
                    if (product.model && product.model.trim() && product.model !== 'عام') {
                        // Extract model name (remove year if present)
                        let modelName = product.model.trim();
                        modelName = modelName.replace(/\s*\d{4}\s*/, '').trim();
                        if (modelName) {
                            allModels.add(modelName);
                            console.log('🏷️ إضافة ماركة من المنتجات:', modelName);
                        }
                    }
                });

                Array.from(allModels).sort().forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    carModelSearch.appendChild(option);
                });

                console.log(`✅ تم عرض ${allModels.size} ماركة من المنتجات`);
            } else {
                console.log('🔍 عرض ماركات لنوع السيارة:', selectedBrand);
                const brandModels = new Set();

                // Add models from carModels data
                if (carModels[selectedBrand]) {
                    carModels[selectedBrand].forEach(model => {
                        brandModels.add(model);
                        console.log('🏷️ إضافة ماركة من carModels:', model);
                    });
                }

                // Add models from existing products for this brand
                products.forEach(product => {
                    if (product.brand === selectedBrand && product.model && product.model.trim() && product.model !== 'عام') {
                        let modelName = product.model.trim();
                        modelName = modelName.replace(/\s*\d{4}\s*/, '').trim();
                        if (modelName) {
                            brandModels.add(modelName);
                            console.log('🏷️ إضافة ماركة من المنتجات:', modelName);
                        }
                    }
                });

                // Add all models to dropdown
                Array.from(brandModels).sort().forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    carModelSearch.appendChild(option);
                });

                console.log(`✅ تم عرض ${brandModels.size} ماركة لـ ${selectedBrand}`);
            }
        }

        // Perform search
        function performSearch() {
            console.log('🔍 بدء البحث...');

            // Get all search field values
            const searchTerm = document.getElementById('searchInput')?.value.trim().toLowerCase() || '';
            const partNumberSearch = document.getElementById('partNumberSearch')?.value.trim().toLowerCase() || '';
            const categoryFilter = document.getElementById('categoryFilter')?.value || '';
            const brandFilter = document.getElementById('brandFilter')?.value || '';
            const modelFilter = document.getElementById('modelFilter')?.value || '';
            const carBrandSearch = document.getElementById('carBrandSearch')?.value || '';
            const carModelSearch = document.getElementById('carModelSearch')?.value || '';
            const carYearSearch = document.getElementById('carYearSearch')?.value || '';
            const searchResults = document.getElementById('searchResults');

            console.log('📊 قيم البحث:', {
                searchTerm, partNumberSearch, categoryFilter,
                carBrandSearch, carModelSearch, carYearSearch,
                brandFilter, modelFilter
            });

            let filteredProducts = [...products]; // Create a copy
            let searchDescription = [];
            let searchType = '';

            // Determine which search type is active and apply only that filter
            if (searchTerm) {
                console.log('🔤 تطبيق البحث النصي:', searchTerm);
                // Text search only
                filteredProducts = filteredProducts.filter(product => {
                    const nameMatch = product.name && product.name.toLowerCase().includes(searchTerm);
                    const partMatch = product.partNumber && product.partNumber.toLowerCase().includes(searchTerm);
                    const brandMatch = product.brand && product.brand.toLowerCase().includes(searchTerm);
                    const modelMatch = product.model && product.model.toLowerCase().includes(searchTerm);
                    const descMatch = product.description && product.description.toLowerCase().includes(searchTerm);

                    return nameMatch || partMatch || brandMatch || modelMatch || descMatch;
                });
                searchDescription.push(`البحث النصي: "${searchTerm}"`);
                searchType = 'text';

            } else if (partNumberSearch) {
                console.log('🏷️ تطبيق البحث برقم القطعة:', partNumberSearch);
                // Part number search only
                filteredProducts = filteredProducts.filter(product =>
                    product.partNumber && product.partNumber.toLowerCase().includes(partNumberSearch)
                );
                searchDescription.push(`رقم القطعة: "${partNumberSearch}"`);
                searchType = 'partNumber';

            } else if (categoryFilter) {
                console.log('📂 تطبيق البحث بالفئة:', categoryFilter);
                // Category search only
                filteredProducts = filteredProducts.filter(product =>
                    product.category === categoryFilter
                );
                searchDescription.push(`الفئة: ${categoryFilter}`);
                searchType = 'category';

            } else if (carBrandSearch || carModelSearch || carYearSearch) {
                console.log('🚗 تطبيق البحث بمعلومات السيارة');
                // Car information search - can combine multiple car fields

                if (carBrandSearch) {
                    console.log('🚗 فلترة بنوع السيارة:', carBrandSearch);
                    filteredProducts = filteredProducts.filter(product =>
                        product.brand === carBrandSearch
                    );
                    searchDescription.push(`نوع السيارة: ${carBrandSearch}`);
                }

                if (carModelSearch) {
                    console.log('🏷️ فلترة بماركة السيارة:', carModelSearch);
                    filteredProducts = filteredProducts.filter(product => {
                        if (product.model && product.model.trim()) {
                            const modelName = product.model.trim();
                            // Check if model contains the search term or matches exactly after removing year
                            const modelWithoutYear = modelName.replace(/\s*\d{4}\s*/, '').trim();
                            return modelName.includes(carModelSearch) || modelWithoutYear === carModelSearch;
                        }
                        return false;
                    });
                    searchDescription.push(`ماركة السيارة: ${carModelSearch}`);
                }

                if (carYearSearch && carYearSearch !== 'عام') {
                    console.log('📅 فلترة بموديل السيارة:', carYearSearch);
                    filteredProducts = filteredProducts.filter(product => {
                        if (product.model && product.model.trim()) {
                            return product.model.includes(carYearSearch);
                        }
                        return false;
                    });
                    searchDescription.push(`موديل السيارة: ${carYearSearch}`);
                }
                searchType = 'car';

            } else if (brandFilter) {
                console.log('🏷️ تطبيق البحث بالماركة القديمة:', brandFilter);
                // Old brand filter (for backward compatibility)
                filteredProducts = filteredProducts.filter(product =>
                    product.brand === brandFilter
                );
                searchDescription.push(`الماركة: ${brandFilter}`);
                searchType = 'oldBrand';

            } else if (modelFilter) {
                console.log('🚗 تطبيق البحث بالموديل القديم:', modelFilter);
                // Old model filter (for backward compatibility)
                filteredProducts = filteredProducts.filter(product =>
                    product.model === modelFilter
                );
                searchDescription.push(`الموديل: ${modelFilter}`);
                searchType = 'oldModel';
            }

            console.log(`✅ نتائج البحث: ${filteredProducts.length} من أصل ${products.length}`);

            // Update search results text
            if (searchDescription.length > 0) {
                const resultText = `تم العثور على ${filteredProducts.length} منتج من أصل ${products.length}`;
                const filterText = searchDescription.join(' + ');

                if (searchResults) {
                    searchResults.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <span>${resultText}</span>
                            <span class="badge bg-primary">${getSearchTypeLabel(searchType)}</span>
                        </div>
                        <small class="text-muted">${filterText}</small>
                    `;
                }

                if (filteredProducts.length === 0) {
                    showNotification('لم يتم العثور على منتجات تطابق البحث', 'warning');
                } else {
                    showNotification(`${resultText} - ${getSearchTypeLabel(searchType)}`, 'success');
                }
            } else {
                if (searchResults) {
                    searchResults.textContent = '';
                }
            }

            // Display filtered products
            displayProducts(filteredProducts);
        }

        // Get search type label
        function getSearchTypeLabel(searchType) {
            const labels = {
                'text': 'بحث نصي',
                'partNumber': 'بحث برقم القطعة',
                'category': 'بحث بالفئة',
                'car': 'بحث بمعلومات السيارة',
                'oldBrand': 'بحث بالماركة',
                'oldModel': 'بحث بالموديل'
            };
            return labels[searchType] || 'بحث عام';
        }

        // Test search functionality
        function testSearchFunctionality() {
            console.log('🧪 اختبار وظائف البحث...');

            // Test if all search elements exist
            const searchElements = [
                'searchInput',
                'partNumberSearch',
                'categoryFilter',
                'carBrandSearch',
                'carModelSearch',
                'carYearSearch',
                'brandFilter',
                'modelFilter'
            ];

            searchElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    console.log(`✅ ${elementId} موجود`);
                    if (element.tagName === 'SELECT') {
                        console.log(`   📋 خيارات ${elementId}:`, element.options.length);
                    }
                } else {
                    console.log(`❌ ${elementId} غير موجود`);
                }
            });

            // Test search with sample data
            console.log('📊 عدد المنتجات:', products.length);
            products.forEach((product, index) => {
                console.log(`📦 منتج ${index + 1}:`, {
                    name: product.name,
                    partNumber: product.partNumber,
                    category: product.category,
                    brand: product.brand,
                    model: product.model
                });
            });

            // Test car search specifically
            testCarSearch();
        }

        // Test car search functionality
        function testCarSearch() {
            console.log('🚗 اختبار البحث بمعلومات السيارة...');

            // Test car brand loading
            const carBrandSearch = document.getElementById('carBrandSearch');
            if (carBrandSearch) {
                console.log('🏷️ أنواع السيارات المتاحة:', carBrandSearch.options.length - 1);
                for (let i = 1; i < carBrandSearch.options.length; i++) {
                    console.log(`   - ${carBrandSearch.options[i].value}`);
                }
            }

            // Test automatic car model update
            setTimeout(() => {
                if (carBrandSearch && carBrandSearch.options.length > 1) {
                    const firstBrand = carBrandSearch.options[1].value;
                    console.log('🔄 اختبار تحديث الماركات لـ:', firstBrand);
                    carBrandSearch.value = firstBrand;
                    updateSearchCarModels();

                    setTimeout(() => {
                        const carModelSearch = document.getElementById('carModelSearch');
                        if (carModelSearch) {
                            console.log('🏷️ ماركات متاحة لـ', firstBrand + ':', carModelSearch.options.length - 1);
                            for (let i = 1; i < carModelSearch.options.length; i++) {
                                console.log(`   - ${carModelSearch.options[i].value}`);
                            }
                        }

                        // Reset
                        carBrandSearch.value = '';
                        updateSearchCarModels();
                    }, 100);
                }
            }, 500);
        }

        // Clear other searches when one type is selected
        function clearOtherSearches(activeType) {
            console.log('🧹 مسح البحث الآخر، النوع النشط:', activeType);

            const searchTypes = {
                'text': ['partNumberSearch', 'categoryFilter', 'carBrandSearch', 'carModelSearch', 'carYearSearch', 'brandFilter', 'modelFilter'],
                'partNumber': ['searchInput', 'categoryFilter', 'carBrandSearch', 'carModelSearch', 'carYearSearch', 'brandFilter', 'modelFilter'],
                'category': ['searchInput', 'partNumberSearch', 'carBrandSearch', 'carModelSearch', 'carYearSearch', 'brandFilter', 'modelFilter'],
                'car': ['searchInput', 'partNumberSearch', 'categoryFilter', 'brandFilter', 'modelFilter']
            };

            if (searchTypes[activeType]) {
                searchTypes[activeType].forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        const oldValue = field.value;
                        field.value = '';
                        if (oldValue) {
                            console.log(`🧹 مسح ${fieldId}: "${oldValue}"`);
                        }
                    }
                });

                // Update car models if car search is cleared
                if (activeType !== 'car') {
                    updateSearchCarModels();
                }
            }
        }

        // Clear all searches
        function clearAllSearches() {
            // Clear all search fields
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('brandFilter').value = '';
            document.getElementById('modelFilter').value = '';
            document.getElementById('searchResults').textContent = '';

            // Clear new search fields
            const partNumberSearch = document.getElementById('partNumberSearch');
            const carBrandSearch = document.getElementById('carBrandSearch');
            const carModelSearch = document.getElementById('carModelSearch');
            const carYearSearch = document.getElementById('carYearSearch');

            if (partNumberSearch) partNumberSearch.value = '';
            if (carBrandSearch) carBrandSearch.value = '';
            if (carModelSearch) carModelSearch.value = '';
            if (carYearSearch) carYearSearch.value = '';

            // Reload models to show all models again
            loadModels();
            updateSearchCarModels();

            displayProducts(products);
            showNotification('تم مسح جميع مرشحات البحث', 'info');
        }

        // Legacy clear search function for backward compatibility
        function clearSearch() {
            clearAllSearches();
        }

        // Filter by category
        function filterByCategory(categoryName) {
            document.getElementById('categoryFilter').value = categoryName;
            document.getElementById('searchInput').value = '';
            document.getElementById('brandFilter').value = '';
            document.getElementById('modelFilter').value = '';
            loadModels();
            performSearch();
        }

        // Filter by brand
        function filterByBrand(brandName) {
            document.getElementById('brandFilter').value = brandName;
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('modelFilter').value = '';
            loadModels();
            performSearch();
        }

        // Filter by model
        function filterByModel(brandName, modelName) {
            document.getElementById('brandFilter').value = brandName;
            document.getElementById('modelFilter').value = modelName;
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            loadModels();
            performSearch();
        }

        // Show all categories
        function showAllCategories() {
            const categoriesCard = document.getElementById('categoriesCard');
            const brandsCard = document.getElementById('brandsCard');
            const modelsCard = document.getElementById('modelsCard');

            // Hide other cards
            brandsCard.style.display = 'none';
            modelsCard.style.display = 'none';

            if (categoriesCard.style.display === 'none') {
                categoriesCard.style.display = 'block';
                showNotification('تم عرض جميع الفئات', 'info');
            } else {
                categoriesCard.style.display = 'none';
                showNotification('تم إخفاء الفئات', 'info');
            }
        }

        // Show all brands
        function showAllBrands() {
            const categoriesCard = document.getElementById('categoriesCard');
            const brandsCard = document.getElementById('brandsCard');
            const modelsCard = document.getElementById('modelsCard');
            const brandsContainer = document.getElementById('brandsContainer');

            // Hide other cards
            categoriesCard.style.display = 'none';
            modelsCard.style.display = 'none';

            if (brandsCard.style.display === 'none') {
                brandsCard.style.display = 'block';
                loadBrandsDisplay();
                showNotification('تم عرض جميع الماركات', 'info');
            } else {
                brandsCard.style.display = 'none';
                showNotification('تم إخفاء الماركات', 'info');
            }
        }

        // Show all models
        function showAllModels() {
            const categoriesCard = document.getElementById('categoriesCard');
            const brandsCard = document.getElementById('brandsCard');
            const modelsCard = document.getElementById('modelsCard');

            // Hide other cards
            categoriesCard.style.display = 'none';
            brandsCard.style.display = 'none';

            if (modelsCard.style.display === 'none') {
                modelsCard.style.display = 'block';
                loadModelsDisplay();
                showNotification('تم عرض جميع الموديلات', 'info');
            } else {
                modelsCard.style.display = 'none';
                showNotification('تم إخفاء الموديلات', 'info');
            }
        }

        // Load brands display
        function loadBrandsDisplay() {
            const brandsContainer = document.getElementById('brandsContainer');

            // Get unique brands with statistics
            const brands = {};
            products.forEach(product => {
                if (product.brand && product.brand.trim()) {
                    const brandName = product.brand.trim();
                    if (!brands[brandName]) {
                        brands[brandName] = {
                            name: brandName,
                            count: 0,
                            totalQuantity: 0,
                            avgPrice: 0,
                            models: new Set(),
                            products: []
                        };
                    }
                    brands[brandName].count++;
                    brands[brandName].products.push(product);

                    if (product.model && product.model.trim()) {
                        brands[brandName].models.add(product.model.trim());
                    }

                    // Calculate total quantity and average price
                    product.shopDetails.forEach(shop => {
                        brands[brandName].totalQuantity += shop.quantity;
                        brands[brandName].avgPrice += shop.price;
                    });
                }
            });

            // Calculate average prices
            Object.keys(brands).forEach(brandName => {
                const brand = brands[brandName];
                const totalPrices = brand.products.reduce((sum, product) => {
                    return sum + product.shopDetails.reduce((pSum, shop) => pSum + shop.price, 0);
                }, 0);
                const totalShops = brand.products.reduce((sum, product) => sum + product.shopDetails.length, 0);
                brand.avgPrice = totalShops > 0 ? Math.round(totalPrices / totalShops) : 0;
            });

            brandsContainer.innerHTML = '';
            if (Object.keys(brands).length === 0) {
                brandsContainer.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد ماركات محددة للمنتجات
                        </div>
                    </div>
                `;
                return;
            }

            Object.keys(brands).sort().forEach(brandName => {
                const brand = brands[brandName];
                const brandCard = `
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="card product-card h-100" style="cursor: pointer;" onclick="filterByBrand('${brandName}')">
                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <i class="fas fa-car fa-2x text-success"></i>
                                </div>
                                <h6 class="card-title text-success">${brandName}</h6>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">المنتجات</small>
                                        <div class="fw-bold text-primary">${brand.count}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">الموديلات</small>
                                        <div class="fw-bold text-info">${brand.models.size}</div>
                                    </div>
                                </div>
                                <div class="row text-center mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">الكمية</small>
                                        <div class="fw-bold text-warning">${brand.totalQuantity}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">متوسط السعر</small>
                                        <div class="fw-bold text-danger">${brand.avgPrice} ريال</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button class="btn btn-outline-success btn-sm" onclick="event.stopPropagation(); filterByBrand('${brandName}')">
                                        <i class="fas fa-search me-1"></i>
                                        عرض المنتجات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                brandsContainer.innerHTML += brandCard;
            });
        }

        // Load models display
        function loadModelsDisplay() {
            const modelsContainer = document.getElementById('modelsContainer');

            // Get unique models with statistics grouped by brand
            const modelsByBrand = {};
            products.forEach(product => {
                if (product.model && product.model.trim()) {
                    const modelName = product.model.trim();
                    const brandName = product.brand && product.brand.trim() ? product.brand.trim() : 'غير محدد';

                    if (!modelsByBrand[brandName]) {
                        modelsByBrand[brandName] = {};
                    }

                    if (!modelsByBrand[brandName][modelName]) {
                        modelsByBrand[brandName][modelName] = {
                            name: modelName,
                            brand: brandName,
                            count: 0,
                            totalQuantity: 0,
                            avgPrice: 0,
                            products: []
                        };
                    }

                    modelsByBrand[brandName][modelName].count++;
                    modelsByBrand[brandName][modelName].products.push(product);

                    // Calculate total quantity and average price
                    product.shopDetails.forEach(shop => {
                        modelsByBrand[brandName][modelName].totalQuantity += shop.quantity;
                        modelsByBrand[brandName][modelName].avgPrice += shop.price;
                    });
                }
            });

            // Calculate average prices
            Object.keys(modelsByBrand).forEach(brandName => {
                Object.keys(modelsByBrand[brandName]).forEach(modelName => {
                    const model = modelsByBrand[brandName][modelName];
                    const totalPrices = model.products.reduce((sum, product) => {
                        return sum + product.shopDetails.reduce((pSum, shop) => pSum + shop.price, 0);
                    }, 0);
                    const totalShops = model.products.reduce((sum, product) => sum + product.shopDetails.length, 0);
                    model.avgPrice = totalShops > 0 ? Math.round(totalPrices / totalShops) : 0;
                });
            });

            modelsContainer.innerHTML = '';
            if (Object.keys(modelsByBrand).length === 0) {
                modelsContainer.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد موديلات محددة للمنتجات
                        </div>
                    </div>
                `;
                return;
            }

            // Display models grouped by brand
            Object.keys(modelsByBrand).sort().forEach(brandName => {
                const models = modelsByBrand[brandName];

                // Add brand header
                modelsContainer.innerHTML += `
                    <div class="col-12 mb-3">
                        <h6 class="text-primary border-bottom pb-2">
                            <i class="fas fa-car me-2"></i>
                            ${brandName}
                        </h6>
                    </div>
                `;

                Object.keys(models).sort().forEach(modelName => {
                    const model = models[modelName];
                    const modelCard = `
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card product-card h-100" style="cursor: pointer;" onclick="filterByModel('${brandName}', '${modelName}')">
                                <div class="card-body text-center">
                                    <div class="mb-2">
                                        <i class="fas fa-cogs fa-2x text-warning"></i>
                                    </div>
                                    <h6 class="card-title text-warning">${modelName}</h6>
                                    <small class="text-muted">${brandName}</small>
                                    <div class="row text-center mt-2">
                                        <div class="col-6">
                                            <small class="text-muted">المنتجات</small>
                                            <div class="fw-bold text-primary">${model.count}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">الكمية</small>
                                            <div class="fw-bold text-info">${model.totalQuantity}</div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">متوسط السعر</small>
                                        <div class="fw-bold text-success">${model.avgPrice} ريال</div>
                                    </div>
                                    <div class="mt-2">
                                        <button class="btn btn-outline-warning btn-sm" onclick="event.stopPropagation(); filterByModel('${brandName}', '${modelName}')">
                                            <i class="fas fa-search me-1"></i>
                                            عرض المنتجات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    modelsContainer.innerHTML += modelCard;
                });
            });
        }

        // Update summary cards
        function updateSummaryCards() {
            const totalProducts = products.length;
            const uniqueShops = new Set();
            let totalQuantity = 0;
            let totalPrice = 0;
            let priceCount = 0;

            products.forEach(product => {
                product.shopDetails.forEach(shop => {
                    uniqueShops.add(shop.shopName);
                    totalQuantity += shop.quantity;
                    totalPrice += shop.price;
                    priceCount++;
                });
            });

            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('totalShops').textContent = uniqueShops.size;
            document.getElementById('totalQuantity').textContent = totalQuantity;
            document.getElementById('averagePrice').textContent = priceCount > 0 ? Math.round(totalPrice / priceCount) + ' ريال' : '0 ريال';
        }

        // Load shops
        function loadShops() {
            const container = document.getElementById('shopsContainer');
            const addShopBtn = document.getElementById('addShopBtn');

            // Show/hide add shop button based on user role
            if (addShopBtn) {
                if (currentUser && currentUser.role === 'admin') {
                    addShopBtn.style.display = 'inline-block';
                } else {
                    addShopBtn.style.display = 'none';
                }
            }

            container.innerHTML = '';

            if (shops.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد محلات
                        </div>
                    </div>
                `;
                return;
            }

            shops.forEach(shop => {
                const shopProducts = products.filter(product =>
                    product.shopDetails.some(detail => detail.shopId === shop.id)
                );

                const shopCard = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card product-card h-100">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${shop.name}</h5>
                                <p class="card-text text-muted">${shop.description}</p>

                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>${shop.phone}<br>
                                        <i class="fas fa-map-marker-alt me-1"></i>${shop.address}, ${shop.city}<br>
                                        ${shop.specialty ? `<i class="fas fa-tag me-1"></i>${shop.specialty}<br>` : ''}
                                        <i class="fas fa-calendar me-1"></i>انضم في ${shop.joinDate}
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge bg-primary">${shopProducts.length} منتج</span>
                                        <span class="badge bg-warning">⭐ ${shop.rating}</span>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewShopProducts(${shop.id})" title="عرض المنتجات">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        ${currentUser && currentUser.role === 'admin' ? `
                                            <button class="btn btn-outline-warning" onclick="editShop(${shop.id})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteShop(${shop.id})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += shopCard;
            });

            showNotification('تم تحميل المحلات بنجاح!', 'success');
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const alertDiv = document.getElementById('loginAlert');

            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };

                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('dashboardLink').style.display = 'inline';
                document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;

                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';

                showNotification(`مرحباً ${currentUser.name}! تم تسجيل الدخول بنجاح.`, 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    showPage('dashboard');
                }, 1000);

            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';

            showNotification('تم تسجيل الخروج بنجاح', 'info');
            showPage('home');
        }

        // Load dashboard
        function loadDashboard() {
            if (!currentUser) {
                showPage('login');
                return;
            }

            if (currentUser.role === 'admin') {
                loadAdminDashboard();
            } else if (currentUser.role === 'shop') {
                loadShopDashboard();
            }
        }

        // Load admin dashboard
        function loadAdminDashboard() {
            document.getElementById('dashboardTitle').textContent = 'لوحة تحكم المدير';
            document.getElementById('dashboardMessage').textContent = `مرحباً ${currentUser.name}`;

            document.getElementById('dashboardProducts').textContent = products.length;
            document.getElementById('dashboardShops').textContent = shops.length;

            const totalValue = products.reduce((sum, product) => {
                return sum + product.shopDetails.reduce((pSum, shop) => pSum + (shop.price * shop.quantity), 0);
            }, 0);
            document.getElementById('dashboardValue').textContent = Math.round(totalValue) + ' ريال';

            // Show all products for admin
            const container = document.getElementById('dashboardProductsContainer');
            container.innerHTML = '';

            if (products.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>لا توجد منتجات</h5>
                            <p class="mb-0">لا توجد منتجات في النظام حالياً.</p>
                        </div>
                    </div>
                `;
                return;
            }

            products.forEach(product => {
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                const avgPrice = product.shopDetails.reduce((sum, shop) => sum + shop.price, 0) / product.shopDetails.length;

                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card product-card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}</p>

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold text-primary">${Math.round(avgPrice)} ريال</span>
                                    <span class="badge bg-success">الكمية: ${totalQuantity}</span>
                                </div>

                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}

                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm ms-1" onclick="deleteProduct(${product.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Load shop dashboard
        function loadShopDashboard() {
            document.getElementById('dashboardTitle').textContent = 'لوحة تحكم المحل';
            document.getElementById('dashboardMessage').textContent = `مرحباً ${currentUser.name}`;

            // Filter products for current shop
            const shopProducts = products.filter(product =>
                product.shopDetails.some(shop => shop.shopId === currentUser.shopId)
            );

            document.getElementById('dashboardProducts').textContent = shopProducts.length;
            document.getElementById('dashboardShops').textContent = '1';

            const totalValue = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                return sum + (shopDetail ? shopDetail.price * shopDetail.quantity : 0);
            }, 0);
            document.getElementById('dashboardValue').textContent = Math.round(totalValue) + ' ريال';

            // Show shop products
            const container = document.getElementById('dashboardProductsContainer');
            container.innerHTML = '';

            if (shopProducts.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>لا توجد منتجات</h5>
                            <p class="mb-0">لم تقم بإضافة أي منتجات بعد في محلك.</p>
                            <p class="mb-0">اضغط "إضافة منتج جديد" لبدء إضافة منتجاتك.</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Check if shop has all products
            const totalProductsInSystem = products.length;
            const shopProductsCount = shopProducts.length;
            const missingProductsCount = totalProductsInSystem - shopProductsCount;

            // Add bulk edit section for shop owner
            let bulkEditSection = `
                <div class="col-12 mb-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-tools me-2"></i>أدوات التحكم السريع:</h6>
            `;

            // Add missing products alert if any
            if (missingProductsCount > 0) {
                bulkEditSection += `
                    <div class="alert alert-warning mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>يوجد ${missingProductsCount} منتج غير مضاف لمحلك من أصل ${totalProductsInSystem}</strong>
                            </div>
                            <button class="btn btn-warning btn-sm" onclick="addAllProductsToShop()">
                                <i class="fas fa-plus-circle me-1"></i>
                                إضافة جميع الأصناف
                            </button>
                        </div>
                    </div>
                `;
            }

            bulkEditSection += `
                            <div class="row">
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="showBulkPriceUpdate()">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        تحديث الأسعار بالجملة
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-success btn-sm w-100" onclick="showBulkQuantityUpdate()">
                                        <i class="fas fa-boxes me-1"></i>
                                        تحديث الكميات بالجملة
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-info btn-sm w-100" onclick="showAvailableProducts()">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض المتوفر فقط
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-warning btn-sm w-100" onclick="addAllProductsToShop()">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        إضافة جميع الأصناف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = bulkEditSection;

            shopProducts.forEach(product => {
                const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (!shopDetail) return;

                const isAvailable = shopDetail.quantity > 0;
                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card product-card ${!isAvailable ? 'border-warning' : ''}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title">${product.name}</h6>
                                    <span class="badge ${isAvailable ? 'bg-success' : 'bg-warning text-dark'}">
                                        ${isAvailable ? 'متوفر' : 'غير متوفر'}
                                    </span>
                                </div>

                                <p class="text-muted small">${product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}</p>

                                <!-- Quick Edit Form -->
                                <div class="bg-light p-2 rounded mb-2">
                                    <div class="row">
                                        <div class="col-6">
                                            <label class="form-label small">السعر (ريال)</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="price_${product.id}" value="${shopDetail.price}"
                                                   step="0.01" min="0" onchange="quickUpdateProduct(${product.id})">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label small">الكمية</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="quantity_${product.id}" value="${shopDetail.quantity}"
                                                   min="0" onchange="quickUpdateProduct(${product.id})">
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">القيمة الإجمالية:</small>
                                    <span class="fw-bold text-success" id="total_${product.id}">
                                        ${Math.round(shopDetail.price * shopDetail.quantity)} ريال
                                    </span>
                                </div>

                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                ${product.model ? `<span class="badge bg-primary badge-sm ms-1">${product.model}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}

                                <div class="mt-2 d-flex justify-content-between">
                                    <button class="btn btn-outline-success btn-sm" onclick="quickUpdateProduct(${product.id})" title="حفظ التغييرات">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="removeProductFromShop(${product.id})" title="حذف من المحل">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Show add shop modal
        function showAddShopModal() {
            if (!currentUser) {
                showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                showPage('login');
                return;
            }

            if (currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بإضافة محلات - هذه الوظيفة للمدير فقط', 'danger');
                return;
            }

            const modal = `
                <div class="modal fade" id="addShopModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-store me-2"></i>
                                    إضافة محل جديد
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addShopForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="shopUsername" required>
                                                <label>اسم المستخدم *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="password" class="form-control" id="shopPassword" required>
                                                <label>كلمة المرور *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="shopName" required>
                                        <label>اسم المحل *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="shopDescription" style="height: 100px" required></textarea>
                                        <label>وصف المحل *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="tel" class="form-control" id="shopPhone" required>
                                                <label>رقم الهاتف *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="email" class="form-control" id="shopEmail">
                                                <label>البريد الإلكتروني</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="shopAddress" required>
                                        <label>العنوان *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="shopCity" required>
                                                    <option value="">اختر المدينة</option>
                                                    <option value="الرياض">الرياض</option>
                                                    <option value="جدة">جدة</option>
                                                    <option value="الدمام">الدمام</option>
                                                    <option value="مكة المكرمة">مكة المكرمة</option>
                                                    <option value="المدينة المنورة">المدينة المنورة</option>
                                                </select>
                                                <label>المدينة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="shopSpecialty">
                                                <label>التخصص</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-success" onclick="addShop()">
                                    <i class="fas fa-save me-2"></i>
                                    إضافة المحل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('addShopModal'));
            modalElement.show();
        }

        // Add shop
        function addShop() {
            console.log('🏪 بدء إضافة محل جديد...');

            // Check if elements exist
            const requiredElements = ['shopUsername', 'shopPassword', 'shopName', 'shopDescription', 'shopPhone', 'shopAddress', 'shopCity', 'shopSpecialty'];
            for (let elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (!element) {
                    console.error(`❌ العنصر ${elementId} غير موجود`);
                    showNotification(`خطأ: العنصر ${elementId} غير موجود`, 'danger');
                    return;
                }
            }

            const shopData = {
                username: document.getElementById('shopUsername').value.trim(),
                password: document.getElementById('shopPassword').value.trim(),
                name: document.getElementById('shopName').value.trim(),
                description: document.getElementById('shopDescription').value.trim(),
                phone: document.getElementById('shopPhone').value.trim(),
                email: document.getElementById('shopEmail').value.trim(),
                address: document.getElementById('shopAddress').value.trim(),
                city: document.getElementById('shopCity').value,
                specialty: document.getElementById('shopSpecialty').value.trim()
            };

            console.log('📝 بيانات المحل:', shopData);

            // Validation
            if (!shopData.username || !shopData.password || !shopData.name || !shopData.description ||
                !shopData.phone || !shopData.address || !shopData.city) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if username exists
            if (users[shopData.username]) {
                showNotification('اسم المستخدم موجود بالفعل', 'warning');
                return;
            }

            // Check if shop name exists
            if (shops.find(shop => shop.name.toLowerCase() === shopData.name.toLowerCase())) {
                showNotification('اسم المحل موجود بالفعل', 'warning');
                return;
            }

            // Create new shop
            const newShop = {
                id: nextShopId++,
                name: shopData.name,
                description: shopData.description,
                phone: shopData.phone,
                email: shopData.email,
                address: shopData.address,
                city: shopData.city,
                specialty: shopData.specialty,
                rating: 0,
                joinDate: new Date().toISOString().split('T')[0]
            };

            // Create new user
            users[shopData.username] = {
                password: shopData.password,
                role: 'shop',
                name: shopData.name,
                shopId: newShop.id
            };

            // Add shop to array
            shops.push(newShop);

            // Add all existing products to the new shop with default values
            products.forEach(product => {
                // Check if product doesn't already have this shop
                const existingShopDetail = product.shopDetails.find(shop => shop.shopId === newShop.id);
                if (!existingShopDetail) {
                    // Calculate average price from existing shops for reference
                    const existingPrices = product.shopDetails.map(shop => shop.price);
                    const avgPrice = existingPrices.length > 0 ?
                        Math.round(existingPrices.reduce((sum, price) => sum + price, 0) / existingPrices.length) : 100;

                    // Add this shop to the product with default values
                    product.shopDetails.push({
                        shopName: newShop.name,
                        shopId: newShop.id,
                        price: avgPrice, // Use average price as starting point
                        quantity: 0 // Start with 0 quantity - shop owner will update
                    });
                }
            });

            // Save to localStorage
            saveToStorage();
            console.log('💾 تم حفظ البيانات');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addShopModal'));
            if (modal) {
                modal.hide();
                console.log('❌ تم إغلاق النافذة');
            }

            console.log('✅ تم إضافة المحل بنجاح');
            showNotification(`تم إضافة المحل "${shopData.name}" بنجاح!`, 'success');
            setTimeout(() => {
                showNotification(`بيانات تسجيل الدخول: ${shopData.username} / ${shopData.password}`, 'info');
            }, 1000);
            setTimeout(() => {
                showNotification(`تم إضافة ${products.length} منتج للمحل الجديد. يمكن للمحل تعديل الأسعار والكميات من لوحة التحكم.`, 'info');
            }, 2000);

            // Refresh shops page if currently viewing
            if (document.getElementById('shops').classList.contains('active')) {
                loadShops();
            }
        }

        // Show add product modal
        function showAddProductModal() {
            if (!currentUser) {
                showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                showPage('login');
                return;
            }

            if (currentUser.role !== 'shop' && currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بإضافة منتجات', 'danger');
                return;
            }

            const modal = `
                <div class="modal fade" id="addProductModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-box me-2"></i>
                                    إضافة منتج جديد
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addProductForm">
                                    <!-- Car Information Section -->
                                    <div class="alert alert-primary">
                                        <h6><i class="fas fa-car me-2"></i>معلومات السيارة</h6>
                                        <p class="mb-0 small">يرجى تحديد نوع السيارة والماركة والموديل أولاً</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="carBrand" onchange="updateCarModels()" required>
                                                    <option value="">اختر نوع السيارة</option>
                                                    <option value="تويوتا">تويوتا</option>
                                                    <option value="هوندا">هوندا</option>
                                                    <option value="نيسان">نيسان</option>
                                                    <option value="هيونداي">هيونداي</option>
                                                    <option value="كيا">كيا</option>
                                                    <option value="مازدا">مازدا</option>
                                                    <option value="ميتسوبيشي">ميتسوبيشي</option>
                                                    <option value="سوزوكي">سوزوكي</option>
                                                    <option value="فورد">فورد</option>
                                                    <option value="شيفروليه">شيفروليه</option>
                                                    <option value="بي إم دبليو">بي إم دبليو</option>
                                                    <option value="مرسيدس">مرسيدس</option>
                                                    <option value="أودي">أودي</option>
                                                    <option value="فولكس واجن">فولكس واجن</option>
                                                    <option value="لكزس">لكزس</option>
                                                    <option value="إنفينيتي">إنفينيتي</option>
                                                    <option value="أكورا">أكورا</option>
                                                    <option value="عام">عام (جميع السيارات)</option>
                                                </select>
                                                <label>نوع السيارة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="carModel" required>
                                                    <option value="">اختر الماركة</option>
                                                </select>
                                                <label>الماركة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="carYear" required>
                                                    <option value="">اختر الموديل</option>
                                                    <option value="2024">2024</option>
                                                    <option value="2023">2023</option>
                                                    <option value="2022">2022</option>
                                                    <option value="2021">2021</option>
                                                    <option value="2020">2020</option>
                                                    <option value="2019">2019</option>
                                                    <option value="2018">2018</option>
                                                    <option value="2017">2017</option>
                                                    <option value="2016">2016</option>
                                                    <option value="2015">2015</option>
                                                    <option value="2014">2014</option>
                                                    <option value="2013">2013</option>
                                                    <option value="2012">2012</option>
                                                    <option value="2011">2011</option>
                                                    <option value="2010">2010</option>
                                                    <option value="2009">2009</option>
                                                    <option value="2008">2008</option>
                                                    <option value="2007">2007</option>
                                                    <option value="2006">2006</option>
                                                    <option value="2005">2005</option>
                                                    <option value="2004">2004</option>
                                                    <option value="2003">2003</option>
                                                    <option value="2002">2002</option>
                                                    <option value="2001">2001</option>
                                                    <option value="2000">2000</option>
                                                    <option value="عام">عام</option>
                                                </select>
                                                <label>الموديل (السنة) *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Product Information Section -->
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-cog me-2"></i>معلومات القطعة</h6>
                                        <p class="mb-0 small">تفاصيل قطعة الغيار</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="partNumber">
                                                <label>كود الصنف / رقم القطعة</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="productCategory">
                                                    <option value="">اختر الفئة</option>
                                                    <option value="فلاتر">فلاتر</option>
                                                    <option value="فرامل">فرامل</option>
                                                    <option value="زيوت">زيوت</option>
                                                    <option value="كهرباء">كهرباء</option>
                                                    <option value="إطارات">إطارات</option>
                                                    <option value="محرك">محرك</option>
                                                    <option value="تكييف">تكييف</option>
                                                    <option value="عادم">عادم</option>
                                                    <option value="تعليق">تعليق</option>
                                                    <option value="توجيه">توجيه</option>
                                                    <option value="إضاءة">إضاءة</option>
                                                    <option value="أخرى">أخرى</option>
                                                </select>
                                                <label>فئة القطعة</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="productName" required>
                                        <label>اسم الصنف *</label>
                                        <div class="form-text">سيتم إنشاء الاسم تلقائياً بناءً على معلومات السيارة والقطعة</div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="productDescription" style="height: 100px"></textarea>
                                        <label>وصف المنتج</label>
                                    </div>

                                    <!-- Pricing Section -->
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-dollar-sign me-2"></i>معلومات السعر والكمية</h6>
                                        <p class="mb-0 small">السعر والكمية في محلك</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required>
                                                <label>السعر (ريال) *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="productQuantity" min="0" required>
                                                <label>الكمية المتوفرة *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Auto-generated Preview -->
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-eye me-2"></i>معاينة الصنف</h6>
                                        <div id="productPreview">
                                            <strong>اسم الصنف:</strong> <span id="previewName">سيتم إنشاؤه تلقائياً</span><br>
                                            <strong>الوصف:</strong> <span id="previewDescription">سيتم إنشاؤه تلقائياً</span>
                                        </div>
                                    </div>
                                </form>

                                <div class="alert alert-info mt-3">
                                    <h6><i class="fas fa-lightbulb me-2"></i>نصيحة:</h6>
                                    <p class="mb-0">يمكنك إضافة جميع الأصناف الموجودة في النظام لمحلك دفعة واحدة بالضغط على "إضافة جميع الأصناف"، ثم تعديل الأسعار والكميات لاحقاً من لوحة التحكم.</p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <div class="me-auto">
                                    <button type="button" class="btn btn-outline-info" onclick="addAllProductsToShop()" title="إضافة جميع المنتجات الموجودة في النظام">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        إضافة جميع الأصناف
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <button type="button" class="btn btn-primary" onclick="addProduct()">
                                        <i class="fas fa-save me-2"></i>
                                        إضافة المنتج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('addProductModal'));
            modalElement.show();

            // Add event listeners after modal is shown
            setTimeout(() => {
                addProductFormListeners();
                updateProductPreview();
            }, 100);
        }

        // Add product
        function addProduct() {
            console.log('📦 بدء إضافة منتج جديد...');

            // Check if elements exist
            const requiredElements = ['carBrand', 'carModel', 'carYear', 'productName', 'productDescription', 'partNumber', 'productCategory', 'productPrice', 'productQuantity'];
            for (let elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (!element) {
                    console.error(`❌ العنصر ${elementId} غير موجود`);
                    showNotification(`خطأ: العنصر ${elementId} غير موجود`, 'danger');
                    return;
                }
            }

            const productData = {
                carBrand: document.getElementById('carBrand').value,
                carModel: document.getElementById('carModel').value,
                carYear: document.getElementById('carYear').value,
                name: document.getElementById('productName').value.trim(),
                description: document.getElementById('productDescription').value.trim(),
                partNumber: document.getElementById('partNumber').value.trim(),
                category: document.getElementById('productCategory').value,
                brand: document.getElementById('carBrand').value, // Use car brand as product brand
                model: document.getElementById('carModel').value + (document.getElementById('carYear').value ? ' ' + document.getElementById('carYear').value : ''), // Combine model and year
                price: parseFloat(document.getElementById('productPrice').value),
                quantity: parseInt(document.getElementById('productQuantity').value)
            };

            console.log('📝 بيانات المنتج:', productData);

            // Validation
            if (!productData.carBrand || !productData.carModel || !productData.carYear || !productData.name ||
                isNaN(productData.price) || productData.price < 0 || isNaN(productData.quantity) || productData.quantity < 0) {
                showNotification('يرجى ملء جميع الحقول المطلوبة: نوع السيارة، الماركة، الموديل، اسم الصنف، السعر ≥ 0، الكمية ≥ 0', 'warning');
                return;
            }

            // Check if product exists
            const existingProduct = products.find(p =>
                p.name.toLowerCase() === productData.name.toLowerCase() &&
                p.partNumber === productData.partNumber
            );

            if (existingProduct) {
                // Add to existing product
                const shopDetail = existingProduct.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (shopDetail) {
                    showNotification('المنتج موجود بالفعل في محلك', 'warning');
                    return;
                }

                existingProduct.shopDetails.push({
                    shopName: currentUser.name,
                    shopId: currentUser.shopId,
                    price: productData.price,
                    quantity: productData.quantity
                });

                showNotification(`تم إضافة المنتج "${productData.name}" إلى محلك بنجاح!`, 'success');
            } else {
                // Create new product
                const newProduct = {
                    id: nextProductId++,
                    name: productData.name,
                    description: productData.description,
                    partNumber: productData.partNumber,
                    category: productData.category,
                    brand: productData.brand,
                    model: productData.model,
                    shopDetails: [{
                        shopName: currentUser.name,
                        shopId: currentUser.shopId,
                        price: productData.price,
                        quantity: productData.quantity
                    }]
                };

                products.push(newProduct);
                showNotification(`تم إضافة المنتج الجديد "${productData.name}" بنجاح!`, 'success');
            }

            // Save to localStorage
            saveToStorage();
            console.log('💾 تم حفظ بيانات المنتج');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            if (modal) {
                modal.hide();
                console.log('❌ تم إغلاق نافذة المنتج');
            }

            // Refresh current page
            if (document.getElementById('dashboard').classList.contains('active')) {
                loadDashboard();
            } else if (document.getElementById('home').classList.contains('active')) {
                loadData();
            }
        }

        // Car models data
        const carModels = {
            'تويوتا': ['كامري', 'كورولا', 'يارس', 'أفالون', 'هايلاندر', 'راف 4', 'برادو', 'لاند كروزر', 'سيكويا', 'تاكوما', 'تندرا'],
            'هوندا': ['أكورد', 'سيفيك', 'سيتي', 'سي آر في', 'بايلوت', 'ريدج لاين', 'أوديسي', 'فيت', 'إنسايت'],
            'نيسان': ['التيما', 'سنترا', 'مكسيما', 'باثفايندر', 'مورانو', 'روج', 'أرمادا', 'تيتان', 'فيرسا', 'جوك'],
            'هيونداي': ['إلنترا', 'سوناتا', 'أكسنت', 'توسان', 'سانتا في', 'كونا', 'باليسيد', 'فيلوستر', 'أيونيك'],
            'كيا': ['أوبتيما', 'فورتي', 'ريو', 'سورينتو', 'سبورتاج', 'تيلورايد', 'ستينجر', 'سول', 'نيرو'],
            'مازدا': ['مازدا 3', 'مازدا 6', 'مازدا 2', 'سي إكس 5', 'سي إكس 9', 'سي إكس 3', 'إم إكس 5'],
            'ميتسوبيشي': ['لانسر', 'أوتلاندر', 'إكليبس كروس', 'مونتيرو', 'ميراج', 'أي إس إكس'],
            'سوزوكي': ['سويفت', 'بالينو', 'فيتارا', 'جيمني', 'إرتيجا', 'سياز'],
            'فورد': ['فيوجن', 'فوكس', 'فييستا', 'إكسبلورر', 'إكسبيديشن', 'إف 150', 'موستانج', 'إيدج'],
            'شيفروليه': ['كروز', 'ماليبو', 'إمبالا', 'إكوينوكس', 'تاهو', 'سوبربان', 'سيلفرادو', 'كامارو'],
            'بي إم دبليو': ['الفئة الثالثة', 'الفئة الخامسة', 'الفئة السابعة', 'إكس 1', 'إكس 3', 'إكس 5', 'إكس 7'],
            'مرسيدس': ['الفئة سي', 'الفئة إي', 'الفئة إس', 'جي إل إيه', 'جي إل سي', 'جي إل إي', 'جي إل إس'],
            'أودي': ['إيه 3', 'إيه 4', 'إيه 6', 'إيه 8', 'كيو 3', 'كيو 5', 'كيو 7', 'كيو 8'],
            'فولكس واجن': ['جيتا', 'باسات', 'جولف', 'تيجوان', 'أطلس', 'أرتيون', 'بولو'],
            'لكزس': ['إي إس', 'آي إس', 'جي إس', 'إل إس', 'إن إكس', 'آر إكس', 'جي إكس', 'إل إكس'],
            'إنفينيتي': ['كيو 50', 'كيو 60', 'كيو إكس 50', 'كيو إكس 60', 'كيو إكس 80'],
            'أكورا': ['آي إل إكس', 'تي إل إكس', 'آر إل إكس', 'آر دي إكس', 'إم دي إكس'],
            'عام': ['عام']
        };

        // Update car models based on selected brand
        function updateCarModels() {
            const carBrand = document.getElementById('carBrand').value;
            const carModelSelect = document.getElementById('carModel');

            // Clear existing options
            carModelSelect.innerHTML = '<option value="">اختر الماركة</option>';

            if (carBrand && carModels[carBrand]) {
                carModels[carBrand].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    carModelSelect.appendChild(option);
                });
            }

            // Update product preview
            updateProductPreview();
        }

        // Update product preview
        function updateProductPreview() {
            const carBrand = document.getElementById('carBrand').value;
            const carModel = document.getElementById('carModel').value;
            const carYear = document.getElementById('carYear').value;
            const partNumber = document.getElementById('partNumber').value;
            const category = document.getElementById('productCategory').value;
            const productName = document.getElementById('productName').value;

            let generatedName = '';
            let generatedDescription = '';

            // Generate name based on inputs
            if (category) {
                generatedName += category + ' ';
            }

            if (carBrand && carBrand !== 'عام') {
                generatedName += carBrand + ' ';
            }

            if (carModel && carModel !== 'عام') {
                generatedName += carModel + ' ';
            }

            if (carYear && carYear !== 'عام') {
                generatedName += carYear + ' ';
            }

            if (productName) {
                generatedName += productName;
            } else {
                generatedName += 'قطعة غيار';
            }

            // Generate description
            generatedDescription = `قطعة غيار ${category || 'أصلية'}`;
            if (carBrand && carBrand !== 'عام') {
                generatedDescription += ` لسيارة ${carBrand}`;
            }
            if (carModel && carModel !== 'عام') {
                generatedDescription += ` ${carModel}`;
            }
            if (carYear && carYear !== 'عام') {
                generatedDescription += ` موديل ${carYear}`;
            }
            if (partNumber) {
                generatedDescription += ` - رقم القطعة: ${partNumber}`;
            }

            // Update preview
            document.getElementById('previewName').textContent = generatedName.trim() || 'سيتم إنشاؤه تلقائياً';
            document.getElementById('previewDescription').textContent = generatedDescription || 'سيتم إنشاؤه تلقائياً';

            // Auto-fill the product name if empty
            if (!document.getElementById('productName').value) {
                document.getElementById('productName').value = generatedName.trim();
            }

            // Auto-fill the description if empty
            if (!document.getElementById('productDescription').value) {
                document.getElementById('productDescription').value = generatedDescription;
            }
        }

        // Add event listeners for real-time preview updates
        function addProductFormListeners() {
            const fields = ['carBrand', 'carModel', 'carYear', 'partNumber', 'productCategory', 'productName'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', updateProductPreview);
                    field.addEventListener('input', updateProductPreview);
                }
            });
        }

        // Update edit car models based on selected brand
        function updateEditCarModels() {
            const carBrand = document.getElementById('editCarBrand')?.value;
            const carModelSelect = document.getElementById('editCarModel');

            if (!carModelSelect) return;

            // Store current selection
            const currentSelection = carModelSelect.value;

            // Clear existing options
            carModelSelect.innerHTML = '<option value="">اختر الماركة</option>';

            if (carBrand && carModels[carBrand]) {
                carModels[carBrand].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    if (model === currentSelection) {
                        option.selected = true;
                    }
                    carModelSelect.appendChild(option);
                });
            }

            // Update preview
            updateEditProductPreview();
        }

        // Update edit product preview
        function updateEditProductPreview() {
            const carBrand = document.getElementById('editCarBrand')?.value || '';
            const carModel = document.getElementById('editCarModel')?.value || '';
            const carYear = document.getElementById('editCarYear')?.value || '';
            const partNumber = document.getElementById('editPartNumber')?.value || '';
            const category = document.getElementById('editProductCategory')?.value || '';
            const productName = document.getElementById('editProductName')?.value || '';
            const productDescription = document.getElementById('editProductDescription')?.value || '';

            let generatedName = '';
            let generatedDescription = '';
            let carInfo = '';

            // Generate name based on inputs
            if (category) {
                generatedName += category + ' ';
            }

            if (carBrand && carBrand !== 'عام') {
                generatedName += carBrand + ' ';
                carInfo += carBrand;
            }

            if (carModel && carModel !== 'عام') {
                generatedName += carModel + ' ';
                carInfo += ' ' + carModel;
            }

            if (carYear && carYear !== 'عام') {
                generatedName += carYear + ' ';
                carInfo += ' ' + carYear;
            }

            // Use current name or generated name
            if (!productName && generatedName) {
                generatedName += 'قطعة غيار';
            } else if (productName) {
                generatedName = productName;
            }

            // Generate description
            if (!productDescription && (carBrand || category)) {
                generatedDescription = `قطعة غيار ${category || 'أصلية'}`;
                if (carBrand && carBrand !== 'عام') {
                    generatedDescription += ` لسيارة ${carBrand}`;
                }
                if (carModel && carModel !== 'عام') {
                    generatedDescription += ` ${carModel}`;
                }
                if (carYear && carYear !== 'عام') {
                    generatedDescription += ` موديل ${carYear}`;
                }
                if (partNumber) {
                    generatedDescription += ` - رقم القطعة: ${partNumber}`;
                }
            } else if (productDescription) {
                generatedDescription = productDescription;
            }

            // Update preview
            const previewName = document.getElementById('editPreviewName');
            const previewDescription = document.getElementById('editPreviewDescription');
            const previewCar = document.getElementById('editPreviewCar');

            if (previewName) previewName.textContent = generatedName || 'اسم المنتج';
            if (previewDescription) previewDescription.textContent = generatedDescription || 'وصف المنتج';
            if (previewCar) previewCar.textContent = carInfo.trim() || 'معلومات السيارة';
        }

        // Add event listeners for edit form
        function addEditProductFormListeners() {
            const fields = ['editCarBrand', 'editCarModel', 'editCarYear', 'editPartNumber', 'editProductCategory', 'editProductName', 'editProductDescription'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', updateEditProductPreview);
                    field.addEventListener('input', updateEditProductPreview);
                }
            });
        }

        // Update shop product with all fields
        function updateShopProduct(productId) {
            console.log('📝 بدء تحديث المنتج:', productId);

            const product = products.find(p => p.id === productId);
            if (!product) {
                showNotification('المنتج غير موجود', 'danger');
                return;
            }

            const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
            if (!shopDetail) {
                showNotification('هذا المنتج غير موجود في محلك', 'warning');
                return;
            }

            // Get all form data
            const formData = {
                carBrand: document.getElementById('editCarBrand').value,
                carModel: document.getElementById('editCarModel').value,
                carYear: document.getElementById('editCarYear').value,
                partNumber: document.getElementById('editPartNumber').value.trim(),
                category: document.getElementById('editProductCategory').value,
                name: document.getElementById('editProductName').value.trim(),
                description: document.getElementById('editProductDescription').value.trim(),
                price: parseFloat(document.getElementById('editProductPrice').value),
                quantity: parseInt(document.getElementById('editProductQuantity').value)
            };

            console.log('📊 بيانات النموذج:', formData);

            // Validation
            if (!formData.carBrand || !formData.carModel || !formData.carYear || !formData.name ||
                isNaN(formData.price) || formData.price < 0 || isNaN(formData.quantity) || formData.quantity < 0) {
                showNotification('يرجى ملء جميع الحقول المطلوبة بقيم صحيحة', 'warning');
                return;
            }

            // Update product information (affects all shops)
            product.brand = formData.carBrand;
            product.model = formData.carModel + (formData.carYear && formData.carYear !== 'عام' ? ' ' + formData.carYear : '');
            product.partNumber = formData.partNumber;
            product.category = formData.category;
            product.name = formData.name;
            product.description = formData.description;

            // Update shop-specific information
            shopDetail.price = formData.price;
            shopDetail.quantity = formData.quantity;

            // Update shop name in all product details if shop name changed
            shopDetail.shopName = currentUser.name;

            // Save to localStorage
            saveToStorage();
            console.log('💾 تم حفظ التحديثات');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            if (modal) {
                modal.hide();
            }

            showNotification(`تم تحديث المنتج "${formData.name}" بنجاح!`, 'success');

            // Refresh current page
            if (document.getElementById('dashboard').classList.contains('active')) {
                loadDashboard();
            } else if (document.getElementById('home').classList.contains('active')) {
                loadData();
            }

            console.log('✅ تم إكمال تحديث المنتج');
        }

        // Add all products to current shop
        function addAllProductsToShop() {
            if (!currentUser || currentUser.role !== 'shop') {
                showNotification('هذه الوظيفة متاحة للمحلات فقط', 'warning');
                return;
            }

            // Count products that will be added
            let productsToAdd = 0;
            let productsAlreadyExists = 0;

            products.forEach(product => {
                const existingShopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (!existingShopDetail) {
                    productsToAdd++;
                } else {
                    productsAlreadyExists++;
                }
            });

            if (productsToAdd === 0) {
                showNotification('جميع المنتجات موجودة بالفعل في محلك!', 'info');
                return;
            }

            // Show confirmation with details
            const confirmMessage = `هل تريد إضافة جميع الأصناف لمحلك؟\n\n` +
                `سيتم إضافة: ${productsToAdd} منتج جديد\n` +
                `موجود بالفعل: ${productsAlreadyExists} منتج\n\n` +
                `ملاحظة: ستبدأ جميع المنتجات الجديدة بكمية 0 وأسعار متوسطة، يمكنك تعديلها لاحقاً من لوحة التحكم.`;

            if (!confirm(confirmMessage)) return;

            let addedCount = 0;

            // Add missing products to the shop
            products.forEach(product => {
                const existingShopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (!existingShopDetail) {
                    // Calculate average price from existing shops for reference
                    const existingPrices = product.shopDetails.map(shop => shop.price);
                    const avgPrice = existingPrices.length > 0 ?
                        Math.round(existingPrices.reduce((sum, price) => sum + price, 0) / existingPrices.length) : 100;

                    // Add this shop to the product with default values
                    product.shopDetails.push({
                        shopName: currentUser.name,
                        shopId: currentUser.shopId,
                        price: avgPrice, // Use average price as starting point
                        quantity: 0 // Start with 0 quantity - shop owner will update
                    });

                    addedCount++;
                }
            });

            // Save to localStorage
            saveToStorage();

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            if (modal) {
                modal.hide();
            }

            // Show success message
            showNotification(`تم إضافة ${addedCount} منتج جديد لمحلك بنجاح!`, 'success');

            setTimeout(() => {
                showNotification('يمكنك الآن تعديل الأسعار والكميات من لوحة التحكم', 'info');
            }, 1500);

            setTimeout(() => {
                showNotification(`تفاصيل: أضيف ${addedCount} منتج، موجود مسبقاً ${productsAlreadyExists} منتج`, 'info');
            }, 2500);

            // Refresh dashboard if currently viewing
            if (document.getElementById('dashboard').classList.contains('active')) {
                setTimeout(() => {
                    loadDashboard();
                }, 500);
            }

            // Refresh home page data if viewing
            if (document.getElementById('home').classList.contains('active')) {
                setTimeout(() => {
                    loadData();
                }, 500);
            }
        }

        // Other functions
        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) {
                showNotification('المنتج غير موجود', 'danger');
                return;
            }

            const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
            const availableShops = product.shopDetails.filter(shop => shop.quantity > 0).length;
            const avgPrice = product.shopDetails.reduce((sum, shop) => sum + shop.price, 0) / product.shopDetails.length;

            // Extract car information
            const carBrand = product.brand || 'غير محدد';
            let carModel = 'غير محدد';
            let carYear = 'غير محدد';

            if (product.model && product.model.trim()) {
                const modelText = product.model.trim();
                const yearMatch = modelText.match(/\d{4}/);
                if (yearMatch) {
                    carYear = yearMatch[0];
                    carModel = modelText.replace(/\s*\d{4}\s*/, '').trim() || 'غير محدد';
                } else {
                    carModel = modelText;
                }
            }

            const modal = `
                <div class="modal fade" id="productDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-cog me-2"></i>
                                    تفاصيل المنتج: ${product.name}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Product Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات المنتج</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>الاسم:</strong> ${product.name}</p>
                                                <p><strong>رقم القطعة:</strong> ${product.partNumber || 'غير محدد'}</p>
                                                <p><strong>الفئة:</strong> ${product.category || 'غير محدد'}</p>
                                                <p><strong>الوصف:</strong> ${product.description || 'لا يوجد وصف'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0"><i class="fas fa-car me-2"></i>معلومات السيارة</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>نوع السيارة:</strong>
                                                    <span class="badge bg-primary">${carBrand}</span>
                                                </p>
                                                <p><strong>ماركة السيارة:</strong>
                                                    <span class="badge bg-info">${carModel}</span>
                                                </p>
                                                <p><strong>موديل السيارة:</strong>
                                                    <span class="badge bg-warning text-dark">${carYear}</span>
                                                </p>
                                                <p><strong>التوافق:</strong> ${carBrand} ${carModel} ${carYear}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Statistics -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white text-center">
                                            <div class="card-body">
                                                <h4>${product.shopDetails.length}</h4>
                                                <p class="mb-0">محل</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white text-center">
                                            <div class="card-body">
                                                <h4>${availableShops}</h4>
                                                <p class="mb-0">متوفر</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white text-center">
                                            <div class="card-body">
                                                <h4>${totalQuantity}</h4>
                                                <p class="mb-0">إجمالي الكمية</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-dark text-center">
                                            <div class="card-body">
                                                <h4>${Math.round(avgPrice)} ريال</h4>
                                                <p class="mb-0">متوسط السعر</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Shops Table -->
                                <h6><i class="fas fa-store me-2"></i>المحلات المتوفرة:</h6>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>المحل</th>
                                                <th>السعر</th>
                                                <th>الكمية</th>
                                                <th>القيمة الإجمالية</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${product.shopDetails.map(shop => {
                                                const totalValue = shop.price * shop.quantity;
                                                const isAvailable = shop.quantity > 0;

                                                return `
                                                    <tr>
                                                        <td><strong>${shop.shopName}</strong></td>
                                                        <td><span class="fw-bold text-primary">${shop.price} ريال</span></td>
                                                        <td>
                                                            <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                ${shop.quantity}
                                                            </span>
                                                        </td>
                                                        <td><strong>${Math.round(totalValue)} ريال</strong></td>
                                                        <td>
                                                            <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                ${isAvailable ? 'متوفر' : 'غير متوفر'}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                `;
                                            }).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('productDetailModal'));
            modalElement.show();
        }

        function deleteProduct(productId) {
            if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return;

            const productIndex = products.findIndex(p => p.id === productId);
            if (productIndex > -1) {
                const productName = products[productIndex].name;
                products.splice(productIndex, 1);

                // Save to localStorage
                saveToStorage();

                showNotification(`تم حذف المنتج "${productName}" بنجاح!`, 'success');

                // Refresh current page
                if (document.getElementById('dashboard').classList.contains('active')) {
                    loadDashboard();
                } else if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                }
            }
        }

        function editShopProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) {
                showNotification('المنتج غير موجود', 'danger');
                return;
            }

            const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
            if (!shopDetail) {
                showNotification('هذا المنتج غير موجود في محلك', 'warning');
                return;
            }

            // Extract current car information
            let currentCarBrand = product.brand || '';
            let currentCarModel = '';
            let currentCarYear = '';

            if (product.model && product.model.trim()) {
                const modelText = product.model.trim();
                const yearMatch = modelText.match(/\d{4}/);
                if (yearMatch) {
                    currentCarYear = yearMatch[0];
                    currentCarModel = modelText.replace(/\s*\d{4}\s*/, '').trim();
                } else {
                    currentCarModel = modelText;
                }
            }

            const modal = `
                <div class="modal fade" id="editProductModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل المنتج: ${product.name}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editProductForm">
                                    <!-- Car Information Section -->
                                    <div class="alert alert-primary">
                                        <h6><i class="fas fa-car me-2"></i>معلومات السيارة</h6>
                                        <p class="mb-0 small">يمكنك تعديل معلومات السيارة المرتبطة بهذا المنتج</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editCarBrand" onchange="updateEditCarModels()" required>
                                                    <option value="">اختر نوع السيارة</option>
                                                    <option value="تويوتا" ${currentCarBrand === 'تويوتا' ? 'selected' : ''}>تويوتا</option>
                                                    <option value="هوندا" ${currentCarBrand === 'هوندا' ? 'selected' : ''}>هوندا</option>
                                                    <option value="نيسان" ${currentCarBrand === 'نيسان' ? 'selected' : ''}>نيسان</option>
                                                    <option value="هيونداي" ${currentCarBrand === 'هيونداي' ? 'selected' : ''}>هيونداي</option>
                                                    <option value="كيا" ${currentCarBrand === 'كيا' ? 'selected' : ''}>كيا</option>
                                                    <option value="مازدا" ${currentCarBrand === 'مازدا' ? 'selected' : ''}>مازدا</option>
                                                    <option value="ميتسوبيشي" ${currentCarBrand === 'ميتسوبيشي' ? 'selected' : ''}>ميتسوبيشي</option>
                                                    <option value="سوزوكي" ${currentCarBrand === 'سوزوكي' ? 'selected' : ''}>سوزوكي</option>
                                                    <option value="فورد" ${currentCarBrand === 'فورد' ? 'selected' : ''}>فورد</option>
                                                    <option value="شيفروليه" ${currentCarBrand === 'شيفروليه' ? 'selected' : ''}>شيفروليه</option>
                                                    <option value="بي إم دبليو" ${currentCarBrand === 'بي إم دبليو' ? 'selected' : ''}>بي إم دبليو</option>
                                                    <option value="مرسيدس" ${currentCarBrand === 'مرسيدس' ? 'selected' : ''}>مرسيدس</option>
                                                    <option value="أودي" ${currentCarBrand === 'أودي' ? 'selected' : ''}>أودي</option>
                                                    <option value="فولكس واجن" ${currentCarBrand === 'فولكس واجن' ? 'selected' : ''}>فولكس واجن</option>
                                                    <option value="لكزس" ${currentCarBrand === 'لكزس' ? 'selected' : ''}>لكزس</option>
                                                    <option value="إنفينيتي" ${currentCarBrand === 'إنفينيتي' ? 'selected' : ''}>إنفينيتي</option>
                                                    <option value="أكورا" ${currentCarBrand === 'أكورا' ? 'selected' : ''}>أكورا</option>
                                                    <option value="عام" ${currentCarBrand === 'عام' ? 'selected' : ''}>عام (جميع السيارات)</option>
                                                </select>
                                                <label>نوع السيارة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editCarModel" required>
                                                    <option value="">اختر الماركة</option>
                                                </select>
                                                <label>الماركة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editCarYear" required>
                                                    <option value="">اختر الموديل</option>
                                                    <option value="2024" ${currentCarYear === '2024' ? 'selected' : ''}>2024</option>
                                                    <option value="2023" ${currentCarYear === '2023' ? 'selected' : ''}>2023</option>
                                                    <option value="2022" ${currentCarYear === '2022' ? 'selected' : ''}>2022</option>
                                                    <option value="2021" ${currentCarYear === '2021' ? 'selected' : ''}>2021</option>
                                                    <option value="2020" ${currentCarYear === '2020' ? 'selected' : ''}>2020</option>
                                                    <option value="2019" ${currentCarYear === '2019' ? 'selected' : ''}>2019</option>
                                                    <option value="2018" ${currentCarYear === '2018' ? 'selected' : ''}>2018</option>
                                                    <option value="2017" ${currentCarYear === '2017' ? 'selected' : ''}>2017</option>
                                                    <option value="2016" ${currentCarYear === '2016' ? 'selected' : ''}>2016</option>
                                                    <option value="2015" ${currentCarYear === '2015' ? 'selected' : ''}>2015</option>
                                                    <option value="2014" ${currentCarYear === '2014' ? 'selected' : ''}>2014</option>
                                                    <option value="2013" ${currentCarYear === '2013' ? 'selected' : ''}>2013</option>
                                                    <option value="2012" ${currentCarYear === '2012' ? 'selected' : ''}>2012</option>
                                                    <option value="2011" ${currentCarYear === '2011' ? 'selected' : ''}>2011</option>
                                                    <option value="2010" ${currentCarYear === '2010' ? 'selected' : ''}>2010</option>
                                                    <option value="2009" ${currentCarYear === '2009' ? 'selected' : ''}>2009</option>
                                                    <option value="2008" ${currentCarYear === '2008' ? 'selected' : ''}>2008</option>
                                                    <option value="2007" ${currentCarYear === '2007' ? 'selected' : ''}>2007</option>
                                                    <option value="2006" ${currentCarYear === '2006' ? 'selected' : ''}>2006</option>
                                                    <option value="2005" ${currentCarYear === '2005' ? 'selected' : ''}>2005</option>
                                                    <option value="2004" ${currentCarYear === '2004' ? 'selected' : ''}>2004</option>
                                                    <option value="2003" ${currentCarYear === '2003' ? 'selected' : ''}>2003</option>
                                                    <option value="2002" ${currentCarYear === '2002' ? 'selected' : ''}>2002</option>
                                                    <option value="2001" ${currentCarYear === '2001' ? 'selected' : ''}>2001</option>
                                                    <option value="2000" ${currentCarYear === '2000' ? 'selected' : ''}>2000</option>
                                                    <option value="عام" ${currentCarYear === 'عام' ? 'selected' : ''}>عام</option>
                                                </select>
                                                <label>الموديل (السنة) *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Product Information Section -->
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-cog me-2"></i>معلومات القطعة</h6>
                                        <p class="mb-0 small">تفاصيل قطعة الغيار</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="editPartNumber" value="${product.partNumber || ''}">
                                                <label>كود الصنف / رقم القطعة</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editProductCategory">
                                                    <option value="">اختر الفئة</option>
                                                    <option value="فلاتر" ${product.category === 'فلاتر' ? 'selected' : ''}>فلاتر</option>
                                                    <option value="فرامل" ${product.category === 'فرامل' ? 'selected' : ''}>فرامل</option>
                                                    <option value="زيوت" ${product.category === 'زيوت' ? 'selected' : ''}>زيوت</option>
                                                    <option value="كهرباء" ${product.category === 'كهرباء' ? 'selected' : ''}>كهرباء</option>
                                                    <option value="إطارات" ${product.category === 'إطارات' ? 'selected' : ''}>إطارات</option>
                                                    <option value="محرك" ${product.category === 'محرك' ? 'selected' : ''}>محرك</option>
                                                    <option value="تكييف" ${product.category === 'تكييف' ? 'selected' : ''}>تكييف</option>
                                                    <option value="عادم" ${product.category === 'عادم' ? 'selected' : ''}>عادم</option>
                                                    <option value="تعليق" ${product.category === 'تعليق' ? 'selected' : ''}>تعليق</option>
                                                    <option value="توجيه" ${product.category === 'توجيه' ? 'selected' : ''}>توجيه</option>
                                                    <option value="إضاءة" ${product.category === 'إضاءة' ? 'selected' : ''}>إضاءة</option>
                                                    <option value="أخرى" ${product.category === 'أخرى' ? 'selected' : ''}>أخرى</option>
                                                </select>
                                                <label>فئة القطعة</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="editProductName" value="${product.name}" required>
                                        <label>اسم الصنف *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="editProductDescription" style="height: 100px">${product.description || ''}</textarea>
                                        <label>وصف المنتج</label>
                                    </div>

                                    <!-- Pricing Section -->
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-dollar-sign me-2"></i>معلومات السعر والكمية في محلك</h6>
                                        <p class="mb-0 small">السعر والكمية الخاصة بمحلك فقط</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="editProductPrice" step="0.01" min="0" value="${shopDetail.price}" required>
                                                <label>السعر (ريال) *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="editProductQuantity" min="0" value="${shopDetail.quantity}" required>
                                                <label>الكمية المتوفرة *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Preview Section -->
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-eye me-2"></i>معاينة التحديث</h6>
                                        <div id="editProductPreview">
                                            <strong>اسم الصنف:</strong> <span id="editPreviewName">${product.name}</span><br>
                                            <strong>الوصف:</strong> <span id="editPreviewDescription">${product.description || 'لا يوجد وصف'}</span><br>
                                            <strong>السيارة:</strong> <span id="editPreviewCar">${currentCarBrand} ${currentCarModel} ${currentCarYear}</span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-warning" onclick="updateShopProduct(${productId})">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('editProductModal'));
            modalElement.show();

            // Initialize the form after modal is shown
            setTimeout(() => {
                // Set current car model in dropdown
                updateEditCarModels();
                if (currentCarModel) {
                    const carModelSelect = document.getElementById('editCarModel');
                    if (carModelSelect) {
                        carModelSelect.value = currentCarModel;
                    }
                }

                addEditProductFormListeners();
                updateEditProductPreview();
            }, 100);
        }

        // Quick update product from dashboard
        function quickUpdateProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
            if (!shopDetail) return;

            const priceInput = document.getElementById(`price_${productId}`);
            const quantityInput = document.getElementById(`quantity_${productId}`);
            const totalSpan = document.getElementById(`total_${productId}`);

            if (!priceInput || !quantityInput || !totalSpan) return;

            const newPrice = parseFloat(priceInput.value) || 0;
            const newQuantity = parseInt(quantityInput.value) || 0;

            // Update the product data
            shopDetail.price = newPrice;
            shopDetail.quantity = newQuantity;

            // Update total display
            const total = Math.round(newPrice * newQuantity);
            totalSpan.textContent = `${total} ريال`;

            // Update card border based on availability
            const card = priceInput.closest('.card');
            const badge = card.querySelector('.badge');

            if (newQuantity > 0) {
                card.classList.remove('border-warning');
                badge.className = 'badge bg-success';
                badge.textContent = 'متوفر';
            } else {
                card.classList.add('border-warning');
                badge.className = 'badge bg-warning text-dark';
                badge.textContent = 'غير متوفر';
            }

            // Save to localStorage
            saveToStorage();

            // Update dashboard statistics
            loadDashboard();

            showNotification(`تم تحديث "${product.name}" - السعر: ${newPrice} ريال، الكمية: ${newQuantity}`, 'success');
        }

        // Show bulk price update
        function showBulkPriceUpdate() {
            const percentage = prompt('تحديث جميع الأسعار بنسبة مئوية:\n(مثال: 10 للزيادة 10%، -5 للتقليل 5%)', '0');
            if (percentage === null) return;

            const percentageValue = parseFloat(percentage);
            if (isNaN(percentageValue)) {
                showNotification('يرجى إدخال رقم صحيح', 'warning');
                return;
            }

            let updatedCount = 0;
            products.forEach(product => {
                const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (shopDetail) {
                    const oldPrice = shopDetail.price;
                    const newPrice = Math.round(oldPrice * (1 + percentageValue / 100) * 100) / 100;
                    shopDetail.price = newPrice;
                    updatedCount++;

                    // Update input if visible
                    const priceInput = document.getElementById(`price_${product.id}`);
                    if (priceInput) {
                        priceInput.value = newPrice;
                        quickUpdateProduct(product.id);
                    }
                }
            });

            saveToStorage();
            loadDashboard();
            showNotification(`تم تحديث أسعار ${updatedCount} منتج بنسبة ${percentageValue}%`, 'success');
        }

        // Show bulk quantity update
        function showBulkQuantityUpdate() {
            const action = confirm('اختر العملية:\nموافق = تعيين كمية محددة لجميع المنتجات\nإلغاء = إضافة كمية لجميع المنتجات');

            if (action) {
                // Set specific quantity
                const quantity = prompt('تعيين الكمية لجميع المنتجات:', '0');
                if (quantity === null) return;

                const quantityValue = parseInt(quantity);
                if (isNaN(quantityValue) || quantityValue < 0) {
                    showNotification('يرجى إدخال رقم صحيح أكبر من أو يساوي 0', 'warning');
                    return;
                }

                let updatedCount = 0;
                products.forEach(product => {
                    const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                    if (shopDetail) {
                        shopDetail.quantity = quantityValue;
                        updatedCount++;

                        // Update input if visible
                        const quantityInput = document.getElementById(`quantity_${product.id}`);
                        if (quantityInput) {
                            quantityInput.value = quantityValue;
                            quickUpdateProduct(product.id);
                        }
                    }
                });

                saveToStorage();
                loadDashboard();
                showNotification(`تم تعيين كمية ${quantityValue} لـ ${updatedCount} منتج`, 'success');

            } else {
                // Add quantity
                const quantity = prompt('إضافة كمية لجميع المنتجات:', '0');
                if (quantity === null) return;

                const quantityValue = parseInt(quantity);
                if (isNaN(quantityValue)) {
                    showNotification('يرجى إدخال رقم صحيح', 'warning');
                    return;
                }

                let updatedCount = 0;
                products.forEach(product => {
                    const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                    if (shopDetail) {
                        shopDetail.quantity = Math.max(0, shopDetail.quantity + quantityValue);
                        updatedCount++;

                        // Update input if visible
                        const quantityInput = document.getElementById(`quantity_${product.id}`);
                        if (quantityInput) {
                            quantityInput.value = shopDetail.quantity;
                            quickUpdateProduct(product.id);
                        }
                    }
                });

                saveToStorage();
                loadDashboard();
                showNotification(`تم إضافة ${quantityValue} لكمية ${updatedCount} منتج`, 'success');
            }
        }

        // Show available products only
        function showAvailableProducts() {
            const availableOnly = confirm('عرض المنتجات المتوفرة فقط (الكمية > 0)؟');
            if (availableOnly) {
                // Hide products with 0 quantity
                document.querySelectorAll('[id^="quantity_"]').forEach(input => {
                    const card = input.closest('.col-lg-4');
                    if (parseInt(input.value) === 0) {
                        card.style.display = 'none';
                    } else {
                        card.style.display = 'block';
                    }
                });
                showNotification('يتم عرض المنتجات المتوفرة فقط', 'info');
            } else {
                // Show all products
                document.querySelectorAll('[id^="quantity_"]').forEach(input => {
                    const card = input.closest('.col-lg-4');
                    card.style.display = 'block';
                });
                showNotification('يتم عرض جميع المنتجات', 'info');
            }
        }

        function removeProductFromShop(productId) {
            if (!confirm('هل أنت متأكد من حذف هذا المنتج من محلك؟')) return;

            const product = products.find(p => p.id === productId);
            const shopDetailIndex = product.shopDetails.findIndex(shop => shop.shopId === currentUser.shopId);

            if (shopDetailIndex > -1) {
                product.shopDetails.splice(shopDetailIndex, 1);

                // If no shops have this product, remove it completely
                if (product.shopDetails.length === 0) {
                    const productIndex = products.findIndex(p => p.id === productId);
                    products.splice(productIndex, 1);
                }

                // Save to localStorage
                saveToStorage();

                showNotification(`تم حذف المنتج "${product.name}" من محلك بنجاح!`, 'success');
                loadDashboard();
            }
        }

        function viewShopProducts(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Get shop products
            const shopProducts = products.filter(product =>
                product.shopDetails.some(detail => detail.shopId === shopId)
            );

            // Calculate statistics
            const totalProducts = shopProducts.length;
            const totalValue = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                return sum + (shopDetail ? shopDetail.price * shopDetail.quantity : 0);
            }, 0);

            const totalQuantity = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                return sum + (shopDetail ? shopDetail.quantity : 0);
            }, 0);

            // Find username for this shop
            let shopUsername = '';
            for (let username in users) {
                if (users[username].shopId === shopId) {
                    shopUsername = username;
                    break;
                }
            }

            const modal = `
                <div class="modal fade" id="viewShopModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header bg-info text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-store me-2"></i>
                                    تفاصيل المحل: ${shop.name}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Shop Info -->
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6><i class="fas fa-info-circle me-2"></i>معلومات المحل:</h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>الاسم:</strong> ${shop.name}</p>
                                                        <p><strong>الوصف:</strong> ${shop.description}</p>
                                                        <p><strong>الهاتف:</strong> ${shop.phone}</p>
                                                        <p><strong>البريد:</strong> ${shop.email || 'غير محدد'}</p>
                                                        <p><strong>اسم المستخدم:</strong> ${shopUsername}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>العنوان:</strong> ${shop.address}</p>
                                                        <p><strong>المدينة:</strong> ${shop.city}</p>
                                                        <p><strong>التخصص:</strong> ${shop.specialty || 'غير محدد'}</p>
                                                        <p><strong>التقييم:</strong> ${shop.rating} ⭐</p>
                                                        <p><strong>تاريخ الانضمام:</strong> ${shop.joinDate}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-primary text-white text-center">
                                            <div class="card-body">
                                                <h4>${totalProducts}</h4>
                                                <p class="mb-0">إجمالي المنتجات</p>
                                            </div>
                                        </div>
                                        <div class="card bg-success text-white text-center mt-2">
                                            <div class="card-body">
                                                <h4>${totalQuantity}</h4>
                                                <p class="mb-0">إجمالي الكميات</p>
                                            </div>
                                        </div>
                                        <div class="card bg-warning text-white text-center mt-2">
                                            <div class="card-body">
                                                <h4>${Math.round(totalValue)} ريال</h4>
                                                <p class="mb-0">إجمالي القيمة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Products Table -->
                                <h6><i class="fas fa-box me-2"></i>منتجات المحل:</h6>
                                ${totalProducts === 0 ? `
                                    <div class="alert alert-info text-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد منتجات في هذا المحل
                                    </div>
                                ` : `
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>رقم القطعة</th>
                                                    <th>الفئة</th>
                                                    <th>السعر</th>
                                                    <th>الكمية</th>
                                                    <th>القيمة الإجمالية</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${shopProducts.map(product => {
                                                    const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                                                    const productValue = shopDetail.price * shopDetail.quantity;
                                                    const isAvailable = shopDetail.quantity > 0;

                                                    return `
                                                        <tr>
                                                            <td>
                                                                <strong>${product.name}</strong>
                                                                ${product.brand ? `<br><small class="text-muted">${product.brand}</small>` : ''}
                                                            </td>
                                                            <td><code>${product.partNumber || '-'}</code></td>
                                                            <td>
                                                                ${product.category ? `<span class="badge bg-secondary">${product.category}</span>` : '-'}
                                                            </td>
                                                            <td><strong>${shopDetail.price} ريال</strong></td>
                                                            <td>
                                                                <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                    ${shopDetail.quantity}
                                                                </span>
                                                            </td>
                                                            <td><strong>${Math.round(productValue)} ريال</strong></td>
                                                            <td>
                                                                <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                    ${isAvailable ? 'متوفر' : 'غير متوفر'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                ${currentUser && currentUser.role === 'admin' ? `
                                    <button type="button" class="btn btn-warning" onclick="editShop(${shopId}); bootstrap.Modal.getInstance(document.getElementById('viewShopModal')).hide();">
                                        <i class="fas fa-edit me-2"></i>
                                        تعديل المحل
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="deleteShop(${shopId}); bootstrap.Modal.getInstance(document.getElementById('viewShopModal')).hide();">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف المحل
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('viewShopModal'));
            modalElement.show();
        }

        function editShop(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Find the user for this shop
            let shopUser = null;
            let shopUsername = '';
            for (let username in users) {
                if (users[username].shopId === shopId) {
                    shopUser = users[username];
                    shopUsername = username;
                    break;
                }
            }

            const modal = `
                <div class="modal fade" id="editShopModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل بيانات المحل
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editShopForm">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>معرف المحل:</strong> ${shop.id} |
                                        <strong>تاريخ الانضمام:</strong> ${shop.joinDate}
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="editShopUsername" value="${shopUsername}" required>
                                                <label>اسم المستخدم *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="password" class="form-control" id="editShopPassword" value="${shopUser ? shopUser.password : ''}" required>
                                                <label>كلمة المرور *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="editShopName" value="${shop.name}" required>
                                        <label>اسم المحل *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="editShopDescription" style="height: 100px" required>${shop.description}</textarea>
                                        <label>وصف المحل *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="tel" class="form-control" id="editShopPhone" value="${shop.phone}" required>
                                                <label>رقم الهاتف *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="email" class="form-control" id="editShopEmail" value="${shop.email || ''}">
                                                <label>البريد الإلكتروني</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="editShopAddress" value="${shop.address}" required>
                                        <label>العنوان *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editShopCity" required>
                                                    <option value="">اختر المدينة</option>
                                                    <option value="الرياض" ${shop.city === 'الرياض' ? 'selected' : ''}>الرياض</option>
                                                    <option value="جدة" ${shop.city === 'جدة' ? 'selected' : ''}>جدة</option>
                                                    <option value="الدمام" ${shop.city === 'الدمام' ? 'selected' : ''}>الدمام</option>
                                                    <option value="مكة المكرمة" ${shop.city === 'مكة المكرمة' ? 'selected' : ''}>مكة المكرمة</option>
                                                    <option value="المدينة المنورة" ${shop.city === 'المدينة المنورة' ? 'selected' : ''}>المدينة المنورة</option>
                                                </select>
                                                <label>المدينة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="editShopSpecialty" value="${shop.specialty || ''}">
                                                <label>التخصص</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="editShopRating" value="${shop.rating}" min="0" max="5" step="0.1">
                                                <label>التقييم (0-5)</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-warning" onclick="updateShop(${shopId}, '${shopUsername}')">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('editShopModal'));
            modalElement.show();
        }

        // Update shop
        function updateShop(shopId, oldUsername) {
            const shopData = {
                username: document.getElementById('editShopUsername').value.trim(),
                password: document.getElementById('editShopPassword').value.trim(),
                name: document.getElementById('editShopName').value.trim(),
                description: document.getElementById('editShopDescription').value.trim(),
                phone: document.getElementById('editShopPhone').value.trim(),
                email: document.getElementById('editShopEmail').value.trim(),
                address: document.getElementById('editShopAddress').value.trim(),
                city: document.getElementById('editShopCity').value,
                specialty: document.getElementById('editShopSpecialty').value.trim(),
                rating: parseFloat(document.getElementById('editShopRating').value) || 0
            };

            // Validation
            if (!shopData.username || !shopData.password || !shopData.name || !shopData.description ||
                !shopData.phone || !shopData.address || !shopData.city) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if new username exists (if changed)
            if (shopData.username !== oldUsername && users[shopData.username]) {
                showNotification('اسم المستخدم الجديد موجود بالفعل', 'warning');
                return;
            }

            // Check if new shop name exists (if changed)
            const existingShop = shops.find(shop => shop.id !== shopId && shop.name.toLowerCase() === shopData.name.toLowerCase());
            if (existingShop) {
                showNotification('اسم المحل الجديد موجود بالفعل', 'warning');
                return;
            }

            // Find and update shop
            const shopIndex = shops.findIndex(shop => shop.id === shopId);
            if (shopIndex === -1) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Update shop data
            shops[shopIndex] = {
                ...shops[shopIndex],
                name: shopData.name,
                description: shopData.description,
                phone: shopData.phone,
                email: shopData.email,
                address: shopData.address,
                city: shopData.city,
                specialty: shopData.specialty,
                rating: shopData.rating
            };

            // Update user data
            if (oldUsername !== shopData.username) {
                // Remove old username
                delete users[oldUsername];

                // Add new username
                users[shopData.username] = {
                    password: shopData.password,
                    role: 'shop',
                    name: shopData.name,
                    shopId: shopId
                };
            } else {
                // Update existing user
                users[shopData.username] = {
                    ...users[shopData.username],
                    password: shopData.password,
                    name: shopData.name
                };
            }

            // Update product shop details with new shop name
            products.forEach(product => {
                product.shopDetails.forEach(shopDetail => {
                    if (shopDetail.shopId === shopId) {
                        shopDetail.shopName = shopData.name;
                    }
                });
            });

            // Save to localStorage
            saveToStorage();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('editShopModal')).hide();

            showNotification(`تم تحديث بيانات المحل "${shopData.name}" بنجاح!`, 'success');

            if (oldUsername !== shopData.username) {
                setTimeout(() => {
                    showNotification(`بيانات تسجيل الدخول الجديدة: ${shopData.username} / ${shopData.password}`, 'info');
                }, 1000);
            }

            // Refresh shops page if currently viewing
            if (document.getElementById('shops').classList.contains('active')) {
                loadShops();
            }

            // Refresh home page data
            if (document.getElementById('home').classList.contains('active')) {
                loadData();
            }

            // Update current user if editing own shop
            if (currentUser && currentUser.shopId === shopId) {
                currentUser.name = shopData.name;
                if (oldUsername !== shopData.username) {
                    currentUser.username = shopData.username;
                }
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;
            }
        }

        function deleteShop(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Count products that will be affected
            const shopProducts = products.filter(product =>
                product.shopDetails.some(detail => detail.shopId === shopId)
            );

            const productsToDelete = products.filter(product =>
                product.shopDetails.length === 1 && product.shopDetails[0].shopId === shopId
            );

            // Find username for this shop
            let shopUsername = '';
            for (let username in users) {
                if (users[username].shopId === shopId) {
                    shopUsername = username;
                    break;
                }
            }

            // Show detailed confirmation
            const confirmMessage = `هل أنت متأكد من حذف المحل "${shop.name}"؟\n\n` +
                `سيتم حذف:\n` +
                `• المحل وجميع بياناته\n` +
                `• حساب المستخدم "${shopUsername}"\n` +
                `• ${shopProducts.length} منتج من المحل\n` +
                `• ${productsToDelete.length} منتج نهائياً (غير متوفر في محلات أخرى)\n\n` +
                `هذا الإجراء لا يمكن التراجع عنه!`;

            if (!confirm(confirmMessage)) return;

            const shopIndex = shops.findIndex(s => s.id === shopId);
            if (shopIndex > -1) {
                const shopName = shops[shopIndex].name;
                let deletedProductsCount = 0;
                let removedProductsCount = 0;

                // Remove shop products and count deletions
                products.forEach(product => {
                    const initialLength = product.shopDetails.length;
                    product.shopDetails = product.shopDetails.filter(shop => shop.shopId !== shopId);

                    if (product.shopDetails.length < initialLength) {
                        removedProductsCount++;
                    }
                });

                // Remove empty products and count deletions
                for (let i = products.length - 1; i >= 0; i--) {
                    if (products[i].shopDetails.length === 0) {
                        products.splice(i, 1);
                        deletedProductsCount++;
                    }
                }

                // Remove shop
                shops.splice(shopIndex, 1);

                // Remove user
                let deletedUsername = '';
                for (let username in users) {
                    if (users[username].shopId === shopId) {
                        deletedUsername = username;
                        delete users[username];
                        break;
                    }
                }

                // Save to localStorage
                saveToStorage();

                // Show detailed success message
                showNotification(`تم حذف المحل "${shopName}" بنجاح!`, 'success');

                setTimeout(() => {
                    showNotification(
                        `تفاصيل الحذف: حُذف ${deletedProductsCount} منتج نهائياً، وأُزيل ${removedProductsCount} منتج من المحل، وحُذف المستخدم "${deletedUsername}"`,
                        'info'
                    );
                }, 1000);

                // Refresh current page
                if (document.getElementById('shops').classList.contains('active')) {
                    loadShops();
                }

                // Refresh home page data if viewing
                if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                }

                // Logout if current user's shop was deleted
                if (currentUser && currentUser.shopId === shopId) {
                    logout();
                    showNotification('تم تسجيل خروجك لأن محلك قد حُذف', 'warning');
                }
            }
        }

        // Update last update time
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const updateTimeElement = document.getElementById('updateTime');
            if (updateTimeElement) {
                updateTimeElement.textContent = timeString;
            }
        }

        // Force refresh data immediately
        function forceRefreshData() {
            console.log('⚡ تحديث فوري للبيانات...');

            // Show loading indicator
            const statusElement = document.getElementById('dataStatus');
            if (statusElement) {
                statusElement.className = 'badge bg-warning me-2';
                statusElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>يحدث...';
            }

            // Reload data from localStorage
            loadFromStorage();

            // Update current page
            const currentPage = document.querySelector('.page-content.active');
            if (currentPage) {
                const pageId = currentPage.id;
                if (pageId === 'home') {
                    loadData();
                    console.log('🏠 تم تحديث الصفحة الرئيسية فورياً');
                } else if (pageId === 'shops') {
                    loadShops();
                    console.log('🏪 تم تحديث صفحة المحلات فورياً');
                } else if (pageId === 'dashboard') {
                    loadDashboard();
                    console.log('📊 تم تحديث لوحة التحكم فورياً');
                }
            }

            // Update summary
            updateSummaryCards();

            // Update time
            updateLastUpdateTime();

            // Show success status
            setTimeout(() => {
                if (statusElement) {
                    statusElement.className = 'badge bg-success me-2';
                    statusElement.innerHTML = '<i class="fas fa-check me-1"></i>محدث';
                }
            }, 500);

            // Show notification
            const dataCount = `المحلات: ${shops.length}, المنتجات: ${products.length}`;
            showNotification(`تم التحديث الفوري! ${dataCount}`, 'success');
            console.log(`⚡ التحديث الفوري مكتمل: ${dataCount}`);
        }

        // Auto-refresh data periodically
        function autoRefreshData() {
            console.log('🔄 تحديث تلقائي للبيانات...');

            // Reload data from localStorage
            loadFromStorage();

            // Update current page
            const currentPage = document.querySelector('.page-content.active');
            if (currentPage) {
                const pageId = currentPage.id;
                if (pageId === 'home') {
                    loadData();
                    console.log('🏠 تم تحديث الصفحة الرئيسية');
                } else if (pageId === 'shops') {
                    loadShops();
                    console.log('🏪 تم تحديث صفحة المحلات');
                } else if (pageId === 'dashboard') {
                    loadDashboard();
                    console.log('📊 تم تحديث لوحة التحكم');
                }
            }

            // Update summary
            updateSummaryCards();

            // Update time
            updateLastUpdateTime();

            // Show subtle notification
            const dataCount = `المحلات: ${shops.length}, المنتجات: ${products.length}`;
            console.log(`📊 البيانات المحدثة تلقائياً: ${dataCount}`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل التطبيق...');

            // Initialize data from localStorage
            initializeData();
            console.log('💾 تم تحميل البيانات الأساسية');

            // Add form event listeners
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // Add search event listeners for real-time search with independent search
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                console.log('🔗 إضافة مستمع البحث النصي');
                searchInput.addEventListener('input', function() {
                    const value = this.value.trim();
                    if (value) {
                        clearOtherSearches('text');
                    }
                    // Perform search after a short delay to avoid too many calls
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        performSearch();
                    }, 300);
                });

                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const value = this.value.trim();
                        if (value) {
                            clearOtherSearches('text');
                        }
                        performSearch();
                    }
                });
            }

            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                console.log('🔗 إضافة مستمع فلتر الفئة');
                categoryFilter.addEventListener('change', function() {
                    if (this.value) {
                        clearOtherSearches('category');
                    }
                    performSearch();
                });
            }

            const brandFilter = document.getElementById('brandFilter');
            if (brandFilter) {
                brandFilter.addEventListener('change', function() {
                    loadModels();
                    performSearch();
                });
            }

            const modelFilter = document.getElementById('modelFilter');
            if (modelFilter) {
                modelFilter.addEventListener('change', function() {
                    performSearch();
                });
            }

            // Add event listeners for new search fields with independent search
            const partNumberSearch = document.getElementById('partNumberSearch');
            if (partNumberSearch) {
                console.log('🔗 إضافة مستمع البحث برقم القطعة');
                partNumberSearch.addEventListener('input', function() {
                    const value = this.value.trim();
                    if (value) {
                        clearOtherSearches('partNumber');
                    }
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        performSearch();
                    }, 300);
                });

                partNumberSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const value = this.value.trim();
                        if (value) {
                            clearOtherSearches('partNumber');
                        }
                        performSearch();
                    }
                });
            }

            const carBrandSearch = document.getElementById('carBrandSearch');
            if (carBrandSearch) {
                console.log('🔗 إضافة مستمع البحث بنوع السيارة');
                carBrandSearch.addEventListener('change', function() {
                    if (this.value) {
                        clearOtherSearches('car');
                    }
                    updateSearchCarModels();
                    performSearch();
                });
            }

            const carModelSearch = document.getElementById('carModelSearch');
            if (carModelSearch) {
                console.log('🔗 إضافة مستمع البحث بماركة السيارة');
                carModelSearch.addEventListener('change', function() {
                    if (this.value) {
                        clearOtherSearches('car');
                    }
                    performSearch();
                });
            }

            const carYearSearch = document.getElementById('carYearSearch');
            if (carYearSearch) {
                console.log('🔗 إضافة مستمع البحث بموديل السيارة');
                carYearSearch.addEventListener('change', function() {
                    if (this.value) {
                        clearOtherSearches('car');
                    }
                    performSearch();
                });
            }

            // Force show home page and load data immediately
            showPage('home');
            console.log('🏠 تم عرض الصفحة الرئيسية');

            // Load initial data with delay to ensure DOM is ready
            setTimeout(() => {
                loadData();
                updateLastUpdateTime();
                console.log('📊 تم تحميل البيانات الأولية');

                // Show welcome message
                const dataCount = `المحلات: ${shops.length}, المنتجات: ${products.length}`;
                showNotification(`مرحباً بك! تم تحميل البيانات (${dataCount})`, 'success');
            }, 100);

            // Set up auto-refresh every 30 seconds
            setInterval(autoRefreshData, 30000);
            console.log('⏰ تم تفعيل التحديث التلقائي كل 30 ثانية');

            // Set up visibility change listener to refresh when tab becomes active
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('👁️ التطبيق أصبح مرئياً - تحديث البيانات');
                    setTimeout(autoRefreshData, 500);
                }
            });

            // Test search functionality
            setTimeout(() => {
                testSearchFunctionality();
            }, 1000);

            console.log('✅ تم تحميل التطبيق بنجاح');
        });
    </script>
</body>
</html>
