#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قاعدة البيانات لتطبيق قطع غيار السيارات
Database module for Auto Parts Shop System
"""

import sqlite3

class Database:
    def __init__(self, db_path='auto_parts.db'):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'shop')),
                name TEXT NOT NULL,
                shop_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (shop_id) REFERENCES shops (id)
            )
        ''')
        
        # جدول المحلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shops (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT NOT NULL,
                phone TEXT NOT NULL,
                email TEXT,
                address TEXT NOT NULL,
                city TEXT NOT NULL,
                specialty TEXT,
                open_time TEXT,
                close_time TEXT,
                website TEXT,
                notes TEXT,
                rating REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                part_number TEXT,
                category TEXT,
                brand TEXT,
                model TEXT,
                image TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تفاصيل المنتجات في المحلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shop_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shop_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                price REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (shop_id) REFERENCES shops (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
                UNIQUE(shop_id, product_id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إدراج البيانات الأولية
        self.insert_initial_data()
    
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        # إدراج المدير
        cursor.execute('''
            INSERT INTO users (username, password, role, name)
            VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin123', 'admin', 'المدير'))
        
        # إدراج محل تجريبي
        cursor.execute('''
            INSERT INTO shops (name, description, phone, email, address, city, specialty, open_time, close_time, notes, rating)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'محل الأصيل لقطع غيار السيارات',
            'محل متخصص في قطع غيار السيارات اليابانية والكورية مع خبرة 15 سنة',
            '+966501234567',
            '<EMAIL>',
            'شارع الملك فهد، الرياض',
            'الرياض',
            'قطع غيار يابانية',
            '08:00',
            '22:00',
            'خدمة توصيل مجاني داخل الرياض',
            4.5
        ))
        
        shop_id = cursor.lastrowid
        
        # إدراج مستخدم المحل
        cursor.execute('''
            INSERT INTO users (username, password, role, name, shop_id)
            VALUES (?, ?, ?, ?, ?)
        ''', ('shop1', 'shop123', 'shop', 'محل الأصيل لقطع غيار السيارات', shop_id))
        
        # إدراج منتجات تجريبية
        products_data = [
            ('فلتر هواء تويوتا كامري', 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020', 'TOY-AF-001', 'فلاتر', 'تويوتا', 'كامري', ''),
            ('تيل فرامل هوندا أكورد', 'تيل فرامل عالي الجودة لسيارة هوندا أكورد', 'HON-BP-002', 'فرامل', 'هوندا', 'أكورد', ''),
            ('زيت محرك موبيل 1', 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات', 'MOB-OIL-003', 'زيوت', '', '', ''),
            ('بطارية نيسان التيما', 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير', 'NIS-BAT-004', 'كهرباء', 'نيسان', 'التيما', '')
        ]
        
        for product_data in products_data:
            cursor.execute('''
                INSERT INTO products (name, description, part_number, category, brand, model, image)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', product_data)
            
            product_id = cursor.lastrowid
            
            # ربط المنتج بالمحل مع السعر والكمية
            prices = [85.0, 120.0, 95.0, 280.0]
            quantities = [15, 8, 25, 5]
            price_index = len(products_data) - len(products_data) + products_data.index(product_data)
            
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (shop_id, product_id, prices[price_index], quantities[price_index]))
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء قاعدة البيانات وإدراج البيانات الأولية بنجاح!")

# إنشاء مثيل من قاعدة البيانات
db = Database()

if __name__ == '__main__':
    print("🗄️ إنشاء قاعدة البيانات...")
    db = Database()
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")
