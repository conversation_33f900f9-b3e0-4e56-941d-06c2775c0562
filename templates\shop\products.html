{% extends "base.html" %}

{% block title %}إدارة المنتجات - {{ shop.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold">
                        <i class="fas fa-cog me-2"></i>
                        إدارة المنتجات
                    </h2>
                    <p class="text-muted">إدارة منتجات محل {{ shop.name }}</p>
                </div>
                <div>
                    <a href="{{ url_for('shop_add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Products Table -->
    {% if products %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المنتجات ({{ products|length }})
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الفئة</th>
                            <th>الماركة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if product.image_url %}
                                    <img src="{{ product.image_url }}" class="me-3" style="width: 50px; height: 50px; object-fit: cover;" alt="{{ product.name }}">
                                    {% else %}
                                    <div class="me-3 bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ product.name }}</strong>
                                        {% if product.part_number %}
                                        <br><small class="text-muted">رقم القطعة: {{ product.part_number }}</small>
                                        {% endif %}
                                        {% if product.description %}
                                        <br><small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-primary">{{ product.price }} ريال</span>
                            </td>
                            <td>
                                {% if product.quantity > 0 %}
                                <span class="badge bg-success">{{ product.quantity }}</span>
                                {% else %}
                                <span class="badge bg-danger">نفد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if product.category %}
                                <span class="badge bg-info">{{ product.category.name }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if product.brand %}
                                <span class="badge bg-secondary">{{ product.brand.name }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if product.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">معطل</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('shop_edit_product', product_id=product.id) }}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete({{ product.id }}, '{{ product.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-cog fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد منتجات حالياً</h4>
            <p class="text-muted">ابدأ بإضافة منتجاتك لعرضها للعملاء</p>
            <a href="{{ url_for('shop_add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول منتج
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذا المنتج؟</h5>
                    <p class="text-muted" id="productName"></p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف المنتج
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(productId, productName) {
    document.getElementById('productName').textContent = productName;
    document.getElementById('deleteForm').action = '{{ url_for("shop_delete_product", product_id=0) }}'.replace('0', productId);
    
    var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
