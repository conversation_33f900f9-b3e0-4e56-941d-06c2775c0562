🚗 تطبيق قطع غيار السيارات مع قاعدة البيانات
===============================================

📋 خطوات التشغيل:

1️⃣ إنشاء قاعدة البيانات:
   - اضغط مرتين على: 1_create_database.bat
   - انتظر حتى ينتهي من إنشاء قاعدة البيانات
   - ستظهر رسالة "تم إنشاء قاعدة البيانات بنجاح!"

2️⃣ تشغيل التطبيق:
   - اضغط مرتين على: 2_run_web_app.bat
   - انتظر حتى يبدأ الخادم
   - افتح المتصفح واذهب إلى: http://localhost:5000/static/app.html

👤 حسابات تجريبية:
   - المدير: admin / admin123
   - المحل: shop1 / shop123

✨ الميزات:
   ✅ قاعدة بيانات SQLite حقيقية
   ✅ حفظ دائم للبيانات
   ✅ إضافة محلات جديدة (للمدير)
   ✅ إضافة منتجات جديدة (لأصحاب المحلات)
   ✅ تعديل وحذف المنتجات
   ✅ واجهة عربية جميلة

🔧 استكشاف الأخطاء:
   - إذا لم يعمل Python: تأكد من تثبيت Python
   - إذا ظهر خطأ Flask: شغل 1_create_database.bat أولاً
   - إذا لم تظهر البيانات: تحقق من وجود ملف auto_parts_simple.db

📁 الملفات المهمة:
   - auto_parts_simple.db: قاعدة البيانات
   - static/app.html: التطبيق الرئيسي
   - api.py: خادم Flask
   - simple_app.py: إنشاء قاعدة البيانات

🎯 اختبار النظام:
   1. سجل دخول كمدير
   2. اذهب لصفحة "المحلات"
   3. أضف محل جديد
   4. سجل خروج وسجل دخول بالمحل الجديد
   5. أضف منتجات للمحل
   6. تحقق من حفظ البيانات

===============================================
تم إنشاؤه بواسطة Augment Agent 🤖
