{% extends "base.html" %}

{% block title %}لوحة تحكم المحل - {{ shop.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="fw-bold">
                <i class="fas fa-store me-2"></i>
                لوحة تحكم المحل
            </h2>
            <p class="text-muted">مرحباً بك في لوحة تحكم محل {{ shop.name }}</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ total_products }}</h4>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cog fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ active_products }}</h4>
                            <p class="mb-0">المنتجات النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ "%.1f"|format(shop.rating) }}</h4>
                            <p class="mb-0">تقييم المحل</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('shop_add_product') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('shop_products_manage') }}" class="btn btn-info w-100">
                                <i class="fas fa-cog me-2"></i>
                                إدارة المنتجات
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('shop_products', shop_id=shop.id) }}" class="btn btn-success w-100">
                                <i class="fas fa-eye me-2"></i>
                                عرض المحل
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('shop_profile') }}" class="btn btn-warning w-100">
                                <i class="fas fa-edit me-2"></i>
                                تعديل البيانات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Shop Information -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات المحل
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>اسم المحل:</strong></td>
                            <td>{{ shop.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td>{{ shop.email or '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td>{{ shop.phone or '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ shop.address or '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ التسجيل:</strong></td>
                            <td>{{ shop.created_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                {% if shop.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">معطل</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ total_products }}</h4>
                            <small class="text-muted">إجمالي المنتجات</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ active_products }}</h4>
                            <small class="text-muted">المنتجات النشطة</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ shop.reviews|length }}</h4>
                            <small class="text-muted">التقييمات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ "%.1f"|format(shop.rating) }}</h4>
                            <small class="text-muted">متوسط التقييم</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Products -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث المنتجات
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_products %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td>
                                        <strong>{{ product.name }}</strong>
                                        {% if product.part_number %}
                                        <br><small class="text-muted">{{ product.part_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ product.price }} ريال</td>
                                    <td>
                                        {% if product.quantity > 0 %}
                                        <span class="badge bg-success">{{ product.quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">نفد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if product.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-danger">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('shop_edit_product', product_id=product.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('shop_products_manage') }}" class="btn btn-primary">
                            عرض جميع المنتجات
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد منتجات حالياً</h5>
                        <p class="text-muted">ابدأ بإضافة منتجاتك لعرضها للعملاء</p>
                        <a href="{{ url_for('shop_add_product') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول منتج
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
