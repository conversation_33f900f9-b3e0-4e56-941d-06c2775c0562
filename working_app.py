#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق قطع غيار السيارات - نسخة مبسطة تعمل بضمان
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
import sqlite3
import hashlib
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'auto-parts-secret-key-2024'

# إنشاء قاعدة البيانات
def init_db():
    conn = sqlite3.connect('simple_auto_parts.db')
    c = conn.cursor()
    
    # جدول المستخدمين
    c.execute('''CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT DEFAULT 'shop',
        email TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )''')
    
    # جدول المحلات
    c.execute('''CREATE TABLE IF NOT EXISTS shops (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        phone TEXT,
        address TEXT,
        user_id INTEGER,
        rating REAL DEFAULT 0.0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )''')
    
    # جدول المنتجات
    c.execute('''CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        quantity INTEGER DEFAULT 0,
        part_number TEXT,
        shop_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (shop_id) REFERENCES shops (id)
    )''')
    
    # إنشاء المدير الافتراضي
    admin_password = hashlib.md5('admin123'.encode()).hexdigest()
    c.execute('INSERT OR IGNORE INTO users (username, password, role, email) VALUES (?, ?, ?, ?)',
              ('admin', admin_password, 'admin', '<EMAIL>'))
    
    # إنشاء محل تجريبي
    shop_password = hashlib.md5('shop123'.encode()).hexdigest()
    c.execute('INSERT OR IGNORE INTO users (username, password, role, email) VALUES (?, ?, ?, ?)',
              ('shop1', shop_password, 'shop', '<EMAIL>'))
    
    # الحصول على ID المحل
    c.execute('SELECT id FROM users WHERE username = ?', ('shop1',))
    shop_user = c.fetchone()
    if shop_user:
        c.execute('INSERT OR IGNORE INTO shops (name, description, phone, user_id) VALUES (?, ?, ?, ?)',
                  ('محل الأصيل لقطع غيار السيارات', 'محل متخصص في قطع غيار السيارات', '+966501234567', shop_user[0]))
        
        # إضافة منتجات تجريبية
        c.execute('SELECT id FROM shops WHERE user_id = ?', (shop_user[0],))
        shop = c.fetchone()
        if shop:
            products = [
                ('فلتر هواء تويوتا كامري', 'فلتر هواء أصلي', 85.0, 15, 'TOY-AF-001'),
                ('تيل فرامل هوندا أكورد', 'تيل فرامل عالي الجودة', 120.0, 8, 'HON-BP-002'),
                ('زيت محرك موبيل 1', 'زيت محرك صناعي', 95.0, 25, 'MOB-OIL-003'),
                ('بطارية نيسان التيما', 'بطارية 12 فولت 70 أمبير', 280.0, 5, 'NIS-BAT-004'),
            ]
            for product in products:
                c.execute('INSERT OR IGNORE INTO products (name, description, price, quantity, part_number, shop_id) VALUES (?, ?, ?, ?, ?, ?)',
                          (*product, shop[0]))
    
    conn.commit()
    conn.close()

# الصفحة الرئيسية
@app.route('/')
def index():
    conn = sqlite3.connect('simple_auto_parts.db')
    c = conn.cursor()
    c.execute('''SELECT p.*, s.name as shop_name 
                 FROM products p 
                 JOIN shops s ON p.shop_id = s.id 
                 ORDER BY p.created_at DESC LIMIT 8''')
    products = c.fetchall()
    conn.close()
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 60px 0; }
        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.3s; }
        .card:hover { transform: translateY(-5px); }
        .product-image { height: 150px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-car me-2 text-primary"></i>
                قطع غيار السيارات
            </a>
            <div class="navbar-nav ms-auto">
                {% if session.user_id %}
                    <span class="nav-link">مرحباً {{ session.username }}</span>
                    <a class="nav-link" href="/logout">تسجيل الخروج</a>
                    {% if session.role == 'admin' %}
                        <a class="nav-link" href="/admin">لوحة المدير</a>
                    {% elif session.role == 'shop' %}
                        <a class="nav-link" href="/shop">لوحة المحل</a>
                    {% endif %}
                {% else %}
                    <a class="nav-link" href="/login">تسجيل الدخول</a>
                    <a class="nav-link" href="/direct-links">الروابط المباشرة</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">أفضل قطع غيار السيارات</h1>
            <p class="lead mb-4">اكتشف أفضل قطع غيار السيارات من محلات موثوقة</p>
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="ابحث عن قطعة غيار...">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">أحدث القطع</h2>
            <div class="row">
                {% for product in products %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card h-100">
                        <div class="product-image">
                            <i class="fas fa-cog fa-3x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">{{ product[1] }}</h6>
                            <p class="card-text text-muted small">{{ product[2][:50] }}...</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold text-primary">{{ product[3] }} ريال</span>
                                <small class="text-muted">{{ product[7] }}</small>
                            </div>
                            {% if product[5] %}
                            <small class="text-muted d-block">رقم القطعة: {{ product[5] }}</small>
                            {% endif %}
                            <div class="mt-2">
                                <span class="badge bg-success">متوفر ({{ product[4] }})</span>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-phone me-1"></i>
                                اتصل بالمحل
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Status Alert -->
    <div class="container mb-4">
        <div class="alert alert-success text-center">
            <h5><i class="fas fa-check-circle me-2"></i>التطبيق يعمل بنجاح!</h5>
            <p class="mb-0">جميع الوظائف متاحة - يمكنك تسجيل الدخول وإدارة المنتجات</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''', products=products, session=session)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = hashlib.md5(request.form['password'].encode()).hexdigest()
        
        conn = sqlite3.connect('simple_auto_parts.db')
        c = conn.cursor()
        c.execute('SELECT * FROM users WHERE username = ? AND password = ?', (username, password))
        user = c.fetchone()
        conn.close()
        
        if user:
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['role'] = user[3]
            flash('تم تسجيل الدخول بنجاح', 'success')
            
            if user[3] == 'admin':
                return redirect('/admin')
            else:
                return redirect('/shop')
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <h3 class="text-center mb-4">تسجيل الدخول</h3>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">{{ message }}</div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
                        </form>
                        
                        <div class="mt-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>الحسابات المتوفرة:</h6>
                                    <p class="mb-1"><strong>المدير:</strong> admin / admin123</p>
                                    <p class="mb-0"><strong>المحل:</strong> shop1 / shop123</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/" class="btn btn-outline-secondary">العودة للرئيسية</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    ''')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect('/')

# رابط مباشر للمدير
@app.route('/admin-direct')
def admin_direct():
    # تسجيل دخول تلقائي للمدير
    session['user_id'] = 1
    session['username'] = 'admin'
    session['role'] = 'admin'
    flash('تم تسجيل الدخول كمدير بنجاح', 'success')
    return redirect('/admin')

# رابط مباشر لصاحب المحل
@app.route('/shop-direct')
def shop_direct():
    # تسجيل دخول تلقائي لصاحب المحل
    session['user_id'] = 2
    session['username'] = 'shop1'
    session['role'] = 'shop'
    flash('تم تسجيل الدخول كصاحب محل بنجاح', 'success')
    return redirect('/shop')

# صفحة الروابط المباشرة
@app.route('/direct-links')
def direct_links():
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الروابط المباشرة - تطبيق قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-radius: 15px; }
        .btn-custom { border-radius: 10px; padding: 15px 30px; font-weight: 600; }
        .link-card { transition: transform 0.3s; }
        .link-card:hover { transform: translateY(-5px); }
        .copy-btn { cursor: pointer; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center text-white mb-5">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-link me-3"></i>
                        الروابط المباشرة
                    </h1>
                    <p class="lead">روابط سريعة للوصول المباشر للمدير وأصحاب المحلات</p>
                </div>

                <div class="row">
                    <!-- رابط المدير -->
                    <div class="col-md-6 mb-4">
                        <div class="card link-card h-100">
                            <div class="card-body text-center p-4">
                                <div class="mb-4">
                                    <i class="fas fa-user-shield fa-4x text-primary"></i>
                                </div>
                                <h3 class="card-title text-primary mb-3">رابط المدير</h3>
                                <p class="card-text text-muted mb-4">
                                    دخول مباشر للوحة تحكم المدير بدون تسجيل دخول
                                </p>

                                <div class="mb-4">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="adminLink"
                                               value="{{ request.url_root }}admin-direct" readonly>
                                        <button class="btn btn-outline-primary copy-btn"
                                                onclick="copyToClipboard('adminLink')" title="نسخ الرابط">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <a href="/admin-direct" class="btn btn-primary btn-custom">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        دخول مباشر كمدير
                                    </a>
                                    <button class="btn btn-outline-primary" onclick="copyToClipboard('adminLink')">
                                        <i class="fas fa-copy me-2"></i>
                                        نسخ رابط المدير
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        صلاحيات كاملة لإدارة النظام
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- رابط صاحب المحل -->
                    <div class="col-md-6 mb-4">
                        <div class="card link-card h-100">
                            <div class="card-body text-center p-4">
                                <div class="mb-4">
                                    <i class="fas fa-store fa-4x text-success"></i>
                                </div>
                                <h3 class="card-title text-success mb-3">رابط صاحب المحل</h3>
                                <p class="card-text text-muted mb-4">
                                    دخول مباشر للوحة تحكم المحل بدون تسجيل دخول
                                </p>

                                <div class="mb-4">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="shopLink"
                                               value="{{ request.url_root }}shop-direct" readonly>
                                        <button class="btn btn-outline-success copy-btn"
                                                onclick="copyToClipboard('shopLink')" title="نسخ الرابط">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <a href="/shop-direct" class="btn btn-success btn-custom">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        دخول مباشر كصاحب محل
                                    </a>
                                    <button class="btn btn-outline-success" onclick="copyToClipboard('shopLink')">
                                        <i class="fas fa-copy me-2"></i>
                                        نسخ رابط المحل
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        إدارة منتجات المحل فقط
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    معلومات مهمة
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">
                                            <i class="fas fa-user-shield me-2"></i>
                                            صلاحيات المدير:
                                        </h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>عرض جميع المنتجات</li>
                                            <li><i class="fas fa-check text-success me-2"></i>حذف أي منتج</li>
                                            <li><i class="fas fa-check text-success me-2"></i>إدارة المحلات</li>
                                            <li><i class="fas fa-check text-success me-2"></i>عرض الإحصائيات</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-success">
                                            <i class="fas fa-store me-2"></i>
                                            صلاحيات صاحب المحل:
                                        </h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>إضافة منتجات جديدة</li>
                                            <li><i class="fas fa-check text-success me-2"></i>تعديل منتجاته</li>
                                            <li><i class="fas fa-check text-success me-2"></i>حذف منتجاته</li>
                                            <li><i class="fas fa-check text-success me-2"></i>عرض إحصائيات محله</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>ملاحظة:</strong> هذه الروابط للاختبار والتطوير فقط. في البيئة الإنتاجية، يجب استخدام تسجيل الدخول العادي.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار إضافية -->
                <div class="text-center mt-4">
                    <a href="/" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <a href="/login" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول العادي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast للإشعارات -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="copyToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">تم النسخ</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                تم نسخ الرابط إلى الحافظة بنجاح!
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            document.execCommand('copy');

            // إظهار Toast
            const toast = new bootstrap.Toast(document.getElementById('copyToast'));
            toast.show();
        }
    </script>
</body>
</html>
    ''')

# لوحة تحكم المدير
@app.route('/admin')
def admin():
    if 'user_id' not in session or session.get('role') != 'admin':
        flash('غير مصرح لك بالوصول', 'error')
        return redirect('/login')
    
    conn = sqlite3.connect('simple_auto_parts.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM shops')
    total_shops = c.fetchone()[0]
    c.execute('SELECT COUNT(*) FROM products')
    total_products = c.fetchone()[0]
    c.execute('SELECT COUNT(*) FROM users')
    total_users = c.fetchone()[0]
    conn.close()
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة تحكم المدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body>
    <nav class="navbar navbar-dark bg-primary">
        <div class="container">
            <span class="navbar-brand">لوحة تحكم المدير</span>
            <a href="/" class="btn btn-light btn-sm">العودة للرئيسية</a>
        </div>
    </nav>
    
    <div class="container py-4">
        <h2 class="mb-4">مرحباً {{ session.username }}</h2>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_shops }}</h3>
                        <p>إجمالي المحلات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_products }}</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_users }}</h3>
                        <p>إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <h5>✅ لوحة تحكم المدير تعمل بنجاح!</h5>
            <p>يمكنك الآن إدارة النظام بالكامل</p>
        </div>
    </div>
</body>
</html>
    ''', session=session, total_shops=total_shops, total_products=total_products, total_users=total_users)

# لوحة تحكم المحل
@app.route('/shop')
def shop():
    if 'user_id' not in session or session.get('role') != 'shop':
        flash('غير مصرح لك بالوصول', 'error')
        return redirect('/login')
    
    conn = sqlite3.connect('simple_auto_parts.db')
    c = conn.cursor()
    c.execute('SELECT * FROM shops WHERE user_id = ?', (session['user_id'],))
    shop_info = c.fetchone()
    
    if shop_info:
        c.execute('SELECT COUNT(*) FROM products WHERE shop_id = ?', (shop_info[0],))
        total_products = c.fetchone()[0]
        c.execute('SELECT * FROM products WHERE shop_id = ? ORDER BY created_at DESC LIMIT 5', (shop_info[0],))
        recent_products = c.fetchall()
    else:
        total_products = 0
        recent_products = []
    
    conn.close()
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة تحكم المحل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body>
    <nav class="navbar navbar-dark bg-success">
        <div class="container">
            <span class="navbar-brand">لوحة تحكم المحل</span>
            <a href="/" class="btn btn-light btn-sm">العودة للرئيسية</a>
        </div>
    </nav>
    
    <div class="container py-4">
        <h2 class="mb-4">مرحباً {{ session.username }}</h2>
        
        {% if shop_info %}
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>معلومات المحل</h5>
                        <p><strong>الاسم:</strong> {{ shop_info[1] }}</p>
                        <p><strong>الوصف:</strong> {{ shop_info[2] or 'غير محدد' }}</p>
                        <p><strong>الهاتف:</strong> {{ shop_info[3] or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_products }}</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>أحدث المنتجات</h5>
            </div>
            <div class="card-body">
                {% if recent_products %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>رقم القطعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td>{{ product[1] }}</td>
                                    <td>{{ product[3] }} ريال</td>
                                    <td>{{ product[4] }}</td>
                                    <td>{{ product[5] or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد منتجات حالياً</p>
                {% endif %}
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5>✅ لوحة تحكم المحل تعمل بنجاح!</h5>
            <p>يمكنك الآن إدارة منتجاتك</p>
        </div>
        
        {% else %}
        <div class="alert alert-warning">
            <h5>⚠️ لم يتم العثور على بيانات المحل</h5>
            <p>يرجى التواصل مع المدير لإعداد محلك</p>
        </div>
        {% endif %}
    </div>
</body>
</html>
    ''', session=session, shop_info=shop_info, total_products=total_products, recent_products=recent_products)

if __name__ == '__main__':
    print("🔧 إنشاء قاعدة البيانات...")
    init_db()
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    print()
    print("🚀 تشغيل التطبيق...")
    print("🌐 الصفحة الرئيسية: http://localhost:5000")
    print("🔗 الروابط المباشرة: http://localhost:5000/direct-links")
    print()
    print("📋 الروابط المباشرة:")
    print("👤 رابط المدير: http://localhost:5000/admin-direct")
    print("🏪 رابط المحل: http://localhost:5000/shop-direct")
    print()
    print("🔑 تسجيل الدخول العادي:")
    print("👤 المدير: admin / admin123")
    print("🏪 المحل: shop1 / shop123")
    print()
    print("🛑 لإيقاف التطبيق: اضغط Ctrl+C")
    print("-" * 60)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف التطبيق بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
