@echo off
chcp 65001 >nul
title تثبيت وتشغيل تطبيق قطع غيار السيارات

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █  🚗 تثبيت وتشغيل تطبيق قطع غيار السيارات                   █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 جاري فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org/downloads
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version
echo.

echo 📦 جاري تثبيت المتطلبات الأساسية...
echo    هذا قد يستغرق دقيقة...

python -m pip install --quiet --upgrade pip
python -m pip install --quiet Flask==2.3.3
python -m pip install --quiet Flask-SQLAlchemy==3.0.5
python -m pip install --quiet Werkzeug==2.3.7

if errorlevel 1 (
    echo ⚠️ مشكلة في التثبيت، جاري المحاولة بطريقة أخرى...
    python -m pip install Flask Flask-SQLAlchemy Werkzeug
)

echo ✅ تم تثبيت المتطلبات
echo.

echo 🧪 جاري اختبار التطبيق...
python test_app.py

if errorlevel 1 (
    echo ❌ فشل في تشغيل التطبيق التجريبي
    echo 🔄 جاري المحاولة مع التطبيق الكامل...
    python app.py
)

pause
