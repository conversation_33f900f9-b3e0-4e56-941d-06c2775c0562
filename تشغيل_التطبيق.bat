@echo off
chcp 65001 >nul
title 🚗 تطبيق ربط محلات قطع غيار السيارات

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █  🚗 تطبيق ربط محلات قطع غيار السيارات                      █
echo █     Auto Parts Shops Connection System                      █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 جاري فحص النظام...

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Python من الرابط التالي:
    echo    https://python.org/downloads
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📦 جاري تحضير التطبيق...

REM تثبيت المتطلبات الأساسية
python -m pip install --quiet --upgrade pip
python -m pip install --quiet Flask Flask-SQLAlchemy Werkzeug

if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون هناك مشكلة في تثبيت المتطلبات
    echo سيتم المحاولة مع الإعدادات الافتراضية...
)

echo ✅ تم تحضير التطبيق
echo.

echo 🚀 جاري تشغيل التطبيق...
echo.
echo ████████████████████████████████████████████████████████████████
echo █                      معلومات مهمة                          █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🌐 رابط التطبيق: http://localhost:5000
echo 🌐 رابط بديل: http://127.0.0.1:5000
echo.
echo 👤 حساب المدير:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🏪 محل تجريبي:
echo    اسم المستخدم: shop1
echo    كلمة المرور: shop123
echo.
echo 📋 الميزات المتوفرة:
echo    ✅ صفحة رئيسية تعرض أحدث القطع
echo    ✅ نظام تسجيل دخول للمحلات والمدير
echo    ✅ لوحة تحكم للمحلات لإدارة المنتجات
echo    ✅ لوحة تحكم للمدير لإدارة المحلات
echo    ✅ نظام بحث متقدم حسب الماركة والموديل
echo    ✅ نظام تقييم للمحلات والمنتجات
echo    ✅ إدارة الأسعار والكميات لكل قطعة
echo.
echo 🛑 لإيقاف التطبيق: اضغط Ctrl+C
echo.
echo ████████████████████████████████████████████████████████████████
echo.

REM تشغيل التطبيق
python quick_start.py

echo.
echo ████████████████████████████████████████████████████████████████
echo █                   شكراً لاستخدام التطبيق                    █
echo ████████████████████████████████████████████████████████████████
echo.
pause
