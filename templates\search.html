{% extends "base.html" %}

{% block title %}البحث - نظام ربط محلات قطع غيار السيارات{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث المتقدم
            </h5>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث العام</label>
                        <input type="text" name="q" class="form-control" placeholder="اسم القطعة..." value="{{ query }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الماركة</label>
                        <select name="brand" class="form-select">
                            <option value="">جميع الماركات</option>
                            {% for brand in brands %}
                            <option value="{{ brand.name }}" {% if selected_brand == brand.name %}selected{% endif %}>
                                {{ brand.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الفئة</label>
                        <select name="category" class="form-select">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                            <option value="{{ category.name }}" {% if selected_category == category.name %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">رقم القطعة</label>
                        <input type="text" name="part_number" class="form-control" placeholder="رقم القطعة..." value="{{ part_number }}">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="{{ url_for('search') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            مسح الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>نتائج البحث</h4>
                <span class="text-muted">{{ products|length }} منتج</span>
            </div>
        </div>
    </div>

    {% if products %}
    <div class="row">
        {% for product in products %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card product-card h-100">
                {% if product.image_url %}
                    <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                {% else %}
                    <div class="card-img-top product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-cog fa-3x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="card-body">
                    <h6 class="card-title">{{ product.name }}</h6>
                    <p class="card-text text-muted small">{{ product.description[:60] }}{% if product.description|length > 60 %}...{% endif %}</p>
                    
                    <div class="mb-2">
                        {% if product.brand %}
                        <span class="badge bg-info">{{ product.brand.name }}</span>
                        {% endif %}
                        {% if product.category %}
                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-primary fs-5">{{ product.price }} ريال</span>
                        <small class="text-muted">{{ product.shop.name }}</small>
                    </div>
                    
                    {% if product.part_number %}
                    <small class="text-muted d-block">رقم القطعة: {{ product.part_number }}</small>
                    {% endif %}
                    
                    <div class="mt-2">
                        {% if product.quantity > 0 %}
                        <span class="badge bg-success">متوفر ({{ product.quantity }})</span>
                        {% else %}
                        <span class="badge bg-danger">غير متوفر</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="row g-2">
                        <div class="col-6">
                            <button class="btn btn-primary btn-sm w-100" onclick="contactShop('{{ product.shop.phone }}', '{{ product.shop.name }}')">
                                <i class="fas fa-phone"></i>
                                اتصل
                            </button>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('shop_products', shop_id=product.shop.id) }}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-store"></i>
                                المحل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد نتائج</h4>
        <p class="text-muted">جرب تغيير معايير البحث أو مسح الفلاتر</p>
    </div>
    {% endif %}
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تواصل مع المحل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                <h4 id="shopName"></h4>
                <p class="lead" id="shopPhone"></p>
                <p class="text-muted">يمكنك الاتصال مباشرة بالمحل للاستفسار عن المنتج</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a id="callButton" href="#" class="btn btn-primary">
                    <i class="fas fa-phone me-1"></i>
                    اتصل الآن
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function contactShop(phone, shopName) {
    document.getElementById('shopName').textContent = shopName;
    document.getElementById('shopPhone').textContent = phone;
    document.getElementById('callButton').href = 'tel:' + phone;
    
    var modal = new bootstrap.Modal(document.getElementById('contactModal'));
    modal.show();
}
</script>
{% endblock %}
