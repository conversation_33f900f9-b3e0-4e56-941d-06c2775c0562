<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض قاعدة البيانات - تطبيق قطع غيار السيارات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .badge-custom {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-success {
            background-color: #28a745;
        }
        
        .status-error {
            background-color: #dc3545;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🗄️ عارض قاعدة البيانات</h1>
            <p class="lead mb-4">تطبيق قطع غيار السيارات مع قاعدة بيانات SQLite</p>
            <div class="alert alert-light d-inline-block">
                <h5><i class="fas fa-database me-2"></i>قاعدة البيانات جاهزة!</h5>
                <p class="mb-0">جميع البيانات محفوظة بشكل دائم</p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Database Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-server me-2"></i>
                            حالة قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="status-indicator status-success"></div>
                                    <strong>SQLite</strong>
                                    <p class="text-muted mb-0">قاعدة البيانات</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="status-indicator status-success"></div>
                                    <strong>4 جداول</strong>
                                    <p class="text-muted mb-0">الهيكل</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="status-indicator status-success"></div>
                                    <strong>بيانات أولية</strong>
                                    <p class="text-muted mb-0">محل + منتجات</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="status-indicator status-success"></div>
                                    <strong>حفظ دائم</strong>
                                    <p class="text-muted mb-0">لا فقدان للبيانات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Schema -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            هيكل قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-users me-2"></i>جدول المستخدمين (users)</h6>
                                <ul class="list-unstyled">
                                    <li><code>id</code> - المعرف الفريد</li>
                                    <li><code>username</code> - اسم المستخدم</li>
                                    <li><code>password</code> - كلمة المرور</li>
                                    <li><code>role</code> - الدور (admin/shop)</li>
                                    <li><code>name</code> - الاسم</li>
                                    <li><code>shop_id</code> - معرف المحل</li>
                                </ul>
                                
                                <h6><i class="fas fa-store me-2"></i>جدول المحلات (shops)</h6>
                                <ul class="list-unstyled">
                                    <li><code>id</code> - المعرف الفريد</li>
                                    <li><code>name</code> - اسم المحل</li>
                                    <li><code>description</code> - الوصف</li>
                                    <li><code>phone</code> - رقم الهاتف</li>
                                    <li><code>address</code> - العنوان</li>
                                    <li><code>city</code> - المدينة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-box me-2"></i>جدول المنتجات (products)</h6>
                                <ul class="list-unstyled">
                                    <li><code>id</code> - المعرف الفريد</li>
                                    <li><code>name</code> - اسم المنتج</li>
                                    <li><code>description</code> - الوصف</li>
                                    <li><code>part_number</code> - رقم القطعة</li>
                                    <li><code>category</code> - الفئة</li>
                                    <li><code>brand</code> - الماركة</li>
                                </ul>
                                
                                <h6><i class="fas fa-link me-2"></i>جدول ربط المحلات والمنتجات (shop_products)</h6>
                                <ul class="list-unstyled">
                                    <li><code>shop_id</code> - معرف المحل</li>
                                    <li><code>product_id</code> - معرف المنتج</li>
                                    <li><code>price</code> - السعر</li>
                                    <li><code>quantity</code> - الكمية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Data -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            البيانات الأولية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>👤 المستخدمين</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>كلمة المرور</th>
                                                <th>الدور</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>admin</code></td>
                                                <td><code>admin123</code></td>
                                                <td><span class="badge bg-danger">مدير</span></td>
                                            </tr>
                                            <tr>
                                                <td><code>shop1</code></td>
                                                <td><code>shop123</code></td>
                                                <td><span class="badge bg-success">محل</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <h6>🏪 المحلات</h6>
                                <div class="alert alert-light">
                                    <strong>محل الأصيل لقطع غيار السيارات</strong><br>
                                    <small class="text-muted">
                                        📞 +966501234567<br>
                                        📍 شارع الملك فهد، الرياض<br>
                                        ⭐ تقييم: 4.5/5
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📦 المنتجات (4 منتجات)</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>السعر</th>
                                                <th>الكمية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>فلتر هواء تويوتا كامري</td>
                                                <td>85 ريال</td>
                                                <td>15</td>
                                            </tr>
                                            <tr>
                                                <td>تيل فرامل هوندا أكورد</td>
                                                <td>120 ريال</td>
                                                <td>8</td>
                                            </tr>
                                            <tr>
                                                <td>زيت محرك موبيل 1</td>
                                                <td>95 ريال</td>
                                                <td>25</td>
                                            </tr>
                                            <tr>
                                                <td>بطارية نيسان التيما</td>
                                                <td>280 ريال</td>
                                                <td>5</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2"></i>
                            كيفية تشغيل التطبيق
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🚀 الطريقة الأولى: التطبيق البسيط</h6>
                                <div class="code-block">
                                    <strong>Windows:</strong><br>
                                    اضغط مرتين على: <code>run_database_app.bat</code><br><br>
                                    
                                    <strong>أو من سطر الأوامر:</strong><br>
                                    <code>python simple_app.py</code>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ماذا يفعل:</strong><br>
                                    • ينشئ قاعدة البيانات<br>
                                    • يدرج البيانات الأولية<br>
                                    • يختبر إضافة محل ومنتج جديد<br>
                                    • يعرض جميع البيانات
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🌐 الطريقة الثانية: تطبيق الويب</h6>
                                <div class="code-block">
                                    <strong>تثبيت المتطلبات:</strong><br>
                                    <code>pip install Flask Flask-CORS</code><br><br>
                                    
                                    <strong>تشغيل الخادم:</strong><br>
                                    <code>python start_app.py</code><br><br>
                                    
                                    <strong>فتح التطبيق:</strong><br>
                                    <code>http://localhost:5000/static/app.html</code>
                                </div>
                                
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>المميزات:</strong><br>
                                    • واجهة ويب تفاعلية<br>
                                    • API متكامل<br>
                                    • حفظ فوري للبيانات<br>
                                    • تسجيل دخول وإدارة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center py-4">
            <div class="alert alert-primary">
                <h5><i class="fas fa-rocket me-2"></i>جاهز للتشغيل!</h5>
                <p class="mb-0">
                    قاعدة البيانات جاهزة مع جميع الجداول والبيانات الأولية.<br>
                    اختر الطريقة المناسبة لك وابدأ التشغيل!
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
