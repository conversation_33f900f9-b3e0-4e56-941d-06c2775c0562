<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق قطع غيار السيارات - تفاعلي كامل</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
        }
        
        .product-card {
            transition: transform 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات - تفاعلي كامل
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="resetData()" title="إعادة تعيين البيانات">
                    <i class="fas fa-redo text-warning"></i>
                </a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showPage('dashboard')" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section text-center" id="heroSection">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
            <p class="lead mb-4">تطبيق تفاعلي كامل مع حفظ دائم للبيانات!</p>
            <div class="alert alert-light d-inline-block">
                <h5><i class="fas fa-save me-2"></i>حفظ دائم</h5>
                <p class="mb-0">جميع البيانات محفوظة في localStorage - لا تضيع عند الإغلاق!</p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Home Page -->
        <div id="home" class="page-content active">
            <!-- Products Table -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول قطع الغيار والمحلات
                    </h5>
                    <div>
                        <span class="badge bg-success me-2" id="dataStatus">
                            <i class="fas fa-save me-1"></i>
                            محفوظ
                        </span>
                        <button class="btn btn-light btn-sm" onclick="loadData()">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 25%;">اسم القطعة</th>
                                    <th style="width: 15%;">رقم القطعة</th>
                                    <th style="width: 35%;">المحلات والأسعار</th>
                                    <th style="width: 15%;">أقل سعر</th>
                                    <th style="width: 10%;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- البيانات ستحمل هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white text-center">
                        <div class="card-body">
                            <h4 id="totalProducts">4</h4>
                            <p class="mb-0">إجمالي القطع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <h4 id="totalShops">1</h4>
                            <p class="mb-0">إجمالي المحلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <h4 id="totalQuantity">53</h4>
                            <p class="mb-0">إجمالي الكميات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white text-center">
                        <div class="card-body">
                            <h4 id="averagePrice">145 ريال</h4>
                            <p class="mb-0">متوسط الأسعار</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shops Page -->
        <div id="shops" class="page-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">جميع المحلات</h2>
                <button class="btn btn-success" onclick="showAddShopModal()" id="addShopBtn">
                    <i class="fas fa-plus me-2"></i>
                    إضافة محل جديد
                </button>
            </div>
            
            <div class="row" id="shopsContainer">
                <!-- المحلات ستظهر هنا -->
            </div>
        </div>

        <!-- Login Page -->
        <div id="login" class="page-content">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="loginForm">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                                    <label for="username">اسم المستخدم</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                                    <label for="password">كلمة المرور</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    دخول
                                </button>
                                
                                <div id="loginAlert" class="alert" style="display: none;"></div>
                            </form>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>حسابات تجريبية:</h6>
                                <p class="mb-1"><strong>المدير:</strong> admin / admin123</p>
                                <p class="mb-0"><strong>المحل:</strong> shop1 / shop123</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboard" class="page-content">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h2 class="mb-3" id="dashboardTitle">لوحة التحكم</h2>
                            <p class="lead mb-0" id="dashboardMessage">مرحباً بك</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardProducts">0</h3>
                            <p class="mb-0">المنتجات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardShops">0</h3>
                            <p class="mb-0">المحلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white text-center">
                        <div class="card-body">
                            <h3 id="dashboardValue">0</h3>
                            <p class="mb-0">إجمالي القيمة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">إدارة المنتجات</h5>
                    <button class="btn btn-success" onclick="showAddProductModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </button>
                </div>
                <div class="card-body">
                    <div class="row" id="dashboardProductsContainer">
                        <!-- منتجات المحل ستظهر هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modalsContainer"></div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentUser = null;
        let nextShopId = 2;
        let nextProductId = 5;

        // Default data
        const defaultUsers = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل لقطع غيار السيارات', shopId: 1 }
        };

        const defaultShops = [
            {
                id: 1,
                name: 'محل الأصيل لقطع غيار السيارات',
                description: 'محل متخصص في قطع غيار السيارات اليابانية والكورية',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'شارع الملك فهد، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار يابانية',
                rating: 4.5,
                joinDate: '2024-01-01'
            }
        ];

        const defaultProducts = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020',
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 85.0, quantity: 15 }]
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد',
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 120.0, quantity: 8 }]
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات',
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: 'موبيل',
                model: '',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 95.0, quantity: 25 }]
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير',
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما',
                shopDetails: [{ shopName: 'محل الأصيل لقطع غيار السيارات', shopId: 1, price: 280.0, quantity: 5 }]
            }
        ];

        // Working data (loaded from localStorage or defaults)
        let users = {};
        let shops = [];
        let products = [];

        // LocalStorage functions
        function saveToStorage() {
            try {
                localStorage.setItem('autoparts_users', JSON.stringify(users));
                localStorage.setItem('autoparts_shops', JSON.stringify(shops));
                localStorage.setItem('autoparts_products', JSON.stringify(products));
                localStorage.setItem('autoparts_nextShopId', nextShopId.toString());
                localStorage.setItem('autoparts_nextProductId', nextProductId.toString());
                localStorage.setItem('autoparts_lastSaved', new Date().toISOString());

                // Update status indicator
                const statusElement = document.getElementById('dataStatus');
                if (statusElement) {
                    statusElement.className = 'badge bg-success me-2';
                    statusElement.innerHTML = '<i class="fas fa-save me-1"></i>محفوظ';
                }

                console.log('✅ تم حفظ البيانات في localStorage');
            } catch (error) {
                console.error('❌ خطأ في حفظ البيانات:', error);

                // Update status indicator
                const statusElement = document.getElementById('dataStatus');
                if (statusElement) {
                    statusElement.className = 'badge bg-danger me-2';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>خطأ';
                }

                showNotification('خطأ في حفظ البيانات', 'warning');
            }
        }

        function loadFromStorage() {
            try {
                const savedUsers = localStorage.getItem('autoparts_users');
                const savedShops = localStorage.getItem('autoparts_shops');
                const savedProducts = localStorage.getItem('autoparts_products');
                const savedNextShopId = localStorage.getItem('autoparts_nextShopId');
                const savedNextProductId = localStorage.getItem('autoparts_nextProductId');

                if (savedUsers && savedShops && savedProducts) {
                    users = JSON.parse(savedUsers);
                    shops = JSON.parse(savedShops);
                    products = JSON.parse(savedProducts);
                    nextShopId = savedNextShopId ? parseInt(savedNextShopId) : 2;
                    nextProductId = savedNextProductId ? parseInt(savedNextProductId) : 5;
                    console.log('✅ تم تحميل البيانات من localStorage');
                    console.log(`📊 المحلات: ${shops.length}, المنتجات: ${products.length}, المستخدمين: ${Object.keys(users).length}`);
                    return true;
                } else {
                    console.log('📝 لا توجد بيانات محفوظة، استخدام البيانات الافتراضية');
                    return false;
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                return false;
            }
        }

        function initializeData() {
            console.log('🔧 تهيئة البيانات...');
            const dataLoaded = loadFromStorage();

            if (!dataLoaded) {
                console.log('📝 استخدام البيانات الافتراضية');
                // Use default data
                users = { ...defaultUsers };
                shops = [...defaultShops];
                products = [...defaultProducts];

                // Save initial data
                saveToStorage();
                console.log('💾 تم حفظ البيانات الافتراضية');
            }

            console.log('✅ تم تهيئة البيانات:', {
                users: Object.keys(users).length,
                shops: shops.length,
                products: products.length
            });
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم فقدان جميع التغييرات!')) {
                localStorage.removeItem('autoparts_users');
                localStorage.removeItem('autoparts_shops');
                localStorage.removeItem('autoparts_products');
                localStorage.removeItem('autoparts_nextShopId');
                localStorage.removeItem('autoparts_nextProductId');

                // Reset to defaults
                users = { ...defaultUsers };
                shops = [...defaultShops];
                products = [...defaultProducts];
                nextShopId = 2;
                nextProductId = 5;

                saveToStorage();
                showNotification('تم إعادة تعيين البيانات بنجاح!', 'success');

                // Refresh current page
                if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                } else if (document.getElementById('shops').classList.contains('active')) {
                    loadShops();
                } else if (document.getElementById('dashboard').classList.contains('active')) {
                    loadDashboard();
                }
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            container.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide notification after 4 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Show page
        function showPage(pageId) {
            // Hide hero section for other pages
            const heroSection = document.getElementById('heroSection');
            if (pageId !== 'home') {
                heroSection.style.display = 'none';
            } else {
                heroSection.style.display = 'block';
            }

            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Load content based on page
            setTimeout(() => {
                if (pageId === 'home') {
                    loadData();
                } else if (pageId === 'shops') {
                    loadShops();
                } else if (pageId === 'dashboard') {
                    loadDashboard();
                }
            }, 100);
        }



        // Load data
        function loadData() {
            const tableBody = document.getElementById('productsTableBody');
            tableBody.innerHTML = '';

            if (products.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد منتجات
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            products.forEach(product => {
                const prices = product.shopDetails.map(shop => shop.price);
                const minPrice = Math.min(...prices);
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);

                const shopsList = product.shopDetails.map(shopDetail => {
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل |
                                إجمالي الكمية: <span class="fw-bold">${totalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${currentUser && currentUser.role === 'admin' ? `
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct(${product.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });

            updateSummaryCards();
            showNotification('تم تحميل البيانات بنجاح!', 'success');
        }

            products.forEach(product => {
                const prices = product.shopDetails.map(shop => shop.price);
                const minPrice = Math.min(...prices);
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);

                const shopsList = product.shopDetails.map(shopDetail => {
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل |
                                إجمالي الكمية: <span class="fw-bold">${totalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${currentUser && currentUser.role === 'admin' ? `
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct(${product.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });

            updateSummaryCards();
            showNotification('تم تحميل البيانات بنجاح!', 'success');
        }

        // Update summary cards
        function updateSummaryCards() {
            const totalProducts = products.length;
            const uniqueShops = new Set();
            let totalQuantity = 0;
            let totalPrice = 0;
            let priceCount = 0;

            products.forEach(product => {
                product.shopDetails.forEach(shop => {
                    uniqueShops.add(shop.shopName);
                    totalQuantity += shop.quantity;
                    totalPrice += shop.price;
                    priceCount++;
                });
            });

            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('totalShops').textContent = uniqueShops.size;
            document.getElementById('totalQuantity').textContent = totalQuantity;
            document.getElementById('averagePrice').textContent = priceCount > 0 ? Math.round(totalPrice / priceCount) + ' ريال' : '0 ريال';
        }

        // Load shops
        function loadShops() {
            const container = document.getElementById('shopsContainer');
            container.innerHTML = '';

            if (shops.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد محلات
                        </div>
                    </div>
                `;
                return;
            }

            shops.forEach(shop => {
                const shopProducts = products.filter(product =>
                    product.shopDetails.some(detail => detail.shopId === shop.id)
                );

                const shopCard = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card product-card h-100">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${shop.name}</h5>
                                <p class="card-text text-muted">${shop.description}</p>

                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>${shop.phone}<br>
                                        <i class="fas fa-map-marker-alt me-1"></i>${shop.address}, ${shop.city}<br>
                                        ${shop.specialty ? `<i class="fas fa-tag me-1"></i>${shop.specialty}<br>` : ''}
                                        <i class="fas fa-calendar me-1"></i>انضم في ${shop.joinDate}
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge bg-primary">${shopProducts.length} منتج</span>
                                        <span class="badge bg-warning">⭐ ${shop.rating}</span>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewShopProducts(${shop.id})" title="عرض المنتجات">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        ${currentUser && currentUser.role === 'admin' ? `
                                            <button class="btn btn-outline-warning" onclick="editShop(${shop.id})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteShop(${shop.id})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += shopCard;
            });

            showNotification('تم تحميل المحلات بنجاح!', 'success');
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const alertDiv = document.getElementById('loginAlert');

            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };

                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('dashboardLink').style.display = 'inline';
                document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;

                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';

                showNotification(`مرحباً ${currentUser.name}! تم تسجيل الدخول بنجاح.`, 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    showPage('dashboard');
                }, 1000);

            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';

            showNotification('تم تسجيل الخروج بنجاح', 'info');
            showPage('home');
        }

        // Load dashboard
        function loadDashboard() {
            if (!currentUser) {
                showPage('login');
                return;
            }

            if (currentUser.role === 'admin') {
                loadAdminDashboard();
            } else if (currentUser.role === 'shop') {
                loadShopDashboard();
            }
        }

        // Load admin dashboard
        function loadAdminDashboard() {
            document.getElementById('dashboardTitle').textContent = 'لوحة تحكم المدير';
            document.getElementById('dashboardMessage').textContent = `مرحباً ${currentUser.name}`;

            document.getElementById('dashboardProducts').textContent = products.length;
            document.getElementById('dashboardShops').textContent = shops.length;

            const totalValue = products.reduce((sum, product) => {
                return sum + product.shopDetails.reduce((pSum, shop) => pSum + (shop.price * shop.quantity), 0);
            }, 0);
            document.getElementById('dashboardValue').textContent = Math.round(totalValue) + ' ريال';

            // Show all products for admin
            const container = document.getElementById('dashboardProductsContainer');
            container.innerHTML = '';

            if (products.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>لا توجد منتجات</h5>
                            <p class="mb-0">لا توجد منتجات في النظام حالياً.</p>
                        </div>
                    </div>
                `;
                return;
            }

            products.forEach(product => {
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                const avgPrice = product.shopDetails.reduce((sum, shop) => sum + shop.price, 0) / product.shopDetails.length;

                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card product-card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}</p>

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold text-primary">${Math.round(avgPrice)} ريال</span>
                                    <span class="badge bg-success">الكمية: ${totalQuantity}</span>
                                </div>

                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}

                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm ms-1" onclick="deleteProduct(${product.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Load shop dashboard
        function loadShopDashboard() {
            document.getElementById('dashboardTitle').textContent = 'لوحة تحكم المحل';
            document.getElementById('dashboardMessage').textContent = `مرحباً ${currentUser.name}`;

            // Filter products for current shop
            const shopProducts = products.filter(product =>
                product.shopDetails.some(shop => shop.shopId === currentUser.shopId)
            );

            document.getElementById('dashboardProducts').textContent = shopProducts.length;
            document.getElementById('dashboardShops').textContent = '1';

            const totalValue = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                return sum + (shopDetail ? shopDetail.price * shopDetail.quantity : 0);
            }, 0);
            document.getElementById('dashboardValue').textContent = Math.round(totalValue) + ' ريال';

            // Show shop products
            const container = document.getElementById('dashboardProductsContainer');
            container.innerHTML = '';

            if (shopProducts.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>لا توجد منتجات</h5>
                            <p class="mb-0">لم تقم بإضافة أي منتجات بعد في محلك.</p>
                            <p class="mb-0">اضغط "إضافة منتج جديد" لبدء إضافة منتجاتك.</p>
                        </div>
                    </div>
                `;
                return;
            }

            shopProducts.forEach(product => {
                const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (!shopDetail) return;

                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card product-card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}</p>

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold text-primary">${shopDetail.price} ريال</span>
                                    <span class="badge ${shopDetail.quantity > 0 ? 'bg-success' : 'bg-danger'}">
                                        الكمية: ${shopDetail.quantity}
                                    </span>
                                </div>

                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}

                                <div class="mt-2">
                                    <button class="btn btn-outline-warning btn-sm" onclick="editShopProduct(${product.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm ms-1" onclick="removeProductFromShop(${product.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Show add shop modal
        function showAddShopModal() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بإضافة محلات', 'danger');
                return;
            }

            const modal = `
                <div class="modal fade" id="addShopModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-store me-2"></i>
                                    إضافة محل جديد
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addShopForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="shopUsername" required>
                                                <label>اسم المستخدم *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="password" class="form-control" id="shopPassword" required>
                                                <label>كلمة المرور *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="shopName" required>
                                        <label>اسم المحل *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="shopDescription" style="height: 100px" required></textarea>
                                        <label>وصف المحل *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="tel" class="form-control" id="shopPhone" required>
                                                <label>رقم الهاتف *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="email" class="form-control" id="shopEmail">
                                                <label>البريد الإلكتروني</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="shopAddress" required>
                                        <label>العنوان *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="shopCity" required>
                                                    <option value="">اختر المدينة</option>
                                                    <option value="الرياض">الرياض</option>
                                                    <option value="جدة">جدة</option>
                                                    <option value="الدمام">الدمام</option>
                                                    <option value="مكة المكرمة">مكة المكرمة</option>
                                                    <option value="المدينة المنورة">المدينة المنورة</option>
                                                </select>
                                                <label>المدينة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="shopSpecialty">
                                                <label>التخصص</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-success" onclick="addShop()">
                                    <i class="fas fa-save me-2"></i>
                                    إضافة المحل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('addShopModal'));
            modalElement.show();
        }

        // Add shop
        function addShop() {
            const shopData = {
                username: document.getElementById('shopUsername').value.trim(),
                password: document.getElementById('shopPassword').value.trim(),
                name: document.getElementById('shopName').value.trim(),
                description: document.getElementById('shopDescription').value.trim(),
                phone: document.getElementById('shopPhone').value.trim(),
                email: document.getElementById('shopEmail').value.trim(),
                address: document.getElementById('shopAddress').value.trim(),
                city: document.getElementById('shopCity').value,
                specialty: document.getElementById('shopSpecialty').value.trim()
            };

            // Validation
            if (!shopData.username || !shopData.password || !shopData.name || !shopData.description ||
                !shopData.phone || !shopData.address || !shopData.city) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if username exists
            if (users[shopData.username]) {
                showNotification('اسم المستخدم موجود بالفعل', 'warning');
                return;
            }

            // Check if shop name exists
            if (shops.find(shop => shop.name.toLowerCase() === shopData.name.toLowerCase())) {
                showNotification('اسم المحل موجود بالفعل', 'warning');
                return;
            }

            // Create new shop
            const newShop = {
                id: nextShopId++,
                name: shopData.name,
                description: shopData.description,
                phone: shopData.phone,
                email: shopData.email,
                address: shopData.address,
                city: shopData.city,
                specialty: shopData.specialty,
                rating: 0,
                joinDate: new Date().toISOString().split('T')[0]
            };

            // Create new user
            users[shopData.username] = {
                password: shopData.password,
                role: 'shop',
                name: shopData.name,
                shopId: newShop.id
            };

            // Add shop to array
            shops.push(newShop);

            // Save to localStorage
            saveToStorage();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('addShopModal')).hide();

            showNotification(`تم إضافة المحل "${shopData.name}" بنجاح!`, 'success');
            setTimeout(() => {
                showNotification(`بيانات تسجيل الدخول: ${shopData.username} / ${shopData.password}`, 'info');
            }, 1000);

            // Refresh shops page if currently viewing
            if (document.getElementById('shops').classList.contains('active')) {
                loadShops();
            }
        }

        // Show add product modal
        function showAddProductModal() {
            if (!currentUser) {
                showNotification('يرجى تسجيل الدخول أولاً', 'warning');
                return;
            }

            const modal = `
                <div class="modal fade" id="addProductModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-box me-2"></i>
                                    إضافة منتج جديد
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addProductForm">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="productName" required>
                                        <label>اسم المنتج *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="productDescription" style="height: 100px"></textarea>
                                        <label>وصف المنتج</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="partNumber">
                                                <label>رقم القطعة</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="productCategory">
                                                    <option value="">اختر الفئة</option>
                                                    <option value="فلاتر">فلاتر</option>
                                                    <option value="فرامل">فرامل</option>
                                                    <option value="زيوت">زيوت</option>
                                                    <option value="كهرباء">كهرباء</option>
                                                    <option value="إطارات">إطارات</option>
                                                    <option value="محرك">محرك</option>
                                                    <option value="أخرى">أخرى</option>
                                                </select>
                                                <label>الفئة</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="productBrand">
                                                <label>الماركة</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="productModel">
                                                <label>الموديل</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required>
                                                <label>السعر (ريال) *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="productQuantity" min="0" required>
                                                <label>الكمية المتوفرة *</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="addProduct()">
                                    <i class="fas fa-save me-2"></i>
                                    إضافة المنتج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('addProductModal'));
            modalElement.show();
        }

        // Add product
        function addProduct() {
            const productData = {
                name: document.getElementById('productName').value.trim(),
                description: document.getElementById('productDescription').value.trim(),
                partNumber: document.getElementById('partNumber').value.trim(),
                category: document.getElementById('productCategory').value,
                brand: document.getElementById('productBrand').value.trim(),
                model: document.getElementById('productModel').value.trim(),
                price: parseFloat(document.getElementById('productPrice').value),
                quantity: parseInt(document.getElementById('productQuantity').value)
            };

            // Validation
            if (!productData.name || !productData.price || isNaN(productData.quantity)) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if product exists
            const existingProduct = products.find(p =>
                p.name.toLowerCase() === productData.name.toLowerCase() &&
                p.partNumber === productData.partNumber
            );

            if (existingProduct) {
                // Add to existing product
                const shopDetail = existingProduct.shopDetails.find(shop => shop.shopId === currentUser.shopId);
                if (shopDetail) {
                    showNotification('المنتج موجود بالفعل في محلك', 'warning');
                    return;
                }

                existingProduct.shopDetails.push({
                    shopName: currentUser.name,
                    shopId: currentUser.shopId,
                    price: productData.price,
                    quantity: productData.quantity
                });

                showNotification(`تم إضافة المنتج "${productData.name}" إلى محلك بنجاح!`, 'success');
            } else {
                // Create new product
                const newProduct = {
                    id: nextProductId++,
                    name: productData.name,
                    description: productData.description,
                    partNumber: productData.partNumber,
                    category: productData.category,
                    brand: productData.brand,
                    model: productData.model,
                    shopDetails: [{
                        shopName: currentUser.name,
                        shopId: currentUser.shopId,
                        price: productData.price,
                        quantity: productData.quantity
                    }]
                };

                products.push(newProduct);
                showNotification(`تم إضافة المنتج الجديد "${productData.name}" بنجاح!`, 'success');
            }

            // Save to localStorage
            saveToStorage();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();

            // Refresh current page
            if (document.getElementById('dashboard').classList.contains('active')) {
                loadDashboard();
            } else if (document.getElementById('home').classList.contains('active')) {
                loadData();
            }
        }



        // Other functions
        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (product) {
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                const availableShops = product.shopDetails.filter(shop => shop.quantity > 0).length;

                showNotification(
                    `${product.name} - متوفر في ${availableShops} محل بإجمالي ${totalQuantity} قطعة`,
                    'info'
                );
            }
        }

        function deleteProduct(productId) {
            if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return;

            const productIndex = products.findIndex(p => p.id === productId);
            if (productIndex > -1) {
                const productName = products[productIndex].name;
                products.splice(productIndex, 1);

                // Save to localStorage
                saveToStorage();

                showNotification(`تم حذف المنتج "${productName}" بنجاح!`, 'success');

                // Refresh current page
                if (document.getElementById('dashboard').classList.contains('active')) {
                    loadDashboard();
                } else if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                }
            }
        }

        function editShopProduct(productId) {
            const product = products.find(p => p.id === productId);
            const shopDetail = product.shopDetails.find(shop => shop.shopId === currentUser.shopId);

            const newPrice = prompt(`تعديل سعر "${product.name}":`, shopDetail.price);
            if (newPrice === null) return;

            const newQuantity = prompt(`تعديل كمية "${product.name}":`, shopDetail.quantity);
            if (newQuantity === null) return;

            shopDetail.price = parseFloat(newPrice);
            shopDetail.quantity = parseInt(newQuantity);

            // Save to localStorage
            saveToStorage();

            showNotification(`تم تحديث المنتج "${product.name}" بنجاح!`, 'success');
            loadDashboard();
        }

        function removeProductFromShop(productId) {
            if (!confirm('هل أنت متأكد من حذف هذا المنتج من محلك؟')) return;

            const product = products.find(p => p.id === productId);
            const shopDetailIndex = product.shopDetails.findIndex(shop => shop.shopId === currentUser.shopId);

            if (shopDetailIndex > -1) {
                product.shopDetails.splice(shopDetailIndex, 1);

                // If no shops have this product, remove it completely
                if (product.shopDetails.length === 0) {
                    const productIndex = products.findIndex(p => p.id === productId);
                    products.splice(productIndex, 1);
                }

                // Save to localStorage
                saveToStorage();

                showNotification(`تم حذف المنتج "${product.name}" من محلك بنجاح!`, 'success');
                loadDashboard();
            }
        }

        function viewShopProducts(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Get shop products
            const shopProducts = products.filter(product =>
                product.shopDetails.some(detail => detail.shopId === shopId)
            );

            // Calculate statistics
            const totalProducts = shopProducts.length;
            const totalValue = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                return sum + (shopDetail ? shopDetail.price * shopDetail.quantity : 0);
            }, 0);

            const totalQuantity = shopProducts.reduce((sum, product) => {
                const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                return sum + (shopDetail ? shopDetail.quantity : 0);
            }, 0);

            const modal = `
                <div class="modal fade" id="viewShopModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header bg-info text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-store me-2"></i>
                                    تفاصيل المحل: ${shop.name}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Shop Info -->
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6><i class="fas fa-info-circle me-2"></i>معلومات المحل:</h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>الاسم:</strong> ${shop.name}</p>
                                                        <p><strong>الوصف:</strong> ${shop.description}</p>
                                                        <p><strong>الهاتف:</strong> ${shop.phone}</p>
                                                        <p><strong>البريد:</strong> ${shop.email || 'غير محدد'}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>العنوان:</strong> ${shop.address}</p>
                                                        <p><strong>المدينة:</strong> ${shop.city}</p>
                                                        <p><strong>التخصص:</strong> ${shop.specialty || 'غير محدد'}</p>
                                                        <p><strong>التقييم:</strong> ${shop.rating} ⭐</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-primary text-white text-center">
                                            <div class="card-body">
                                                <h4>${totalProducts}</h4>
                                                <p class="mb-0">إجمالي المنتجات</p>
                                            </div>
                                        </div>
                                        <div class="card bg-success text-white text-center mt-2">
                                            <div class="card-body">
                                                <h4>${totalQuantity}</h4>
                                                <p class="mb-0">إجمالي الكميات</p>
                                            </div>
                                        </div>
                                        <div class="card bg-warning text-white text-center mt-2">
                                            <div class="card-body">
                                                <h4>${Math.round(totalValue)} ريال</h4>
                                                <p class="mb-0">إجمالي القيمة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Products Table -->
                                <h6><i class="fas fa-box me-2"></i>منتجات المحل:</h6>
                                ${totalProducts === 0 ? `
                                    <div class="alert alert-info text-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد منتجات في هذا المحل
                                    </div>
                                ` : `
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>رقم القطعة</th>
                                                    <th>الفئة</th>
                                                    <th>السعر</th>
                                                    <th>الكمية</th>
                                                    <th>القيمة الإجمالية</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${shopProducts.map(product => {
                                                    const shopDetail = product.shopDetails.find(detail => detail.shopId === shopId);
                                                    const productValue = shopDetail.price * shopDetail.quantity;
                                                    const isAvailable = shopDetail.quantity > 0;

                                                    return `
                                                        <tr>
                                                            <td>
                                                                <strong>${product.name}</strong>
                                                                ${product.brand ? `<br><small class="text-muted">${product.brand}</small>` : ''}
                                                            </td>
                                                            <td><code>${product.partNumber || '-'}</code></td>
                                                            <td>
                                                                ${product.category ? `<span class="badge bg-secondary">${product.category}</span>` : '-'}
                                                            </td>
                                                            <td><strong>${shopDetail.price} ريال</strong></td>
                                                            <td>
                                                                <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                    ${shopDetail.quantity}
                                                                </span>
                                                            </td>
                                                            <td><strong>${Math.round(productValue)} ريال</strong></td>
                                                            <td>
                                                                <span class="badge ${isAvailable ? 'bg-success' : 'bg-danger'}">
                                                                    ${isAvailable ? 'متوفر' : 'غير متوفر'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                ${currentUser && currentUser.role === 'admin' ? `
                                    <button type="button" class="btn btn-warning" onclick="editShop(${shopId}); bootstrap.Modal.getInstance(document.getElementById('viewShopModal')).hide();">
                                        <i class="fas fa-edit me-2"></i>
                                        تعديل المحل
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('viewShopModal'));
            modalElement.show();
        }

        function editShop(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Find the user for this shop
            let shopUser = null;
            let shopUsername = '';
            for (let username in users) {
                if (users[username].shopId === shopId) {
                    shopUser = users[username];
                    shopUsername = username;
                    break;
                }
            }

            const modal = `
                <div class="modal fade" id="editShopModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل بيانات المحل
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editShopForm">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>معرف المحل:</strong> ${shop.id} |
                                        <strong>تاريخ الانضمام:</strong> ${shop.joinDate}
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="editShopUsername" value="${shopUsername}" required>
                                                <label>اسم المستخدم *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="password" class="form-control" id="editShopPassword" value="${shopUser ? shopUser.password : ''}" required>
                                                <label>كلمة المرور *</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="editShopName" value="${shop.name}" required>
                                        <label>اسم المحل *</label>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="editShopDescription" style="height: 100px" required>${shop.description}</textarea>
                                        <label>وصف المحل *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="tel" class="form-control" id="editShopPhone" value="${shop.phone}" required>
                                                <label>رقم الهاتف *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="email" class="form-control" id="editShopEmail" value="${shop.email || ''}">
                                                <label>البريد الإلكتروني</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="editShopAddress" value="${shop.address}" required>
                                        <label>العنوان *</label>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="editShopCity" required>
                                                    <option value="">اختر المدينة</option>
                                                    <option value="الرياض" ${shop.city === 'الرياض' ? 'selected' : ''}>الرياض</option>
                                                    <option value="جدة" ${shop.city === 'جدة' ? 'selected' : ''}>جدة</option>
                                                    <option value="الدمام" ${shop.city === 'الدمام' ? 'selected' : ''}>الدمام</option>
                                                    <option value="مكة المكرمة" ${shop.city === 'مكة المكرمة' ? 'selected' : ''}>مكة المكرمة</option>
                                                    <option value="المدينة المنورة" ${shop.city === 'المدينة المنورة' ? 'selected' : ''}>المدينة المنورة</option>
                                                </select>
                                                <label>المدينة *</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="editShopSpecialty" value="${shop.specialty || ''}">
                                                <label>التخصص</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-floating mb-3">
                                                <input type="number" class="form-control" id="editShopRating" value="${shop.rating}" min="0" max="5" step="0.1">
                                                <label>التقييم (0-5)</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-warning" onclick="updateShop(${shopId}, '${shopUsername}')">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalsContainer').innerHTML = modal;
            const modalElement = new bootstrap.Modal(document.getElementById('editShopModal'));
            modalElement.show();
        }

        // Update shop
        function updateShop(shopId, oldUsername) {
            const shopData = {
                username: document.getElementById('editShopUsername').value.trim(),
                password: document.getElementById('editShopPassword').value.trim(),
                name: document.getElementById('editShopName').value.trim(),
                description: document.getElementById('editShopDescription').value.trim(),
                phone: document.getElementById('editShopPhone').value.trim(),
                email: document.getElementById('editShopEmail').value.trim(),
                address: document.getElementById('editShopAddress').value.trim(),
                city: document.getElementById('editShopCity').value,
                specialty: document.getElementById('editShopSpecialty').value.trim(),
                rating: parseFloat(document.getElementById('editShopRating').value) || 0
            };

            // Validation
            if (!shopData.username || !shopData.password || !shopData.name || !shopData.description ||
                !shopData.phone || !shopData.address || !shopData.city) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if new username exists (if changed)
            if (shopData.username !== oldUsername && users[shopData.username]) {
                showNotification('اسم المستخدم الجديد موجود بالفعل', 'warning');
                return;
            }

            // Check if new shop name exists (if changed)
            const existingShop = shops.find(shop => shop.id !== shopId && shop.name.toLowerCase() === shopData.name.toLowerCase());
            if (existingShop) {
                showNotification('اسم المحل الجديد موجود بالفعل', 'warning');
                return;
            }

            // Find and update shop
            const shopIndex = shops.findIndex(shop => shop.id === shopId);
            if (shopIndex === -1) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Update shop data
            shops[shopIndex] = {
                ...shops[shopIndex],
                name: shopData.name,
                description: shopData.description,
                phone: shopData.phone,
                email: shopData.email,
                address: shopData.address,
                city: shopData.city,
                specialty: shopData.specialty,
                rating: shopData.rating
            };

            // Update user data
            if (oldUsername !== shopData.username) {
                // Remove old username
                delete users[oldUsername];

                // Add new username
                users[shopData.username] = {
                    password: shopData.password,
                    role: 'shop',
                    name: shopData.name,
                    shopId: shopId
                };
            } else {
                // Update existing user
                users[shopData.username] = {
                    ...users[shopData.username],
                    password: shopData.password,
                    name: shopData.name
                };
            }

            // Update product shop details with new shop name
            products.forEach(product => {
                product.shopDetails.forEach(shopDetail => {
                    if (shopDetail.shopId === shopId) {
                        shopDetail.shopName = shopData.name;
                    }
                });
            });

            // Save to localStorage
            saveToStorage();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('editShopModal')).hide();

            showNotification(`تم تحديث بيانات المحل "${shopData.name}" بنجاح!`, 'success');

            if (oldUsername !== shopData.username) {
                setTimeout(() => {
                    showNotification(`بيانات تسجيل الدخول الجديدة: ${shopData.username} / ${shopData.password}`, 'info');
                }, 1000);
            }

            // Refresh shops page if currently viewing
            if (document.getElementById('shops').classList.contains('active')) {
                loadShops();
            }

            // Refresh home page data
            if (document.getElementById('home').classList.contains('active')) {
                loadData();
            }
        }

        function deleteShop(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                showNotification('المحل غير موجود', 'danger');
                return;
            }

            // Count products that will be affected
            const shopProducts = products.filter(product =>
                product.shopDetails.some(detail => detail.shopId === shopId)
            );

            const productsToDelete = products.filter(product =>
                product.shopDetails.length === 1 && product.shopDetails[0].shopId === shopId
            );

            // Show detailed confirmation
            const confirmMessage = `هل أنت متأكد من حذف المحل "${shop.name}"؟\n\n` +
                `سيتم حذف:\n` +
                `• المحل وجميع بياناته\n` +
                `• حساب المستخدم الخاص بالمحل\n` +
                `• ${shopProducts.length} منتج من المحل\n` +
                `• ${productsToDelete.length} منتج نهائياً (غير متوفر في محلات أخرى)\n\n` +
                `هذا الإجراء لا يمكن التراجع عنه!`;

            if (!confirm(confirmMessage)) return;

            const shopIndex = shops.findIndex(s => s.id === shopId);
            if (shopIndex > -1) {
                const shopName = shops[shopIndex].name;
                let deletedProductsCount = 0;
                let removedProductsCount = 0;

                // Remove shop products and count deletions
                products.forEach(product => {
                    const initialLength = product.shopDetails.length;
                    product.shopDetails = product.shopDetails.filter(shop => shop.shopId !== shopId);

                    if (product.shopDetails.length < initialLength) {
                        removedProductsCount++;
                    }
                });

                // Remove empty products and count deletions
                for (let i = products.length - 1; i >= 0; i--) {
                    if (products[i].shopDetails.length === 0) {
                        products.splice(i, 1);
                        deletedProductsCount++;
                    }
                }

                // Remove shop
                shops.splice(shopIndex, 1);

                // Remove user and get username
                let deletedUsername = '';
                for (let username in users) {
                    if (users[username].shopId === shopId) {
                        deletedUsername = username;
                        delete users[username];
                        break;
                    }
                }

                // Save to localStorage
                saveToStorage();

                // Show detailed success message
                showNotification(`تم حذف المحل "${shopName}" بنجاح!`, 'success');

                setTimeout(() => {
                    showNotification(
                        `تفاصيل الحذف: حُذف ${deletedProductsCount} منتج نهائياً، وأُزيل ${removedProductsCount} منتج من المحل، وحُذف المستخدم "${deletedUsername}"`,
                        'info'
                    );
                }, 1000);

                // Refresh current page
                if (document.getElementById('shops').classList.contains('active')) {
                    loadShops();
                }

                // Refresh home page data if viewing
                if (document.getElementById('home').classList.contains('active')) {
                    loadData();
                }

                // Refresh dashboard if current user's shop was deleted
                if (currentUser && currentUser.shopId === shopId) {
                    logout();
                    showNotification('تم تسجيل خروجك لأن محلك قد حُذف', 'warning');
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize data from localStorage
            initializeData();

            // Add form event listeners
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // Load initial data
            loadData();

            // Show welcome message
            const dataCount = `المحلات: ${shops.length}, المنتجات: ${products.length}`;
            showNotification(`مرحباً بك! تم تحميل البيانات المحفوظة (${dataCount})`, 'success');
        });
    </script>
</body>
</html>
