{% extends "base.html" %}

{% block title %}الرئيسية - نظام ربط محلات قطع غيار السيارات{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    أفضل قطع غيار السيارات
                </h1>
                <p class="lead mb-4">
                    اكتشف أفضل قطع غيار السيارات من محلات موثوقة ومعتمدة
                </p>
                <a href="{{ url_for('search') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-search me-2"></i>
                    ابحث الآن
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-car fa-10x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="search-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <form action="{{ url_for('search') }}" method="GET" class="row g-3">
                    <div class="col-md-4">
                        <select name="brand" class="form-select">
                            <option value="">اختر الماركة</option>
                            <option value="toyota">تويوتا</option>
                            <option value="honda">هوندا</option>
                            <option value="nissan">نيسان</option>
                            <option value="hyundai">هيونداي</option>
                            <option value="kia">كيا</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="part_number" class="form-control" placeholder="رقم القطعة">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Latest Products -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">أحدث القطع</h2>
                <p class="text-muted">اكتشف أحدث قطع الغيار المضافة</p>
            </div>
        </div>
        
        <div class="row">
            {% for product in latest_products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card">
                    {% if product.image_url %}
                        <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                    {% else %}
                        <div class="card-img-top product-image bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-cog fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small">{{ product.description[:50] }}...</p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold text-primary">{{ product.price }} ريال</span>
                            <small class="text-muted">{{ product.shop.name }}</small>
                        </div>
                        
                        {% if product.part_number %}
                        <small class="text-muted d-block mt-1">رقم القطعة: {{ product.part_number }}</small>
                        {% endif %}
                        
                        <div class="mt-2">
                            <span class="badge bg-success">متوفر ({{ product.quantity }})</span>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <button class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-phone me-1"></i>
                            اتصل بالمحل
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if not latest_products %}
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد منتجات حالياً</h4>
            <p class="text-muted">سيتم عرض المنتجات هنا عند إضافتها من قبل المحلات</p>
        </div>
        {% endif %}
    </div>
</section>

<!-- Top Shops -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">أفضل المحلات</h2>
                <p class="text-muted">المحلات الأعلى تقييماً</p>
            </div>
        </div>
        
        <div class="row">
            {% for shop in top_shops %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-store fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">{{ shop.name }}</h5>
                        <p class="card-text text-muted">{{ shop.description[:80] }}...</p>
                        
                        <div class="rating mb-2">
                            {% for i in range(5) %}
                                {% if i < shop.rating %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="ms-2">({{ shop.rating }})</span>
                        </div>
                        
                        {% if shop.phone %}
                        <p class="text-muted mb-2">
                            <i class="fas fa-phone me-1"></i>
                            {{ shop.phone }}
                        </p>
                        {% endif %}
                        
                        <a href="{{ url_for('shop_products', shop_id=shop.id) }}" class="btn btn-outline-primary">
                            عرض المنتجات
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if not top_shops %}
        <div class="text-center py-5">
            <i class="fas fa-store fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد محلات مسجلة حالياً</h4>
        </div>
        {% endif %}
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 text-center mb-4">
                <i class="fas fa-search fa-3x text-primary mb-3"></i>
                <h4>بحث متقدم</h4>
                <p class="text-muted">ابحث عن قطع الغيار حسب الماركة والموديل ورقم القطعة</p>
            </div>
            <div class="col-lg-4 text-center mb-4">
                <i class="fas fa-star fa-3x text-primary mb-3"></i>
                <h4>تقييمات موثوقة</h4>
                <p class="text-muted">تقييمات حقيقية من العملاء للمحلات والمنتجات</p>
            </div>
            <div class="col-lg-4 text-center mb-4">
                <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                <h4>محلات معتمدة</h4>
                <p class="text-muted">جميع المحلات مراجعة ومعتمدة لضمان الجودة</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
