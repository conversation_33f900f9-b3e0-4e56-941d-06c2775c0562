{% extends "base.html" %}

{% block title %}تعديل المنتج - {{ product.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="fw-bold">
                <i class="fas fa-edit me-2"></i>
                تعديل المنتج
            </h2>
            <p class="text-muted">تعديل بيانات المنتج: {{ product.name }}</p>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        بيانات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="part_number" class="form-label">رقم القطعة</label>
                                <input type="text" class="form-control" id="part_number" name="part_number" value="{{ product.part_number or '' }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="اكتب وصفاً مفصلاً للمنتج...">{{ product.description or '' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="price" class="form-label">السعر (ريال) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ product.price }}" required>
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="{{ product.quantity }}" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if product.category_id == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="brand_id" class="form-label">الماركة</label>
                                <select class="form-select" id="brand_id" name="brand_id">
                                    <option value="">اختر الماركة</option>
                                    {% for brand in brands %}
                                    <option value="{{ brand.id }}" {% if product.brand_id == brand.id %}selected{% endif %}>
                                        {{ brand.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="car_model_id" class="form-label">موديل السيارة</label>
                                <select class="form-select" id="car_model_id" name="car_model_id">
                                    <option value="">اختر الموديل</option>
                                    {% for model in car_models %}
                                    <option value="{{ model.id }}" {% if product.car_model_id == model.id %}selected{% endif %}>
                                        {{ model.brand.name }} {{ model.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="image_url" class="form-label">رابط الصورة</label>
                            <input type="url" class="form-control" id="image_url" name="image_url" value="{{ product.image_url or '' }}" placeholder="https://example.com/image.jpg">
                            <div class="form-text">يمكنك إضافة رابط صورة المنتج (اختياري)</div>
                            
                            {% if product.image_url %}
                            <div class="mt-2">
                                <img src="{{ product.image_url }}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" alt="{{ product.name }}">
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('shop_products_manage') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <div>
                                <button type="button" class="btn btn-danger me-2" onclick="confirmDelete({{ product.id }}, '{{ product.name }}')">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف المنتج
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذا المنتج؟</h5>
                    <p class="text-muted" id="productName"></p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف المنتج
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(productId, productName) {
    document.getElementById('productName').textContent = productName;
    document.getElementById('deleteForm').action = '{{ url_for("shop_delete_product", product_id=0) }}'.replace('0', productId);
    
    var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// تحديث قائمة الموديلات عند تغيير الماركة
document.getElementById('brand_id').addEventListener('change', function() {
    const brandId = this.value;
    const modelSelect = document.getElementById('car_model_id');
    const currentModelId = {{ product.car_model_id or 'null' }};
    
    // مسح الخيارات الحالية
    modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
    
    if (brandId) {
        {% for model in car_models %}
        if ({{ model.brand_id }} == brandId) {
            const option = document.createElement('option');
            option.value = {{ model.id }};
            option.textContent = '{{ model.name }}';
            if ({{ model.id }} == currentModelId) {
                option.selected = true;
            }
            modelSelect.appendChild(option);
        }
        {% endfor %}
    }
});

// معاينة الصورة
document.getElementById('image_url').addEventListener('input', function() {
    const url = this.value;
    const existingPreview = document.getElementById('new_image_preview');
    
    if (existingPreview) {
        existingPreview.remove();
    }
    
    if (url) {
        const img = document.createElement('img');
        img.id = 'new_image_preview';
        img.src = url;
        img.className = 'img-thumbnail mt-2';
        img.style.maxWidth = '200px';
        img.style.maxHeight = '200px';
        
        img.onerror = function() {
            this.remove();
        };
        
        this.parentNode.appendChild(img);
    }
});
</script>
{% endblock %}
