#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل تطبيق قطع غيار السيارات
Run script for Auto Parts Shop System
"""

import os
import sys
from database import Database
from api import app

def main():
    print("=" * 60)
    print("🚗 تطبيق قطع غيار السيارات مع قاعدة البيانات")
    print("Auto Parts Shop System with Database")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    print("🗄️ إنشاء قاعدة البيانات...")
    try:
        db = Database()
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return
    
    print("\n📊 معلومات التطبيق:")
    print("🌐 الرابط الرئيسي: http://localhost:5000")
    print("📱 التطبيق: http://localhost:5000/static/app.html")
    print("🔗 API المنتجات: http://localhost:5000/api/products")
    print("🔗 API المحلات: http://localhost:5000/api/shops")
    
    print("\n👤 حسابات تجريبية:")
    print("🔑 المدير: admin / admin123")
    print("🏪 المحل: shop1 / shop123")
    
    print("\n🛑 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 60)
    
    # تشغيل الخادم
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    main()
