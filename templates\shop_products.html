{% extends "base.html" %}

{% block title %}{{ shop.name }} - منتجات المحل{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Shop Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="fw-bold mb-2">
                                <i class="fas fa-store me-2"></i>
                                {{ shop.name }}
                            </h2>
                            {% if shop.description %}
                            <p class="mb-2">{{ shop.description }}</p>
                            {% endif %}
                            
                            <div class="row">
                                {% if shop.address %}
                                <div class="col-md-6 mb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    {{ shop.address }}
                                </div>
                                {% endif %}
                                {% if shop.phone %}
                                <div class="col-md-6 mb-2">
                                    <i class="fas fa-phone me-2"></i>
                                    {{ shop.phone }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="rating mb-2">
                                {% for i in range(5) %}
                                    {% if i < shop.rating %}
                                        <i class="fas fa-star fa-lg"></i>
                                    {% else %}
                                        <i class="far fa-star fa-lg"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <h4>{{ "%.1f"|format(shop.rating) }} / 5</h4>
                            <small>({{ shop.reviews|length }} تقييم)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Products Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h3>منتجات المحل ({{ products|length }})</h3>
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reviewModal">
                    <i class="fas fa-star me-2"></i>
                    إضافة تقييم
                </button>
            </div>
        </div>
    </div>
    
    {% if products %}
    <div class="row">
        {% for product in products %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card product-card h-100">
                {% if product.image_url %}
                    <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                {% else %}
                    <div class="card-img-top product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-cog fa-3x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="card-body">
                    <h6 class="card-title">{{ product.name }}</h6>
                    {% if product.description %}
                    <p class="card-text text-muted small">{{ product.description[:60] }}{% if product.description|length > 60 %}...{% endif %}</p>
                    {% endif %}
                    
                    <div class="mb-2">
                        {% if product.brand %}
                        <span class="badge bg-info">{{ product.brand.name }}</span>
                        {% endif %}
                        {% if product.category %}
                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-primary fs-5">{{ product.price }} ريال</span>
                        {% if product.part_number %}
                        <small class="text-muted">{{ product.part_number }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-2">
                        {% if product.quantity > 0 %}
                        <span class="badge bg-success">متوفر ({{ product.quantity }})</span>
                        {% else %}
                        <span class="badge bg-danger">غير متوفر</span>
                        {% endif %}
                    </div>
                    
                    {% if product.car_model %}
                    <small class="text-muted d-block">
                        <i class="fas fa-car me-1"></i>
                        {{ product.car_model.brand.name }} {{ product.car_model.name }}
                    </small>
                    {% endif %}
                </div>
                
                <div class="card-footer bg-transparent">
                    <button class="btn btn-primary btn-sm w-100" onclick="contactShop('{{ shop.phone }}', '{{ shop.name }}', '{{ product.name }}')">
                        <i class="fas fa-phone me-1"></i>
                        اتصل للاستفسار
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد منتجات حالياً</h4>
        <p class="text-muted">لم يقم المحل بإضافة أي منتجات بعد</p>
    </div>
    {% endif %}
    
    <!-- Reviews Section -->
    {% if shop.reviews %}
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-comments me-2"></i>
                التقييمات والآراء
            </h4>
            
            {% for review in shop.reviews[-5:] %}
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="fw-bold">{{ review.reviewer_name }}</h6>
                            <div class="rating mb-2">
                                {% for i in range(5) %}
                                    {% if i < review.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-warning"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            {% if review.comment %}
                            <p class="mb-0">{{ review.comment }}</p>
                            {% endif %}
                        </div>
                        <small class="text-muted">{{ review.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تواصل مع المحل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                <h4 id="shopName"></h4>
                <p class="lead" id="shopPhone"></p>
                <p class="text-muted" id="productName"></p>
                <p class="text-muted">يمكنك الاتصال مباشرة بالمحل للاستفسار عن المنتج</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a id="callButton" href="#" class="btn btn-primary">
                    <i class="fas fa-phone me-1"></i>
                    اتصل الآن
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة تقييم للمحل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_review') }}" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="shop_id" value="{{ shop.id }}">
                    
                    <div class="mb-3">
                        <label class="form-label">التقييم</label>
                        <div class="rating-input">
                            {% for i in range(1, 6) %}
                            <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                            <label for="star{{ i }}" class="star-label">
                                <i class="fas fa-star"></i>
                            </label>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reviewer_name" class="form-label">اسمك</label>
                        <input type="text" class="form-control" id="reviewer_name" name="reviewer_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reviewer_email" class="form-label">البريد الإلكتروني (اختياري)</label>
                        <input type="email" class="form-control" id="reviewer_email" name="reviewer_email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="comment" class="form-label">التعليق</label>
                        <textarea class="form-control" id="comment" name="comment" rows="3" placeholder="شاركنا رأيك في المحل..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-star me-1"></i>
                        إضافة التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    color: #ddd;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
}

.rating-input input[type="radio"]:checked ~ .star-label,
.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label {
    color: #ffc107;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function contactShop(phone, shopName, productName) {
    document.getElementById('shopName').textContent = shopName;
    document.getElementById('shopPhone').textContent = phone;
    document.getElementById('productName').textContent = 'الاستفسار عن: ' + productName;
    document.getElementById('callButton').href = 'tel:' + phone;
    
    var modal = new bootstrap.Modal(document.getElementById('contactModal'));
    modal.show();
}
</script>
{% endblock %}
