#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد البيانات التجريبية لتطبيق قطع غيار السيارات
Demo Data Setup for Auto Parts System
"""

from app import app, db, User, Shop, Product, Category, Brand, CarModel, Review
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import random

def create_demo_data():
    """إنشاء بيانات تجريبية للتطبيق"""
    
    with app.app_context():
        print("🔄 جاري إنشاء البيانات التجريبية...")
        
        # إنشاء محل تجريبي
        if not User.query.filter_by(username='shop1').first():
            # إنشاء مستخدم المحل
            shop_user = User(
                username='shop1',
                email='<EMAIL>',
                password_hash=generate_password_hash('shop123'),
                role='shop'
            )
            db.session.add(shop_user)
            db.session.flush()
            
            # إنشاء المحل
            demo_shop = Shop(
                name='محل الأصيل لقطع غيار السيارات',
                description='محل متخصص في قطع غيار السيارات اليابانية والكورية مع خبرة 15 سنة',
                address='شارع الملك فهد، الرياض، المملكة العربية السعودية',
                phone='+966501234567',
                email='<EMAIL>',
                user_id=shop_user.id,
                rating=4.5
            )
            db.session.add(demo_shop)
            db.session.flush()
            
            print("✅ تم إنشاء محل تجريبي: محل الأصيل")
            
            # إضافة منتجات تجريبية
            demo_products = [
                {
                    'name': 'فلتر هواء تويوتا كامري',
                    'description': 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020، يضمن تنقية الهواء الداخل للمحرك',
                    'part_number': 'TOY-AF-001',
                    'price': 85.00,
                    'quantity': 15,
                    'category_id': 7,  # فلاتر
                    'brand_id': 1,     # تويوتا
                },
                {
                    'name': 'تيل فرامل هوندا أكورد',
                    'description': 'تيل فرامل عالي الجودة لسيارة هوندا أكورد، يوفر أداء فرملة ممتاز وآمن',
                    'part_number': 'HON-BP-002',
                    'price': 120.00,
                    'quantity': 8,
                    'category_id': 1,  # فرامل
                    'brand_id': 2,     # هوندا
                },
                {
                    'name': 'زيت محرك موبيل 1',
                    'description': 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات، يوفر حماية فائقة للمحرك',
                    'part_number': 'MOB-OIL-003',
                    'price': 95.00,
                    'quantity': 25,
                    'category_id': 6,  # زيوت
                    'brand_id': None,
                },
                {
                    'name': 'بطارية نيسان التيما',
                    'description': 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير، ضمان سنتين',
                    'part_number': 'NIS-BAT-004',
                    'price': 280.00,
                    'quantity': 5,
                    'category_id': 4,  # كهرباء
                    'brand_id': 3,     # نيسان
                },
                {
                    'name': 'مكيف هواء هيونداي إلنترا',
                    'description': 'كمبروسر مكيف هواء لسيارة هيونداي إلنترا، يوفر تبريد فعال وموفر للطاقة',
                    'part_number': 'HYU-AC-005',
                    'price': 450.00,
                    'quantity': 3,
                    'category_id': 3,  # تكييف
                    'brand_id': 4,     # هيونداي
                },
                {
                    'name': 'إطار ميشلان 205/55R16',
                    'description': 'إطار ميشلان عالي الجودة، مقاس 205/55R16، مناسب للسيارات المتوسطة',
                    'part_number': 'MICH-TIRE-006',
                    'price': 320.00,
                    'quantity': 12,
                    'category_id': 5,  # عجلات
                    'brand_id': None,
                }
            ]
            
            for product_data in demo_products:
                product = Product(
                    name=product_data['name'],
                    description=product_data['description'],
                    part_number=product_data['part_number'],
                    price=product_data['price'],
                    quantity=product_data['quantity'],
                    shop_id=demo_shop.id,
                    category_id=product_data['category_id'],
                    brand_id=product_data['brand_id'],
                    created_at=datetime.utcnow() - timedelta(days=random.randint(1, 30))
                )
                db.session.add(product)
            
            print("✅ تم إضافة 6 منتجات تجريبية")
            
            # إضافة تقييمات تجريبية
            demo_reviews = [
                {
                    'rating': 5,
                    'comment': 'محل ممتاز وخدمة رائعة، القطع أصلية والأسعار معقولة',
                    'reviewer_name': 'أحمد محمد',
                    'reviewer_email': '<EMAIL>'
                },
                {
                    'rating': 4,
                    'comment': 'تعامل جيد وسرعة في التوصيل، أنصح بالتعامل معهم',
                    'reviewer_name': 'سارة أحمد',
                    'reviewer_email': '<EMAIL>'
                },
                {
                    'rating': 5,
                    'comment': 'أفضل محل لقطع غيار السيارات في المنطقة، خبرة وأمانة',
                    'reviewer_name': 'محمد علي',
                    'reviewer_email': '<EMAIL>'
                }
            ]
            
            for review_data in demo_reviews:
                review = Review(
                    rating=review_data['rating'],
                    comment=review_data['comment'],
                    reviewer_name=review_data['reviewer_name'],
                    reviewer_email=review_data['reviewer_email'],
                    shop_id=demo_shop.id,
                    created_at=datetime.utcnow() - timedelta(days=random.randint(1, 15))
                )
                db.session.add(review)
            
            print("✅ تم إضافة 3 تقييمات تجريبية")
            
            db.session.commit()
            print("✅ تم حفظ جميع البيانات التجريبية بنجاح")
            
        else:
            print("ℹ️ البيانات التجريبية موجودة بالفعل")

if __name__ == '__main__':
    print("🚗 إعداد البيانات التجريبية لتطبيق قطع غيار السيارات")
    print("=" * 60)
    
    create_demo_data()
    
    print("\n" + "=" * 60)
    print("✅ تم الانتهاء من إعداد البيانات التجريبية")
    print("\n📋 الحسابات المتوفرة:")
    print("👤 المدير: admin / admin123")
    print("🏪 محل تجريبي: shop1 / shop123")
    print("\n🌐 يمكنك الآن تشغيل التطبيق والاستمتاع بالبيانات التجريبية!")
