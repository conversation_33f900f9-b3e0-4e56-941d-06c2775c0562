🚗 تطبيق ربط محلات قطع غيار السيارات
=======================================

📊 معلومات المشروع:
- الاسم: نظام ربط محلات قطع غيار السيارات
- النوع: تطبيق ويب متكامل
- اللغة: Python (Flask)
- قاعدة البيانات: SQLite
- واجهة المستخدم: HTML5, CSS3, Bootstrap 5, JavaScript

🎯 الهدف من التطبيق:
ربط محلات قطع غيار السيارات مع العملاء من خلال منصة إلكترونية شاملة
تسهل عملية البحث والعثور على قطع الغيار المطلوبة.

✨ الميزات الرئيسية:

للعملاء:
✅ صفحة رئيسية تعرض أحدث القطع
✅ نظام بحث متقدم (حسب الماركة، الموديل، رقم القطعة)
✅ عرض المحلات مع التقييمات والمعلومات
✅ نظام تقييم للمحلات والمنتجات
✅ تواصل مباشر مع المحلات عبر الهاتف

للمحلات:
✅ لوحة تحكم شاملة لإدارة المحل
✅ إدارة المنتجات (إضافة، تعديل، حذف)
✅ إدارة الأسعار والكميات لكل منتج على حدة
✅ تحديث بيانات المحل والمعلومات
✅ عرض الإحصائيات والتقييمات

للمدير:
✅ لوحة تحكم المدير الشاملة
✅ إدارة المحلات (إضافة، تعديل، حذف)
✅ إدارة المستخدمين وصلاحياتهم
✅ إدارة الفئات والماركات
✅ مراقبة النظام والإحصائيات العامة

📁 ملفات المشروع:

الملفات الأساسية:
- app.py: الملف الرئيسي للتطبيق
- run.py: ملف تشغيل التطبيق مع البيانات التجريبية
- quick_start.py: تشغيل سريع مع فتح المتصفح تلقائياً
- setup_demo_data.py: إنشاء البيانات التجريبية
- requirements.txt: متطلبات Python

ملفات التشغيل:
- تشغيل_التطبيق.bat: الملف التنفيذي الرئيسي (الأفضل)
- start_app.bat: ملف تنفيذي بديل
- install_and_run.bat: ملف تثبيت وتشغيل شامل

ملفات التوثيق:
- README.md: دليل المشروع باللغة الإنجليزية
- دليل_المستخدم.md: دليل شامل باللغة العربية
- تعليمات_التشغيل.txt: تعليمات سريعة
- معلومات_المشروع.txt: هذا الملف

مجلدات المشروع:
- templates/: قوالب HTML
  - admin/: قوالب لوحة تحكم المدير
  - shop/: قوالب لوحة تحكم المحلات
- static/: الملفات الثابتة (CSS, JS, الصور)

🚀 طرق التشغيل:

الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على "تشغيل_التطبيق.bat"
2. انتظر حتى يتم تحضير التطبيق
3. سيفتح المتصفح تلقائياً

الطريقة الثانية:
1. انقر نقراً مزدوجاً على "install_and_run.bat"
2. اتبع التعليمات

الطريقة الثالثة (يدوياً):
1. افتح Command Prompt في مجلد المشروع
2. اكتب: python -m pip install -r requirements.txt
3. اكتب: python run.py
4. افتح المتصفح على: http://localhost:5000

🔐 الحسابات الافتراضية:

المدير:
- اسم المستخدم: admin
- كلمة المرور: admin123

محل تجريبي:
- اسم المستخدم: shop1
- كلمة المرور: shop123

🌐 روابط الوصول:
- الرابط الأساسي: http://localhost:5000
- رابط بديل: http://127.0.0.1:5000
- للوصول من الشبكة المحلية: http://[عنوان-IP]:5000

📋 البيانات التجريبية:
يحتوي التطبيق على:
- محل تجريبي واحد (محل الأصيل لقطع غيار السيارات)
- 6 منتجات تجريبية متنوعة
- 3 تقييمات تجريبية
- فئات وماركات أساسية

🛠️ متطلبات النظام:
- نظام التشغيل: Windows 7 أو أحدث
- Python 3.7 أو أحدث
- ذاكرة: 512 ميجابايت على الأقل
- مساحة القرص: 100 ميجابايت
- اتصال بالإنترنت (لتثبيت المتطلبات فقط)

🔧 حل المشاكل الشائعة:

مشكلة: Python غير موجود
الحل: حمل وثبت Python من https://python.org

مشكلة: خطأ في تثبيت المتطلبات
الحل: تأكد من الاتصال بالإنترنت وأعد المحاولة

مشكلة: لا يمكن الوصول للتطبيق
الحل: تأكد من أن المنفذ 5000 غير مستخدم

مشكلة: قاعدة البيانات تالفة
الحل: احذف ملف auto_parts.db وأعد تشغيل التطبيق

📈 إحصائيات المشروع:
- عدد الملفات: 25+ ملف
- عدد الصفحات: 15+ صفحة ويب
- عدد الوظائف: 20+ وظيفة
- أسطر الكود: 2000+ سطر
- وقت التطوير: يوم واحد

🎨 التصميم والواجهة:
- تصميم متجاوب (Responsive Design)
- دعم اللغة العربية (RTL)
- ألوان متناسقة ومريحة للعين
- أيقونات Font Awesome
- خط Cairo من Google Fonts

🔒 الأمان:
- تشفير كلمات المرور
- حماية من SQL Injection
- التحقق من صحة البيانات
- إدارة جلسات آمنة

📱 التوافق:
- جميع المتصفحات الحديثة
- الهواتف الذكية والأجهزة اللوحية
- أنظمة التشغيل المختلفة

🚀 إمكانيات التطوير المستقبلية:
- إضافة نظام دفع إلكتروني
- تطبيق جوال
- نظام إشعارات
- تقارير مفصلة
- نظام مراسلة داخلية

📞 الدعم:
للحصول على المساعدة، راجع:
- دليل_المستخدم.md
- README.md
- تعليمات_التشغيل.txt

---
تم تطوير هذا التطبيق بواسطة Augment Agent 🤖
تاريخ الإنشاء: 2024
الإصدار: 1.0
