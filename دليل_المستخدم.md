# 🚗 دليل المستخدم - تطبيق ربط محلات قطع غيار السيارات

## 📋 جدول المحتويات
1. [نظرة عامة](#نظرة-عامة)
2. [طرق التشغيل](#طرق-التشغيل)
3. [الحسابات الافتراضية](#الحسابات-الافتراضية)
4. [دليل المدير](#دليل-المدير)
5. [دليل المحلات](#دليل-المحلات)
6. [دليل العملاء](#دليل-العملاء)
7. [حل المشاكل](#حل-المشاكل)

## 🎯 نظرة عامة

تطبيق ويب متكامل يربط محلات قطع غيار السيارات مع العملاء، يوفر:

### للعملاء:
- 🏠 صفحة رئيسية تعرض أحدث القطع
- 🔍 بحث متقدم حسب الماركة والموديل ورقم القطعة
- 🏪 عرض المحلات مع التقييمات
- ⭐ نظام تقييم للمحلات
- 📞 تواصل مباشر مع المحلات

### للمحلات:
- 📊 لوحة تحكم شاملة
- 📦 إدارة المنتجات (إضافة، تعديل، حذف)
- 💰 إدارة الأسعار والكميات
- 📝 تحديث بيانات المحل
- 📈 عرض الإحصائيات والتقييمات

### للمدير:
- 🎛️ لوحة تحكم المدير
- 🏪 إدارة المحلات والمستخدمين
- 📊 مراقبة النظام والإحصائيات
- 🏷️ إدارة الفئات والماركات

## 🚀 طرق التشغيل

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف `تشغيل_التطبيق.bat`
2. انتظر حتى يتم تحضير التطبيق
3. سيفتح المتصفح تلقائياً على http://localhost:5000

### الطريقة الثانية:
1. انقر نقراً مزدوجاً على ملف `install_and_run.bat`
2. اتبع التعليمات على الشاشة

### الطريقة الثالثة (يدوياً):
```bash
python -m pip install -r requirements.txt
python run.py
```

## 🔐 الحسابات الافتراضية

### 👨‍💼 حساب المدير
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** إدارة كاملة للنظام

### 🏪 محل تجريبي
- **اسم المستخدم:** `shop1`
- **كلمة المرور:** `shop123`
- **الصلاحيات:** إدارة منتجات المحل

## 👨‍💼 دليل المدير

### الوصول للوحة التحكم:
1. سجل دخول بحساب المدير
2. ستنتقل تلقائياً للوحة تحكم المدير

### إدارة المحلات:
1. **إضافة محل جديد:**
   - اذهب إلى "إدارة المحلات" → "إضافة محل جديد"
   - املأ بيانات المستخدم والمحل
   - اضغط "إضافة المحل"

2. **تعديل محل:**
   - اذهب إلى "إدارة المحلات"
   - اضغط على أيقونة التعديل بجانب المحل
   - عدّل البيانات واضغط "حفظ"

3. **تفعيل/إلغاء تفعيل محل:**
   - من صفحة تعديل المحل
   - حدد/ألغ تحديد "نشط"

### إدارة المستخدمين:
- عرض جميع المستخدمين
- تعديل صلاحيات المستخدمين
- تفعيل/إلغاء تفعيل الحسابات

### إدارة الفئات والماركات:
- إضافة فئات جديدة لقطع الغيار
- إضافة ماركات سيارات جديدة
- تعديل الفئات والماركات الموجودة

## 🏪 دليل المحلات

### الوصول للوحة التحكم:
1. سجل دخول بحساب المحل
2. ستنتقل تلقائياً للوحة تحكم المحل

### إدارة المنتجات:

#### إضافة منتج جديد:
1. اذهب إلى "إدارة المنتجات" → "إضافة منتج جديد"
2. املأ البيانات المطلوبة:
   - **اسم المنتج** (مطلوب)
   - **رقم القطعة** (اختياري)
   - **وصف المنتج**
   - **السعر** (مطلوب)
   - **الكمية المتوفرة** (مطلوب)
   - **الفئة** (اختياري)
   - **الماركة** (اختياري)
   - **موديل السيارة** (اختياري)
   - **رابط الصورة** (اختياري)
3. اضغط "حفظ المنتج"

#### تعديل منتج:
1. اذهب إلى "إدارة المنتجات"
2. اضغط على أيقونة التعديل بجانب المنتج
3. عدّل البيانات المطلوبة
4. اضغط "حفظ التغييرات"

#### حذف منتج:
1. من صفحة "إدارة المنتجات" أو "تعديل المنتج"
2. اضغط على أيقونة الحذف
3. أكد الحذف في النافذة المنبثقة

### تحديث بيانات المحل:
1. اذهب إلى "تعديل البيانات"
2. عدّل المعلومات المطلوبة
3. اضغط "حفظ التغييرات"

### عرض الإحصائيات:
- إجمالي المنتجات
- المنتجات النشطة
- تقييم المحل
- عدد التقييمات

## 👥 دليل العملاء

### البحث عن قطع الغيار:

#### البحث السريع:
1. من الصفحة الرئيسية
2. استخدم شريط البحث السريع
3. اختر الماركة أو أدخل رقم القطعة

#### البحث المتقدم:
1. اذهب إلى صفحة "البحث"
2. استخدم الفلاتر المتاحة:
   - **البحث العام:** اسم القطعة
   - **الماركة:** ماركة السيارة
   - **الفئة:** نوع القطعة
   - **رقم القطعة:** الرقم المحدد
3. اضغط "بحث"

### عرض المحلات:
1. اذهب إلى صفحة "المحلات"
2. تصفح المحلات المتاحة
3. اضغط على "عرض المنتجات" لرؤية منتجات محل معين

### التواصل مع المحلات:
1. من صفحة المنتج أو المحل
2. اضغط على "اتصل بالمحل"
3. ستظهر معلومات الاتصال
4. اضغط "اتصل الآن" للاتصال المباشر

### إضافة تقييم:
1. من صفحة المحل
2. اضغط على "إضافة تقييم"
3. اختر التقييم (1-5 نجوم)
4. أدخل اسمك والتعليق
5. اضغط "إضافة التقييم"

## 🔧 حل المشاكل

### مشكلة: Python غير موجود
**الحل:**
1. حمل Python من https://python.org/downloads
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
3. أعد تشغيل الكمبيوتر
4. جرب تشغيل التطبيق مرة أخرى

### مشكلة: خطأ في تثبيت المتطلبات
**الحل:**
```bash
python -m pip install --upgrade pip
python -m pip install Flask Flask-SQLAlchemy Werkzeug
```

### مشكلة: لا يمكن الوصول للتطبيق
**الأسباب المحتملة:**
- المنفذ 5000 مستخدم بالفعل
- جدار الحماية يحجب الاتصال
- مشكلة في الشبكة

**الحلول:**
1. أغلق أي تطبيق يستخدم المنفذ 5000
2. جرب الوصول عبر 127.0.0.1:5000
3. تأكد من إعدادات جدار الحماية

### مشكلة: قاعدة البيانات تالفة
**الحل:**
1. أغلق التطبيق
2. احذف ملف `auto_parts.db`
3. أعد تشغيل التطبيق
4. ستُنشأ قاعدة بيانات جديدة تلقائياً

### مشكلة: البيانات التجريبية لا تظهر
**الحل:**
```bash
python setup_demo_data.py
```

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات
4. جرب إعادة تشغيل التطبيق

### معلومات تقنية:
- **اللغة:** Python 3.7+
- **إطار العمل:** Flask
- **قاعدة البيانات:** SQLite
- **واجهة المستخدم:** Bootstrap 5

---

**تم تطوير هذا التطبيق بواسطة Augment Agent** 🤖

*آخر تحديث: 2024*
