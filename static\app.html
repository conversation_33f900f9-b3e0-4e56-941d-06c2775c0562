<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق قطع غيار السيارات - مع قاعدة البيانات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
        }
        
        .product-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .product-image {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .table td {
            vertical-align: middle;
            font-size: 0.9rem;
        }
        
        .shop-badge {
            display: inline-block;
            margin: 2px;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .product-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                
                <!-- Status indicator -->
                <span class="nav-link" id="dbStatus">
                    <i class="fas fa-database text-success me-1"></i>
                    متصل
                </span>
                
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showDashboard()" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section text-center" id="heroSection">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
            <p class="lead mb-4">الآن مع قاعدة بيانات حقيقية - جميع التغييرات محفوظة!</p>
            <div class="alert alert-success d-inline-block">
                <h5><i class="fas fa-database me-2"></i>قاعدة البيانات تعمل!</h5>
                <p class="mb-0">Flask + SQLite - حفظ دائم للبيانات</p>
            </div>
        </div>
    </div>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="fw-bold">قطع الغيار المتوفرة</h2>
                    <p class="text-muted">جميع قطع الغيار مع المحلات والأسعار - محفوظة في قاعدة البيانات</p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول قطع الغيار والمحلات
                    </h5>
                    <button class="btn btn-light btn-sm" onclick="loadProducts()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="productsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 25%;">اسم القطعة</th>
                                    <th style="width: 15%;">رقم القطعة</th>
                                    <th style="width: 35%;">المحلات والأسعار والكميات</th>
                                    <th style="width: 15%;">أقل سعر</th>
                                    <th style="width: 10%;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="loading show">
                                            <i class="fas fa-spinner fa-spin me-2"></i>
                                            جاري تحميل المنتجات من قاعدة البيانات...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white text-center">
                        <div class="card-body">
                            <h4 id="totalProducts">0</h4>
                            <p class="mb-0">إجمالي القطع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <h4 id="totalShops">0</h4>
                            <p class="mb-0">إجمالي المحلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <h4 id="totalQuantity">0</h4>
                            <p class="mb-0">إجمالي الكميات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white text-center">
                        <div class="card-body">
                            <h4 id="averagePrice">0</h4>
                            <p class="mb-0">متوسط الأسعار</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="loginForm" class="login-form">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                                    <label for="username">اسم المستخدم</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                                    <label for="password">كلمة المرور</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    دخول
                                </button>
                                
                                <div id="loginAlert" class="alert" style="display: none;"></div>
                            </form>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>حسابات تجريبية:</h6>
                                <p class="mb-1"><strong>المدير:</strong> admin / admin123</p>
                                <p class="mb-0"><strong>المحل:</strong> shop1 / shop123</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let currentUser = null;
        const API_BASE = '';  // Same origin
        
        // Show page function
        function showPage(pageId) {
            // Hide hero section for other pages
            const heroSection = document.getElementById('heroSection');
            if (pageId !== 'home') {
                heroSection.style.display = 'none';
            } else {
                heroSection.style.display = 'block';
            }
            
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // Load content based on page
            if (pageId === 'home') {
                loadProducts();
            } else if (pageId === 'shops') {
                loadShops();
            }
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
            `;
            
            container.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Hide notification after 4 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
        
        // Load products from database
        async function loadProducts() {
            try {
                const response = await fetch(`${API_BASE}/api/products`);
                const products = await response.json();
                
                displayProducts(products);
                updateSummaryCards(products);
                
            } catch (error) {
                console.error('Error loading products:', error);
                showNotification('خطأ في تحميل المنتجات من قاعدة البيانات', 'danger');
                
                // Update database status
                document.getElementById('dbStatus').innerHTML = '<i class="fas fa-database text-danger me-1"></i>خطأ في الاتصال';
            }
        }
        
        // Display products in table
        function displayProducts(products) {
            const tableBody = document.getElementById('productsTableBody');
            tableBody.innerHTML = '';
            
            if (products.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد منتجات في قاعدة البيانات
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }
            
            products.forEach(product => {
                const prices = product.shopDetails.map(shop => shop.price);
                const minPrice = Math.min(...prices);
                const maxPrice = Math.max(...prices);
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                
                // Create shops list
                const shopsList = product.shopDetails.map(shopDetail => {
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    ${product.image ? 
                                        `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">` : 
                                        '<div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;"><i class="fas fa-cog text-muted"></i></div>'
                                    }
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل | 
                                إجمالي الكمية: <span class="fw-bold">${totalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                                ${minPrice !== maxPrice ? `<br><small class="text-muted">أعلى: ${maxPrice} ريال</small>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }
        
        // Update summary cards
        function updateSummaryCards(products) {
            const totalProducts = products.length;
            const uniqueShops = new Set();
            let totalQuantity = 0;
            let totalPrice = 0;
            let priceCount = 0;
            
            products.forEach(product => {
                product.shopDetails.forEach(shop => {
                    uniqueShops.add(shop.shopName);
                    totalQuantity += shop.quantity;
                    totalPrice += shop.price;
                    priceCount++;
                });
            });
            
            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('totalShops').textContent = uniqueShops.size;
            document.getElementById('totalQuantity').textContent = totalQuantity;
            document.getElementById('averagePrice').textContent = priceCount > 0 ? Math.round(totalPrice / priceCount) + ' ريال' : '0 ريال';
        }
        
        // Handle login
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('loginAlert');
            const loginBtn = document.getElementById('loginBtn');
            
            // Show loading
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
            loginBtn.disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/api/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentUser = result.user;
                    
                    // Update UI
                    document.getElementById('loginLink').style.display = 'none';
                    document.getElementById('logoutLink').style.display = 'inline';
                    document.getElementById('dashboardLink').style.display = 'inline';
                    document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                    document.getElementById('userWelcome').style.display = 'inline';
                    document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;
                    
                    // Show success message
                    alertDiv.className = 'alert alert-success';
                    alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                    alertDiv.style.display = 'block';
                    
                    showNotification(`مرحباً ${currentUser.name}! تم تسجيل الدخول بنجاح.`, 'success');
                    
                    // Redirect to home
                    setTimeout(() => {
                        showPage('home');
                    }, 1000);
                    
                } else {
                    alertDiv.className = 'alert alert-danger';
                    alertDiv.textContent = result.message;
                    alertDiv.style.display = 'block';
                }
                
            } catch (error) {
                console.error('Login error:', error);
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'خطأ في الاتصال بالخادم';
                alertDiv.style.display = 'block';
            } finally {
                // Reset button
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>دخول';
                loginBtn.disabled = false;
            }
        }
        
        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';
            
            showNotification('تم تسجيل الخروج بنجاح', 'info');
            showPage('home');
        }
        
        // Show dashboard (placeholder)
        function showDashboard() {
            showNotification('لوحة التحكم قيد التطوير...', 'info');
        }
        
        // Show product detail (placeholder)
        function showProductDetail(productId) {
            showNotification('تفاصيل المنتج قيد التطوير...', 'info');
        }
        
        // Load shops (placeholder)
        function loadShops() {
            showNotification('صفحة المحلات قيد التطوير...', 'info');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add login form event listener
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            
            // Load initial data
            loadProducts();
            
            // Show welcome message
            showNotification('مرحباً بك في تطبيق قطع غيار السيارات مع قاعدة البيانات!', 'success');
        });
    </script>
</body>
</html>
