@echo off
chcp 65001 >nul
title فحص شامل لمشاكل التطبيق

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                      فحص شامل للنظام                       █
echo ████████████████████████████████████████████████████████████████
echo.

echo 📋 تقرير التشخيص الشامل
echo ========================
echo.

echo 🔍 1. فحص نظام التشغيل...
echo النظام: %OS%
echo المعالج: %PROCESSOR_ARCHITECTURE%
echo المستخدم: %USERNAME%
echo.

echo 🔍 2. فحص Python...
python --version 2>nul
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. حمل Python من: https://python.org/downloads
    echo    2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo    3. أعد تشغيل الكمبيوتر بعد التثبيت
    echo    4. أو استخدم py بدلاً من python
    echo.
    
    echo 🧪 اختبار py...
    py --version 2>nul
    if errorlevel 1 (
        echo ❌ py أيضاً غير متوفر
    ) else (
        echo ✅ py متوفر - يمكن استخدامه بدلاً من python
        set PYTHON_CMD=py
        goto :python_found
    )
    
    goto :no_python
) else (
    echo ✅ Python متوفر
    python --version
    set PYTHON_CMD=python
    goto :python_found
)

:no_python
echo.
echo ████████████████████████████████████████████████████████████████
echo █                    لا يوجد Python مثبت                     █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 الحلول البديلة المتوفرة:
echo.
echo 1. 📱 التطبيق الثابت (يعمل الآن):
echo    - افتح ملف: تطبيق_ويب_ثابت.html
echo    - يحتوي على جميع الميزات
echo    - يعمل بدون Python
echo.
echo 2. 🔧 تثبيت Python:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل أحدث إصدار
echo    - تأكد من تحديد "Add Python to PATH"
echo    - أعد تشغيل الكمبيوتر
echo.
goto :end

:python_found
echo.
echo 🔍 3. فحص pip...
%PYTHON_CMD% -m pip --version 2>nul
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 🔧 جاري محاولة إصلاح pip...
    %PYTHON_CMD% -m ensurepip --upgrade
) else (
    echo ✅ pip متوفر
    %PYTHON_CMD% -m pip --version
)

echo.
echo 🔍 4. فحص Flask...
%PYTHON_CMD% -c "import flask; print('Flask version:', flask.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    echo 📦 جاري تثبيت Flask...
    %PYTHON_CMD% -m pip install Flask
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        echo 🔧 جرب: %PYTHON_CMD% -m pip install --user Flask
    ) else (
        echo ✅ تم تثبيت Flask بنجاح
    )
) else (
    echo ✅ Flask مثبت ويعمل
)

echo.
echo 🔍 5. فحص المنفذ 5000...
netstat -an | findstr :5000 >nul
if not errorlevel 1 (
    echo ⚠️ المنفذ 5000 مستخدم بالفعل
    echo 🔧 أغلق التطبيق الذي يستخدم المنفذ
) else (
    echo ✅ المنفذ 5000 متاح
)

echo.
echo 🔍 6. فحص ملفات التطبيق...
if exist app.py (
    echo ✅ app.py موجود
) else (
    echo ❌ app.py مفقود
)

if exist working_app.py (
    echo ✅ working_app.py موجود
) else (
    echo ❌ working_app.py مفقود
)

if exist "تطبيق_ويب_ثابت.html" (
    echo ✅ التطبيق الثابت موجود
) else (
    echo ❌ التطبيق الثابت مفقود
)

echo.
echo 🧪 7. اختبار تشغيل Python...
%PYTHON_CMD% -c "print('Python يعمل بشكل طبيعي')" 2>nul
if errorlevel 1 (
    echo ❌ مشكلة في تشغيل Python
) else (
    echo ✅ Python يعمل بشكل طبيعي
)

echo.
echo ████████████████████████████████████████████████████████████████
echo █                      نتائج التشخيص                         █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 الحلول المتوفرة:
echo.
echo 1. 📱 التطبيق الثابت (الأفضل حالياً):
echo    - افتح: تطبيق_ويب_ثابت.html
echo    - يعمل بدون Python
echo    - جميع الميزات متوفرة
echo.
echo 2. 🐍 التطبيق بـ Python:
echo    - %PYTHON_CMD% working_app.py
echo    - أو: %PYTHON_CMD% app.py
echo.
echo 3. 🔧 إصلاح المشاكل:
echo    - تثبيت Python إذا لم يكن موجود
echo    - تثبيت Flask: %PYTHON_CMD% -m pip install Flask
echo.

:end
echo.
echo 📞 للمساعدة الإضافية:
echo    - راجع ملف README.md
echo    - راجع ملف دليل_المستخدم.md
echo.
pause
