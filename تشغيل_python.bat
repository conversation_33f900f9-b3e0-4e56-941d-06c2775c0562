@echo off
chcp 65001 >nul
title تشغيل تطبيق قطع غيار السيارات بـ Python

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █  🐍 تشغيل تطبيق قطع غيار السيارات بـ Python                █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 البحث عن Python...

REM محاولة python
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python
    set PYTHON_CMD=python
    goto :found_python
)

REM محاولة py
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على py
    set PYTHON_CMD=py
    goto :found_python
)

REM محاولة python3
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python3
    set PYTHON_CMD=python3
    goto :found_python
)

REM لم يتم العثور على Python
echo ❌ لم يتم العثور على Python
echo.
echo 📥 يرجى تثبيت Python أولاً:
echo    1. اذهب إلى: https://python.org/downloads
echo    2. حمل أحدث إصدار من Python
echo    3. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
echo    4. أعد تشغيل الكمبيوتر بعد التثبيت
echo    5. أعد تشغيل هذا الملف
echo.
echo 🔄 أو استخدم التطبيق الثابت الذي يعمل بدون Python:
echo    افتح ملف: تطبيق_ويب_ثابت.html
echo.
pause
exit /b 1

:found_python
echo.
echo 🚀 تشغيل الخادم البسيط...
echo.
echo ████████████████████████████████████████████████████████████████
echo █                      معلومات الخادم                        █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🌐 الرابط: http://localhost:5000
echo 👤 المدير: admin / admin123
echo 🏪 المحل: shop1 / shop123
echo.
echo 📋 الميزات:
echo    ✅ خادم Python HTTP بسيط
echo    ✅ صفحة رئيسية تفاعلية
echo    ✅ تسجيل دخول يعمل
echo    ✅ عرض المنتجات من API
echo    ✅ بدون مكتبات خارجية
echo.
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo ████████████████████████████████████████████████████████████████
echo.

%PYTHON_CMD% simple_server.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل الخادم
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من أن المنفذ 5000 غير مستخدم
    echo    2. جرب تشغيل الأمر يدوياً: %PYTHON_CMD% simple_server.py
    echo    3. أو استخدم التطبيق الثابت: تطبيق_ويب_ثابت.html
    echo.
)

echo.
echo ✅ تم إغلاق الخادم
pause
