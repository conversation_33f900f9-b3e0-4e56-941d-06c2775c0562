#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ربط محلات قطع غيار السيارات
Auto Parts Shops Connection System

تطبيق ويب متكامل لربط محلات قطع غيار السيارات مع العملاء
يحتوي على:
- صفحة رئيسية تعرض أحدث القطع
- نظام تسجيل دخول منفصل للمحلات
- لوحة تحكم خاصة لكل محل
- نظام بحث متقدم
- نظام تقييم للمحلات والمنتجات
- إدارة شاملة للمنتجات والأسعار والكميات
"""

from app import app

if __name__ == '__main__':
    print("=" * 60)
    print("🚗 تطبيق ربط محلات قطع غيار السيارات")
    print("Auto Parts Shops Connection System")
    print("=" * 60)
    print()

    # إضافة البيانات التجريبية
    try:
        from setup_demo_data import create_demo_data
        create_demo_data()
        print()
    except Exception as e:
        print(f"⚠️ تحذير: لم يتم إنشاء البيانات التجريبية: {e}")
        print()

    print("🌐 التطبيق يعمل على: http://localhost:5000")
    print("🌐 أو: http://127.0.0.1:5000")
    print()
    print("👤 الحسابات المتوفرة:")
    print("   🔑 المدير: admin / admin123")
    print("   🏪 محل تجريبي: shop1 / shop123")
    print()
    print("📋 الميزات المتوفرة:")
    print("   ✅ صفحة رئيسية تعرض أحدث القطع")
    print("   ✅ نظام تسجيل دخول للمحلات والمدير")
    print("   ✅ لوحة تحكم للمحلات لإدارة المنتجات")
    print("   ✅ لوحة تحكم للمدير لإدارة المحلات")
    print("   ✅ نظام بحث متقدم حسب الماركة والموديل")
    print("   ✅ نظام تقييم للمحلات والمنتجات")
    print("   ✅ إدارة الأسعار والكميات")
    print()
    print("🛑 لإيقاف التطبيق: اضغط Ctrl+C")
    print("=" * 60)

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n✅ تم إيقاف التطبيق بنجاح")
        print("شكراً لاستخدام تطبيق ربط محلات قطع غيار السيارات! 🚗")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("تأكد من أن المنفذ 5000 غير مستخدم وأعد المحاولة")
