<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
        
        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c3e50 !important;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('search')">البحث</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">
                            أفضل قطع غيار السيارات
                        </h1>
                        <p class="lead mb-4">
                            اكتشف أفضل قطع غيار السيارات من محلات موثوقة ومعتمدة
                        </p>
                        <button class="btn btn-light btn-lg" onclick="showPage('search')">
                            <i class="fas fa-search me-2"></i>
                            ابحث الآن
                        </button>
                    </div>
                    <div class="col-lg-6 text-center">
                        <i class="fas fa-car fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section class="py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 class="fw-bold">أحدث القطع</h2>
                        <p class="text-muted">اكتشف أحدث قطع الغيار المضافة</p>
                    </div>
                </div>
                
                <div class="row" id="productsContainer">
                    <!-- Products will be loaded here -->
                </div>
            </div>
        </section>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h3 class="fw-bold">تسجيل الدخول</h3>
                            </div>
                            
                            <div id="loginAlert" class="alert" style="display: none;"></div>
                            
                            <form id="loginForm" onsubmit="handleLogin(event)">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">حسابات تجريبية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>المدير:</strong><br>
                                            <small>admin / admin123</small>
                                        </div>
                                        <div class="col-6">
                                            <strong>المحل:</strong><br>
                                            <small>shop1 / shop123</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة تحكم المدير
                            </h2>
                            <p class="mb-0">مرحباً بك في لوحة التحكم الرئيسية</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">5</h4>
                        <p class="mb-0">إجمالي المحلات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">24</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">8</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">التقييمات</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المدير تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة النظام بالكامل - إضافة محلات، إدارة مستخدمين، مراقبة الإحصائيات</p>
            </div>
        </div>
    </div>

    <!-- Shop Dashboard -->
    <div id="shop" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-store me-2"></i>
                                لوحة تحكم المحل
                            </h2>
                            <p class="mb-0">محل الأصيل لقطع غيار السيارات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">15</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">المنتجات النشطة</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">4.5</h4>
                        <p class="mb-0">تقييم المحل</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أحدث المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>رقم القطعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>فلتر هواء تويوتا كامري</td>
                                    <td>85 ريال</td>
                                    <td>15</td>
                                    <td>TOY-AF-001</td>
                                </tr>
                                <tr>
                                    <td>تيل فرامل هوندا أكورد</td>
                                    <td>120 ريال</td>
                                    <td>8</td>
                                    <td>HON-BP-002</td>
                                </tr>
                                <tr>
                                    <td>زيت محرك موبيل 1</td>
                                    <td>95 ريال</td>
                                    <td>25</td>
                                    <td>MOB-OIL-003</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success mt-4">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المحل تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة منتجاتك - إضافة، تعديل، حذف المنتجات وإدارة الأسعار والكميات</p>
            </div>
        </div>
    </div>

    <!-- Search Page -->
    <div id="search" class="page-content">
        <div class="container py-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث المتقدم</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="اسم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select">
                                <option>جميع الماركات</option>
                                <option>تويوتا</option>
                                <option>هوندا</option>
                                <option>نيسان</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="رقم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100">بحث</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-search me-2"></i>نظام البحث المتقدم</h5>
                <p class="mb-0">يمكنك البحث عن قطع الغيار حسب الاسم، الماركة، أو رقم القطعة</p>
            </div>
        </div>
    </div>

    <!-- Shops Page -->
    <div id="shops" class="page-content">
        <div class="container py-4">
            <h2 class="text-center mb-4">جميع المحلات</h2>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-store fa-3x text-primary mb-3"></i>
                            <h5>محل الأصيل لقطع غيار السيارات</h5>
                            <p class="text-muted">محل متخصص في قطع غيار السيارات اليابانية والكورية</p>
                            <div class="rating mb-2">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star-half-alt text-warning"></i>
                                <span class="ms-2">(4.5)</span>
                            </div>
                            <p class="text-muted"><i class="fas fa-phone me-1"></i> +966501234567</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-store me-2"></i>المحلات المعتمدة</h5>
                <p class="mb-0">جميع المحلات مراجعة ومعتمدة لضمان جودة المنتجات والخدمة</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample products data
        const products = [
            {
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020',
                price: 85,
                quantity: 15,
                partNumber: 'TOY-AF-001',
                shop: 'محل الأصيل'
            },
            {
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد',
                price: 120,
                quantity: 8,
                partNumber: 'HON-BP-002',
                shop: 'محل الأصيل'
            },
            {
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات',
                price: 95,
                quantity: 25,
                partNumber: 'MOB-OIL-003',
                shop: 'محل الأصيل'
            },
            {
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير',
                price: 280,
                quantity: 5,
                partNumber: 'NIS-BAT-004',
                shop: 'محل الأصيل'
            }
        ];

        // Users data
        const users = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل' }
        };

        // Current user
        let currentUser = null;

        // Show page function
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            
            // Load products if home page
            if (pageId === 'home') {
                loadProducts();
            }
        }

        // Load products
        function loadProducts() {
            const container = document.getElementById('productsContainer');
            container.innerHTML = '';
            
            products.forEach(product => {
                const productCard = `
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card product-card h-100">
                            <div class="product-image">
                                <i class="fas fa-cog fa-3x text-muted"></i>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="card-text text-muted small">${product.description.substring(0, 50)}...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold text-primary">${product.price} ريال</span>
                                    <small class="text-muted">${product.shop}</small>
                                </div>
                                <small class="text-muted d-block mt-1">رقم القطعة: ${product.partNumber}</small>
                                <div class="mt-2">
                                    <span class="badge bg-success">متوفر (${product.quantity})</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <button class="btn btn-primary btn-sm w-100" onclick="contactShop()">
                                    <i class="fas fa-phone me-1"></i>
                                    اتصل بالمحل
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('loginAlert');
            
            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };
                
                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;
                
                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';
                
                // Redirect to dashboard
                setTimeout(() => {
                    if (currentUser.role === 'admin') {
                        showPage('admin');
                    } else {
                        showPage('shop');
                    }
                }, 1000);
                
            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';
            showPage('home');
        }

        // Contact shop
        function contactShop() {
            alert('سيتم الاتصال بالمحل على الرقم: +966501234567');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
        });
    </script>
</body>
</html>
