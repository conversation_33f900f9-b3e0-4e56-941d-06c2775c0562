<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
        
        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c3e50 !important;
        }
        
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
        }

        .product-detail-modal .modal-dialog {
            max-width: 800px;
        }

        .shop-badge {
            display: inline-block;
            margin: 2px;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .product-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            vertical-align: middle;
            font-size: 0.9rem;
        }

        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
        }

        .badge-sm {
            font-size: 0.7rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .shops-list {
            max-height: 100px;
            overflow-y: auto;
        }

        .shop-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 4px 8px;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('search')">البحث</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showDashboard()" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">
                            أفضل قطع غيار السيارات
                        </h1>
                        <p class="lead mb-4">
                            اكتشف أفضل قطع غيار السيارات من محلات موثوقة ومعتمدة
                        </p>
                        <button class="btn btn-light btn-lg" onclick="showPage('search')">
                            <i class="fas fa-search me-2"></i>
                            ابحث الآن
                        </button>
                    </div>
                    <div class="col-lg-6 text-center">
                        <i class="fas fa-car fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Table Section -->
        <section class="py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 class="fw-bold">قطع الغيار المتوفرة</h2>
                        <p class="text-muted">جميع قطع الغيار مع المحلات والأسعار</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            جدول قطع الغيار والمحلات
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="productsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 25%;">اسم القطعة</th>
                                        <th style="width: 15%;">رقم القطعة</th>
                                        <th style="width: 35%;">المحلات والأسعار والكميات</th>
                                        <th style="width: 15%;">أقل سعر</th>
                                        <th style="width: 10%;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- Products table rows will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <h4 id="totalProducts">0</h4>
                                <p class="mb-0">إجمالي القطع</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <h4 id="totalShops">0</h4>
                                <p class="mb-0">إجمالي المحلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <h4 id="totalQuantity">0</h4>
                                <p class="mb-0">إجمالي الكميات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <h4 id="averagePrice">0</h4>
                                <p class="mb-0">متوسط الأسعار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h3 class="fw-bold">تسجيل الدخول</h3>
                            </div>
                            
                            <div id="loginAlert" class="alert" style="display: none;"></div>
                            
                            <form id="loginForm" onsubmit="handleLogin(event)">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">حسابات تجريبية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>المدير:</strong><br>
                                            <small>admin / admin123</small>
                                        </div>
                                        <div class="col-6">
                                            <strong>المحل:</strong><br>
                                            <small>shop1 / shop123</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة تحكم المدير
                            </h2>
                            <p class="mb-0">مرحباً بك في لوحة التحكم الرئيسية</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">5</h4>
                        <p class="mb-0">إجمالي المحلات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">24</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">8</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">التقييمات</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المدير تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة النظام بالكامل - إضافة محلات، إدارة مستخدمين، مراقبة الإحصائيات</p>
            </div>
        </div>
    </div>

    <!-- Shop Dashboard -->
    <div id="shop" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-store me-2"></i>
                                لوحة تحكم المحل
                            </h2>
                            <p class="mb-0">محل الأصيل لقطع غيار السيارات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">15</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">المنتجات النشطة</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">4.5</h4>
                        <p class="mb-0">تقييم المحل</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أحدث المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>رقم القطعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>فلتر هواء تويوتا كامري</td>
                                    <td>85 ريال</td>
                                    <td>15</td>
                                    <td>TOY-AF-001</td>
                                </tr>
                                <tr>
                                    <td>تيل فرامل هوندا أكورد</td>
                                    <td>120 ريال</td>
                                    <td>8</td>
                                    <td>HON-BP-002</td>
                                </tr>
                                <tr>
                                    <td>زيت محرك موبيل 1</td>
                                    <td>95 ريال</td>
                                    <td>25</td>
                                    <td>MOB-OIL-003</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">إدارة المنتجات</h5>
                            <button class="btn btn-success" onclick="showPage('add-product')">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row" id="shopProductsContainer">
                                <!-- Shop products will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-success mt-4">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المحل تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة منتجاتك - إضافة، تعديل، حذف المنتجات وإدارة الأسعار والكميات</p>
            </div>
        </div>
    </div>

    <!-- Search Page -->
    <div id="search" class="page-content">
        <div class="container py-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث المتقدم</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="اسم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select">
                                <option>جميع الماركات</option>
                                <option>تويوتا</option>
                                <option>هوندا</option>
                                <option>نيسان</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="رقم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100">بحث</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-search me-2"></i>نظام البحث المتقدم</h5>
                <p class="mb-0">يمكنك البحث عن قطع الغيار حسب الاسم، الماركة، أو رقم القطعة</p>
            </div>
        </div>
    </div>

    <!-- Shops Page -->
    <div id="shops" class="page-content">
        <div class="container py-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">جميع المحلات</h2>
                <button class="btn btn-success" onclick="showPage('add-shop')" id="addShopBtn" style="display: none;">
                    <i class="fas fa-plus me-2"></i>
                    إضافة محل جديد
                </button>
            </div>

            <div class="row" id="shopsContainer">
                <!-- Shops will be loaded here -->
            </div>

            <div class="alert alert-info">
                <h5><i class="fas fa-store me-2"></i>المحلات المعتمدة</h5>
                <p class="mb-0">جميع المحلات مراجعة ومعتمدة لضمان جودة المنتجات والخدمة</p>
            </div>
        </div>
    </div>

    <!-- Add Shop Page -->
    <div id="add-shop" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-store me-2"></i>
                                إضافة محل جديد
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="addShopForm" class="product-form">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                                    <p class="mb-0">سيتم إنشاء حساب جديد للمحل مع البيانات المدخلة. تأكد من صحة جميع المعلومات.</p>
                                </div>

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    بيانات تسجيل الدخول
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="shopUsername" placeholder="اسم المستخدم" required>
                                            <label for="shopUsername">اسم المستخدم *</label>
                                            <div class="form-text">سيستخدم لتسجيل الدخول</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="shopPassword" placeholder="كلمة المرور" required>
                                            <label for="shopPassword">كلمة المرور *</label>
                                            <div class="form-text">يجب أن تكون قوية وآمنة</div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات المحل
                                </h5>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="shopName" placeholder="اسم المحل" required>
                                    <label for="shopName">اسم المحل *</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="shopDescription" placeholder="وصف المحل" style="height: 100px" required></textarea>
                                    <label for="shopDescription">وصف المحل *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="shopPhone" placeholder="رقم الهاتف" required>
                                            <label for="shopPhone">رقم الهاتف *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="shopEmail" placeholder="البريد الإلكتروني">
                                            <label for="shopEmail">البريد الإلكتروني</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="shopAddress" placeholder="العنوان" required>
                                    <label for="shopAddress">العنوان *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="shopCity" required>
                                                <option value="">اختر المدينة</option>
                                                <option value="الرياض">الرياض</option>
                                                <option value="جدة">جدة</option>
                                                <option value="الدمام">الدمام</option>
                                                <option value="مكة المكرمة">مكة المكرمة</option>
                                                <option value="المدينة المنورة">المدينة المنورة</option>
                                                <option value="الطائف">الطائف</option>
                                                <option value="تبوك">تبوك</option>
                                                <option value="بريدة">بريدة</option>
                                                <option value="خميس مشيط">خميس مشيط</option>
                                                <option value="حائل">حائل</option>
                                            </select>
                                            <label for="shopCity">المدينة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="shopSpecialty">
                                                <option value="">اختر التخصص</option>
                                                <option value="قطع غيار يابانية">قطع غيار يابانية</option>
                                                <option value="قطع غيار كورية">قطع غيار كورية</option>
                                                <option value="قطع غيار أوروبية">قطع غيار أوروبية</option>
                                                <option value="قطع غيار أمريكية">قطع غيار أمريكية</option>
                                                <option value="قطع غيار صينية">قطع غيار صينية</option>
                                                <option value="جميع الأنواع">جميع الأنواع</option>
                                            </select>
                                            <label for="shopSpecialty">تخصص المحل</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="shopOpenTime">
                                            <label for="shopOpenTime">وقت الفتح</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="shopCloseTime">
                                            <label for="shopCloseTime">وقت الإغلاق</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="shopWebsite" placeholder="موقع المحل الإلكتروني">
                                    <label for="shopWebsite">موقع المحل الإلكتروني</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="shopNotes" placeholder="ملاحظات إضافية" style="height: 80px"></textarea>
                                    <label for="shopNotes">ملاحظات إضافية</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shops')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        إضافة المحل
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Page -->
    <div id="add-product" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="addProductForm" class="product-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productName" placeholder="اسم المنتج" required>
                                            <label for="productName">اسم المنتج *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="partNumber" placeholder="رقم القطعة">
                                            <label for="partNumber">رقم القطعة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="productDescription" placeholder="وصف المنتج" style="height: 100px"></textarea>
                                    <label for="productDescription">وصف المنتج</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productPrice" placeholder="السعر" step="0.01" min="0" required>
                                            <label for="productPrice">السعر (ريال) *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productQuantity" placeholder="الكمية" min="0" required>
                                            <label for="productQuantity">الكمية المتوفرة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <select class="form-select" id="productCategory">
                                                <option value="">اختر الفئة</option>
                                                <option value="فرامل">فرامل</option>
                                                <option value="محرك">محرك</option>
                                                <option value="تكييف">تكييف</option>
                                                <option value="كهرباء">كهرباء</option>
                                                <option value="عجلات">عجلات</option>
                                                <option value="زيوت">زيوت</option>
                                                <option value="فلاتر">فلاتر</option>
                                            </select>
                                            <label for="productCategory">الفئة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="productBrand">
                                                <option value="">اختر الماركة</option>
                                                <option value="تويوتا">تويوتا</option>
                                                <option value="هوندا">هوندا</option>
                                                <option value="نيسان">نيسان</option>
                                                <option value="هيونداي">هيونداي</option>
                                                <option value="كيا">كيا</option>
                                                <option value="مازدا">مازدا</option>
                                                <option value="ميتسوبيشي">ميتسوبيشي</option>
                                            </select>
                                            <label for="productBrand">ماركة السيارة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productModel" placeholder="موديل السيارة">
                                            <label for="productModel">موديل السيارة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="productImage" placeholder="رابط الصورة">
                                    <label for="productImage">رابط صورة المنتج</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shop')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal fade" id="productDetailModal" tabindex="-1">
        <div class="modal-dialog product-detail-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailTitle">تفاصيل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="productDetailImage" class="product-image mb-3">
                                <i class="fas fa-cog fa-3x text-muted"></i>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4 id="productDetailName"></h4>
                            <p id="productDetailDescription" class="text-muted"></p>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>السعر:</strong>
                                    <span id="productDetailPrice" class="text-primary fs-5"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الكمية:</strong>
                                    <span id="productDetailQuantity"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>رقم القطعة:</strong>
                                    <span id="productDetailPartNumber"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الفئة:</strong>
                                    <span id="productDetailCategory"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>الماركة:</strong>
                                    <span id="productDetailBrand"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الموديل:</strong>
                                    <span id="productDetailModel"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6><i class="fas fa-store me-2"></i>المحلات التي يوجد بها هذا المنتج:</h6>
                        <div id="productShopsList">
                            <!-- Shops list will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="contactShop()">
                        <i class="fas fa-phone me-1"></i>
                        اتصل بالمحل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample products data with shop-specific quantities and prices
        let products = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020، يضمن تنقية الهواء الداخل للمحرك',
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 85,
                        quantity: 15
                    },
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 90,
                        quantity: 8
                    }
                ]
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد، يوفر أداء فرملة ممتاز وآمن',
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 120,
                        quantity: 8
                    }
                ]
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات، يوفر حماية فائقة للمحرك',
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 95,
                        quantity: 25
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 92,
                        quantity: 18
                    }
                ]
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير، ضمان سنتين',
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 280,
                        quantity: 5
                    }
                ]
            },
            {
                id: 5,
                name: 'إطار ميشلان 205/55R16',
                description: 'إطار ميشلان عالي الجودة، مقاس 205/55R16، مناسب للسيارات المتوسطة',
                partNumber: 'MICH-TIRE-006',
                category: 'عجلات',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 320,
                        quantity: 12
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 315,
                        quantity: 8
                    }
                ]
            },
            {
                id: 6,
                name: 'مكيف هواء هيونداي إلنترا',
                description: 'كمبروسر مكيف هواء لسيارة هيونداي إلنترا، يوفر تبريد فعال',
                partNumber: 'HYU-AC-005',
                category: 'تكييف',
                brand: 'هيونداي',
                model: 'إلنترا',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 450,
                        quantity: 3
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 440,
                        quantity: 2
                    }
                ]
            },
            {
                id: 7,
                name: 'شمعات إشعال NGK',
                description: 'شمعات إشعال عالية الجودة من NGK، مناسبة لمعظم السيارات',
                partNumber: 'NGK-SP-007',
                category: 'محرك',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 45,
                        quantity: 30
                    },
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 48,
                        quantity: 25
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 42,
                        quantity: 35
                    }
                ]
            },
            {
                id: 8,
                name: 'مساحات زجاج بوش',
                description: 'مساحات زجاج أمامي من بوش، مقاس 24 بوصة، مقاومة للعوامل الجوية',
                partNumber: 'BOSCH-WB-008',
                category: 'كهرباء',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 75,
                        quantity: 20
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 72,
                        quantity: 15
                    }
                ]
            }
        ];

        // Sample shops data
        let shops = [
            {
                id: 1,
                name: 'محل الأصيل لقطع غيار السيارات',
                description: 'محل متخصص في قطع غيار السيارات اليابانية والكورية مع خبرة 15 سنة',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'شارع الملك فهد، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار يابانية',
                openTime: '08:00',
                closeTime: '22:00',
                website: '',
                notes: 'خدمة توصيل مجاني داخل الرياض',
                rating: 4.5,
                productsCount: 15,
                joinDate: '2020-01-15'
            },
            {
                id: 2,
                name: 'محل النجمة للسيارات',
                description: 'محل شامل لجميع أنواع قطع غيار السيارات الأوروبية والأمريكية',
                phone: '+966507654321',
                email: '<EMAIL>',
                address: 'شارع العليا، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار أوروبية',
                openTime: '09:00',
                closeTime: '23:00',
                website: '',
                notes: 'ضمان سنة على جميع القطع',
                rating: 4.2,
                productsCount: 8,
                joinDate: '2021-03-20'
            },
            {
                id: 3,
                name: 'محل السرعة للسيارات',
                description: 'متخصص في قطع غيار السيارات الرياضية والفاخرة',
                phone: '+966509876543',
                email: '<EMAIL>',
                address: 'شارع التحلية، جدة',
                city: 'جدة',
                specialty: 'جميع الأنواع',
                openTime: '10:00',
                closeTime: '24:00',
                website: 'www.alsuraa.com',
                notes: 'متخصص في السيارات الفاخرة والرياضية',
                rating: 4.7,
                productsCount: 12,
                joinDate: '2019-11-10'
            }
        ];

        // Users data
        let users = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل لقطع غيار السيارات', shopId: 1 }
        };

        let nextShopId = 4;
        let nextUserId = 3;

        // Current user
        let currentUser = null;
        let nextProductId = 9;

        // Show page function
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Load content based on page
            if (pageId === 'home') {
                loadProducts();
            } else if (pageId === 'shops') {
                loadShops();
            } else if (pageId === 'shop') {
                loadShopProducts();
            }
        }

        // Show dashboard based on user role
        function showDashboard() {
            if (currentUser) {
                if (currentUser.role === 'admin') {
                    showPage('admin');
                } else {
                    showPage('shop');
                }
            }
        }

        // Load products table
        function loadProducts() {
            const tableBody = document.getElementById('productsTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            let totalQuantity = 0;
            let allPrices = [];

            products.forEach(product => {
                // Calculate totals from shop details
                const productTotalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                const minPrice = Math.min(...product.shopDetails.map(shop => shop.price));
                const maxPrice = Math.max(...product.shopDetails.map(shop => shop.price));

                totalQuantity += productTotalQuantity;
                product.shopDetails.forEach(shop => allPrices.push(shop.price));

                // Create shops list with prices and quantities
                const shopsList = product.shopDetails.map(shopDetail => {
                    const shop = shops.find(s => s.name === shopDetail.shopName);
                    const phone = shop ? shop.phone : '';
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                            <button class="btn btn-outline-primary btn-sm ms-2" onclick="contactShop('${shopDetail.shopName}', '${phone}')" title="اتصل بالمحل">
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                    `;
                }).join('');

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    ${product.image ?
                                        `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">` :
                                        '<div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;"><i class="fas fa-cog text-muted"></i></div>'
                                    }
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل |
                                إجمالي الكمية: <span class="fw-bold">${productTotalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                                ${minPrice !== maxPrice ? `<br><small class="text-muted">أعلى: ${maxPrice} ريال</small>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="contactBestPriceShop(${product.id})" title="اتصل بأفضل سعر">
                                    <i class="fas fa-phone"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });

            // Update summary cards
            updateSummaryCards(totalQuantity, allPrices);
        }

        // Update summary cards
        function updateSummaryCards(totalQuantity, allPrices) {
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalShops').textContent = shops.length;
            document.getElementById('totalQuantity').textContent = totalQuantity;

            if (allPrices.length > 0) {
                const averagePrice = allPrices.reduce((sum, price) => sum + price, 0) / allPrices.length;
                document.getElementById('averagePrice').textContent = Math.round(averagePrice) + ' ريال';
            }
        }

        // Contact shop with best price
        function contactBestPriceShop(productId) {
            const product = products.find(p => p.id === productId);
            if (!product || !product.shopDetails.length) return;

            // Find shop with minimum price
            const bestPriceShop = product.shopDetails.reduce((min, shop) =>
                shop.price < min.price ? shop : min
            );

            const shop = shops.find(s => s.name === bestPriceShop.shopName);
            const phone = shop ? shop.phone : '';

            contactShop(bestPriceShop.shopName, phone);
        }

        // Load shops
        function loadShops() {
            const container = document.getElementById('shopsContainer');
            container.innerHTML = '';

            // Show add shop button for admin
            const addShopBtn = document.getElementById('addShopBtn');
            if (currentUser && currentUser.role === 'admin') {
                addShopBtn.style.display = 'inline-block';
            } else {
                addShopBtn.style.display = 'none';
            }

            shops.forEach(shop => {
                const shopCard = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <i class="fas fa-store fa-3x text-primary mb-2"></i>
                                    <h5 class="card-title">${shop.name}</h5>
                                </div>

                                <p class="text-muted small">${shop.description}</p>

                                <div class="mb-2">
                                    <div class="rating mb-1">
                                        ${generateStars(shop.rating)}
                                        <span class="ms-2 fw-bold">(${shop.rating})</span>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-phone me-1 text-primary"></i>${shop.phone}
                                    </small>
                                    ${shop.email ? `<small class="text-muted d-block"><i class="fas fa-envelope me-1 text-primary"></i>${shop.email}</small>` : ''}
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>${shop.address}
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-city me-1 text-primary"></i>${shop.city}
                                    </small>
                                </div>

                                ${shop.specialty ? `
                                <div class="mb-2">
                                    <span class="badge bg-secondary">${shop.specialty}</span>
                                </div>
                                ` : ''}

                                ${shop.openTime && shop.closeTime ? `
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1 text-primary"></i>
                                        ${shop.openTime} - ${shop.closeTime}
                                    </small>
                                </div>
                                ` : ''}

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-info">${shop.productsCount} منتج</span>
                                    <small class="text-muted">انضم: ${new Date(shop.joinDate).toLocaleDateString('ar-SA')}</small>
                                </div>

                                ${shop.notes ? `
                                <div class="alert alert-light p-2 mb-2">
                                    <small><i class="fas fa-info-circle me-1"></i>${shop.notes}</small>
                                </div>
                                ` : ''}
                            </div>

                            <div class="card-footer bg-transparent">
                                <div class="row g-1">
                                    <div class="col-8">
                                        <button class="btn btn-primary btn-sm w-100" onclick="contactShop('${shop.name}', '${shop.phone}')">
                                            <i class="fas fa-phone me-1"></i>
                                            اتصل
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        ${shop.website ? `
                                        <button class="btn btn-outline-primary btn-sm w-100" onclick="window.open('${shop.website.startsWith('http') ? shop.website : 'http://' + shop.website}', '_blank')" title="زيارة الموقع">
                                            <i class="fas fa-globe"></i>
                                        </button>
                                        ` : `
                                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="showShopDetails(${shop.id})" title="تفاصيل أكثر">
                                            <i class="fas fa-info"></i>
                                        </button>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += shopCard;
            });
        }

        // Load shop products (for shop dashboard)
        function loadShopProducts() {
            if (!currentUser || currentUser.role !== 'shop') return;

            const container = document.getElementById('shopProductsContainer');
            if (!container) return;

            container.innerHTML = '';

            // Filter products for current shop
            const shopProducts = products.filter(product =>
                product.shopDetails.some(shop => shop.shopName === currentUser.name)
            );

            shopProducts.forEach(product => {
                // Find this shop's details for the product
                const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
                if (!shopDetail) return;

                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description.substring(0, 40)}...</p>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold text-primary">${shopDetail.price} ريال</span>
                                    <span class="badge bg-success">الكمية: ${shopDetail.quantity}</span>
                                </div>
                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm ms-1" onclick="editProduct(${product.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Generate stars for rating
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star text-warning"></i>';
                } else if (i - 0.5 <= rating) {
                    stars += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    stars += '<i class="far fa-star text-warning"></i>';
                }
            }
            return stars;
        }

        // Show product detail
        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            // Calculate price range and total quantity
            const prices = product.shopDetails.map(shop => shop.price);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);

            // Fill modal with product data
            document.getElementById('productDetailTitle').textContent = product.name;
            document.getElementById('productDetailName').textContent = product.name;
            document.getElementById('productDetailDescription').textContent = product.description;

            // Show price range
            const priceText = minPrice === maxPrice ?
                `${minPrice} ريال` :
                `${minPrice} - ${maxPrice} ريال`;
            document.getElementById('productDetailPrice').textContent = priceText;

            document.getElementById('productDetailQuantity').textContent = totalQuantity;
            document.getElementById('productDetailPartNumber').textContent = product.partNumber || '-';
            document.getElementById('productDetailCategory').textContent = product.category || '-';
            document.getElementById('productDetailBrand').textContent = product.brand || '-';
            document.getElementById('productDetailModel').textContent = product.model || '-';

            // Handle image
            const imageContainer = document.getElementById('productDetailImage');
            if (product.image) {
                imageContainer.innerHTML = `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">`;
            } else {
                imageContainer.innerHTML = '<i class="fas fa-cog fa-3x text-muted"></i>';
            }

            // Load shops list with individual prices and quantities
            const shopsList = document.getElementById('productShopsList');
            shopsList.innerHTML = '';
            product.shopDetails.forEach(shopDetail => {
                const shop = shops.find(s => s.name === shopDetail.shopName);
                if (shop) {
                    shopsList.innerHTML += `
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded ${shopDetail.price === minPrice ? 'border-success bg-light' : ''}">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <strong class="text-primary">${shop.name}</strong>
                                    ${shopDetail.price === minPrice ? '<span class="badge bg-success">أفضل سعر</span>' : ''}
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted d-block">السعر:</small>
                                        <span class="fw-bold text-success">${shopDetail.price} ريال</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">الكمية:</small>
                                        <span class="badge bg-info">${shopDetail.quantity}</span>
                                    </div>
                                </div>
                                <small class="text-muted d-block mt-1">
                                    <i class="fas fa-phone me-1"></i>${shop.phone}
                                    <i class="fas fa-map-marker-alt me-1 ms-2"></i>${shop.address}
                                </small>
                                <div class="rating mt-1">
                                    ${generateStars(shop.rating)}
                                    <small class="text-muted ms-1">(${shop.rating})</small>
                                </div>
                            </div>
                            <div class="ms-3">
                                <button class="btn btn-primary" onclick="contactShop('${shop.name}', '${shop.phone}')">
                                    <i class="fas fa-phone me-1"></i>اتصل
                                </button>
                            </div>
                        </div>
                    `;
                }
            });

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('productDetailModal'));
            modal.show();
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('loginAlert');

            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };

                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('dashboardLink').style.display = 'inline';
                document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;

                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';

                // Redirect to dashboard
                setTimeout(() => {
                    if (currentUser.role === 'admin') {
                        showPage('admin');
                    } else {
                        showPage('shop');
                    }
                }, 1000);

            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Add new product
        function addProduct(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'shop') {
                alert('غير مصرح لك بإضافة منتجات');
                return;
            }

            const price = parseFloat(document.getElementById('productPrice').value);
            const quantity = parseInt(document.getElementById('productQuantity').value);

            // Check if product already exists
            const existingProduct = products.find(p =>
                p.name.toLowerCase() === document.getElementById('productName').value.toLowerCase() &&
                p.partNumber === document.getElementById('partNumber').value
            );

            if (existingProduct) {
                // Add this shop to existing product
                const existingShopDetail = existingProduct.shopDetails.find(shop =>
                    shop.shopName === currentUser.name
                );

                if (existingShopDetail) {
                    // Update existing shop details
                    existingShopDetail.price = price;
                    existingShopDetail.quantity = quantity;
                    alert('تم تحديث بيانات المنتج في محلك!');
                } else {
                    // Add new shop to existing product
                    existingProduct.shopDetails.push({
                        shopName: currentUser.name,
                        price: price,
                        quantity: quantity
                    });
                    alert('تم إضافة المنتج إلى محلك!');
                }
            } else {
                // Create new product
                const newProduct = {
                    id: nextProductId++,
                    name: document.getElementById('productName').value,
                    description: document.getElementById('productDescription').value,
                    partNumber: document.getElementById('partNumber').value,
                    category: document.getElementById('productCategory').value,
                    brand: document.getElementById('productBrand').value,
                    model: document.getElementById('productModel').value,
                    image: document.getElementById('productImage').value,
                    shopDetails: [{
                        shopName: currentUser.name,
                        price: price,
                        quantity: quantity
                    }]
                };

                products.push(newProduct);
                alert('تم إضافة المنتج الجديد بنجاح!');
            }

            // Reset form
            document.getElementById('addProductForm').reset();

            // Redirect to shop dashboard
            showPage('shop');
        }

        // Add new shop
        function addShop(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بإضافة محلات');
                return;
            }

            const username = document.getElementById('shopUsername').value;
            const password = document.getElementById('shopPassword').value;
            const shopName = document.getElementById('shopName').value;

            // Check if username already exists
            if (users[username]) {
                alert('اسم المستخدم موجود بالفعل، اختر اسم مستخدم آخر');
                return;
            }

            // Check if shop name already exists
            if (shops.find(shop => shop.name.toLowerCase() === shopName.toLowerCase())) {
                alert('اسم المحل موجود بالفعل، اختر اسم محل آخر');
                return;
            }

            // Create new shop
            const newShop = {
                id: nextShopId++,
                name: shopName,
                description: document.getElementById('shopDescription').value,
                phone: document.getElementById('shopPhone').value,
                email: document.getElementById('shopEmail').value,
                address: document.getElementById('shopAddress').value,
                city: document.getElementById('shopCity').value,
                specialty: document.getElementById('shopSpecialty').value,
                openTime: document.getElementById('shopOpenTime').value,
                closeTime: document.getElementById('shopCloseTime').value,
                website: document.getElementById('shopWebsite').value,
                notes: document.getElementById('shopNotes').value,
                rating: 0,
                productsCount: 0,
                joinDate: new Date().toISOString().split('T')[0]
            };

            // Create new user account
            users[username] = {
                password: password,
                role: 'shop',
                name: shopName,
                shopId: newShop.id
            };

            // Add shop to shops array
            shops.push(newShop);

            // Reset form
            document.getElementById('addShopForm').reset();

            // Show success message
            alert(`تم إضافة المحل "${shopName}" بنجاح!\n\nبيانات تسجيل الدخول:\nاسم المستخدم: ${username}\nكلمة المرور: ${password}\n\nيمكن لصاحب المحل الآن تسجيل الدخول وإضافة منتجاته.`);

            // Redirect to shops page
            showPage('shops');
        }

        // Show shop details (placeholder)
        function showShopDetails(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) return;

            let details = `تفاصيل ${shop.name}\n\n`;
            details += `الوصف: ${shop.description}\n`;
            details += `الهاتف: ${shop.phone}\n`;
            if (shop.email) details += `البريد: ${shop.email}\n`;
            details += `العنوان: ${shop.address}\n`;
            details += `المدينة: ${shop.city}\n`;
            if (shop.specialty) details += `التخصص: ${shop.specialty}\n`;
            if (shop.openTime && shop.closeTime) details += `أوقات العمل: ${shop.openTime} - ${shop.closeTime}\n`;
            details += `التقييم: ${shop.rating}/5\n`;
            details += `عدد المنتجات: ${shop.productsCount}\n`;
            details += `تاريخ الانضمام: ${new Date(shop.joinDate).toLocaleDateString('ar-SA')}\n`;
            if (shop.notes) details += `ملاحظات: ${shop.notes}\n`;

            alert(details);
        }

        // Edit product (placeholder)
        function editProduct(productId) {
            alert('ميزة تعديل المنتج ستكون متوفرة قريباً');
        }

        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';
            showPage('home');
        }

        // Contact shop
        function contactShop(shopName, phone) {
            if (phone) {
                if (confirm(`هل تريد الاتصال بـ ${shopName} على الرقم ${phone}؟`)) {
                    window.open(`tel:${phone}`, '_self');
                }
            } else {
                alert(`للتواصل مع ${shopName || 'المحل'}`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();

            // Add event listener for add product form
            const addProductForm = document.getElementById('addProductForm');
            if (addProductForm) {
                addProductForm.addEventListener('submit', addProduct);
            }

            // Add event listener for add shop form
            const addShopForm = document.getElementById('addShopForm');
            if (addShopForm) {
                addShopForm.addEventListener('submit', addShop);
            }
        });
    </script>
</body>
</html>
