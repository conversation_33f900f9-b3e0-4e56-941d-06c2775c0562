<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
        
        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c3e50 !important;
        }
        
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
        }

        .product-detail-modal .modal-dialog {
            max-width: 800px;
        }

        .shop-badge {
            display: inline-block;
            margin: 2px;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .product-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            vertical-align: middle;
            font-size: 0.9rem;
        }

        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
        }

        .badge-sm {
            font-size: 0.7rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .shops-list {
            max-height: 100px;
            overflow-y: auto;
        }

        .shop-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 4px 8px;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('search')">البحث</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>

                <!-- Auto-refresh toggle button -->
                <button class="btn btn-warning btn-sm me-2" id="autoRefreshToggle" onclick="toggleAutoRefresh()" style="display: none;" title="التحكم في التحديث التلقائي">
                    <i class="fas fa-pause me-1"></i>إيقاف التحديث
                </button>

                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showDashboard()" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">
                            أفضل قطع غيار السيارات
                        </h1>
                        <p class="lead mb-4">
                            اكتشف أفضل قطع غيار السيارات من محلات موثوقة ومعتمدة
                        </p>
                        <button class="btn btn-light btn-lg" onclick="showPage('search')">
                            <i class="fas fa-search me-2"></i>
                            ابحث الآن
                        </button>
                    </div>
                    <div class="col-lg-6 text-center">
                        <i class="fas fa-car fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Table Section -->
        <section class="py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 class="fw-bold">قطع الغيار المتوفرة</h2>
                        <p class="text-muted">جميع قطع الغيار مع المحلات والأسعار</p>

                        <!-- Auto-refresh status indicator -->
                        <div id="refreshStatus" class="d-inline-block" style="display: none !important;">
                            <span class="badge bg-success">
                                <i class="fas fa-sync-alt fa-spin me-1"></i>
                                التحديث التلقائي نشط
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            جدول قطع الغيار والمحلات
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="productsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 25%;">اسم القطعة</th>
                                        <th style="width: 15%;">رقم القطعة</th>
                                        <th style="width: 35%;">المحلات والأسعار والكميات</th>
                                        <th style="width: 15%;">أقل سعر</th>
                                        <th style="width: 10%;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- Products table rows will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <h4 id="totalProducts">0</h4>
                                <p class="mb-0">إجمالي القطع</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <h4 id="totalShops">0</h4>
                                <p class="mb-0">إجمالي المحلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <h4 id="totalQuantity">0</h4>
                                <p class="mb-0">إجمالي الكميات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <h4 id="averagePrice">0</h4>
                                <p class="mb-0">متوسط الأسعار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h3 class="fw-bold">تسجيل الدخول</h3>
                            </div>
                            
                            <div id="loginAlert" class="alert" style="display: none;"></div>
                            
                            <form id="loginForm" onsubmit="handleLogin(event)">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">حسابات تجريبية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>المدير:</strong><br>
                                            <small>admin / admin123</small>
                                        </div>
                                        <div class="col-6">
                                            <strong>المحل:</strong><br>
                                            <small>shop1 / shop123</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة تحكم المدير
                            </h2>
                            <p class="mb-0">مرحباً بك في لوحة التحكم الرئيسية</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">5</h4>
                        <p class="mb-0">إجمالي المحلات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">24</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">8</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">التقييمات</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المدير تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة النظام بالكامل - إضافة محلات، إدارة مستخدمين، مراقبة الإحصائيات</p>
            </div>
        </div>
    </div>

    <!-- Shop Dashboard -->
    <div id="shop" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-store me-2"></i>
                                لوحة تحكم المحل
                            </h2>
                            <p class="mb-0">محل الأصيل لقطع غيار السيارات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">15</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">المنتجات النشطة</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">4.5</h4>
                        <p class="mb-0">تقييم المحل</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أحدث المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>رقم القطعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>فلتر هواء تويوتا كامري</td>
                                    <td>85 ريال</td>
                                    <td>15</td>
                                    <td>TOY-AF-001</td>
                                </tr>
                                <tr>
                                    <td>تيل فرامل هوندا أكورد</td>
                                    <td>120 ريال</td>
                                    <td>8</td>
                                    <td>HON-BP-002</td>
                                </tr>
                                <tr>
                                    <td>زيت محرك موبيل 1</td>
                                    <td>95 ريال</td>
                                    <td>25</td>
                                    <td>MOB-OIL-003</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">إدارة المنتجات</h5>
                            <div>
                                <button class="btn btn-outline-info btn-sm me-2" onclick="debugReloadData()" title="تشخيص البيانات">
                                    <i class="fas fa-bug me-1"></i>
                                    تشخيص
                                </button>
                                <button class="btn btn-success" onclick="showPage('add-product')">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة منتج جديد
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" id="shopProductsContainer">
                                <!-- Shop products will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-success mt-4">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المحل تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة منتجاتك - إضافة، تعديل، حذف المنتجات وإدارة الأسعار والكميات</p>
            </div>
        </div>
    </div>

    <!-- Search Page -->
    <div id="search" class="page-content">
        <div class="container py-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث المتقدم</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="اسم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select">
                                <option>جميع الماركات</option>
                                <option>تويوتا</option>
                                <option>هوندا</option>
                                <option>نيسان</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="رقم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100">بحث</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-search me-2"></i>نظام البحث المتقدم</h5>
                <p class="mb-0">يمكنك البحث عن قطع الغيار حسب الاسم، الماركة، أو رقم القطعة</p>
            </div>
        </div>
    </div>

    <!-- Shops Page -->
    <div id="shops" class="page-content">
        <div class="container py-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">جميع المحلات</h2>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="loadShops()" title="تحديث القائمة">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success" onclick="showPage('add-shop')" id="addShopBtn" style="display: none;">
                        <i class="fas fa-plus me-2"></i>
                        إضافة محل جديد
                    </button>
                </div>
            </div>

            <div class="row" id="shopsContainer">
                <!-- Shops will be loaded here -->
            </div>

            <div class="alert alert-info">
                <h5><i class="fas fa-store me-2"></i>المحلات المعتمدة</h5>
                <p class="mb-0">جميع المحلات مراجعة ومعتمدة لضمان جودة المنتجات والخدمة</p>
            </div>
        </div>
    </div>

    <!-- Add Shop Page -->
    <div id="add-shop" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-store me-2"></i>
                                إضافة محل جديد
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="addShopForm" class="product-form">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                                    <p class="mb-0">سيتم إنشاء حساب جديد للمحل مع البيانات المدخلة. تأكد من صحة جميع المعلومات.</p>
                                </div>

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    بيانات تسجيل الدخول
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="shopUsername" placeholder="اسم المستخدم" required>
                                            <label for="shopUsername">اسم المستخدم *</label>
                                            <div class="form-text">سيستخدم لتسجيل الدخول</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="shopPassword" placeholder="كلمة المرور" required>
                                            <label for="shopPassword">كلمة المرور *</label>
                                            <div class="form-text">يجب أن تكون قوية وآمنة</div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات المحل
                                </h5>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="shopName" placeholder="اسم المحل" required>
                                    <label for="shopName">اسم المحل *</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="shopDescription" placeholder="وصف المحل" style="height: 100px" required></textarea>
                                    <label for="shopDescription">وصف المحل *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="shopPhone" placeholder="رقم الهاتف" required>
                                            <label for="shopPhone">رقم الهاتف *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="shopEmail" placeholder="البريد الإلكتروني">
                                            <label for="shopEmail">البريد الإلكتروني</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="shopAddress" placeholder="العنوان" required>
                                    <label for="shopAddress">العنوان *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="shopCity" required>
                                                <option value="">اختر المدينة</option>
                                                <option value="الرياض">الرياض</option>
                                                <option value="جدة">جدة</option>
                                                <option value="الدمام">الدمام</option>
                                                <option value="مكة المكرمة">مكة المكرمة</option>
                                                <option value="المدينة المنورة">المدينة المنورة</option>
                                                <option value="الطائف">الطائف</option>
                                                <option value="تبوك">تبوك</option>
                                                <option value="بريدة">بريدة</option>
                                                <option value="خميس مشيط">خميس مشيط</option>
                                                <option value="حائل">حائل</option>
                                            </select>
                                            <label for="shopCity">المدينة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="shopSpecialty">
                                                <option value="">اختر التخصص</option>
                                                <option value="قطع غيار يابانية">قطع غيار يابانية</option>
                                                <option value="قطع غيار كورية">قطع غيار كورية</option>
                                                <option value="قطع غيار أوروبية">قطع غيار أوروبية</option>
                                                <option value="قطع غيار أمريكية">قطع غيار أمريكية</option>
                                                <option value="قطع غيار صينية">قطع غيار صينية</option>
                                                <option value="جميع الأنواع">جميع الأنواع</option>
                                            </select>
                                            <label for="shopSpecialty">تخصص المحل</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="shopOpenTime">
                                            <label for="shopOpenTime">وقت الفتح</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="shopCloseTime">
                                            <label for="shopCloseTime">وقت الإغلاق</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="shopWebsite" placeholder="موقع المحل الإلكتروني">
                                    <label for="shopWebsite">موقع المحل الإلكتروني</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="shopNotes" placeholder="ملاحظات إضافية" style="height: 80px"></textarea>
                                    <label for="shopNotes">ملاحظات إضافية</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shops')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-success" id="addShopSubmitBtn">
                                        <i class="fas fa-save me-2"></i>
                                        إضافة المحل
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Page -->
    <div id="add-product" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="addProductForm" class="product-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productName" placeholder="اسم المنتج" required>
                                            <label for="productName">اسم المنتج *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="partNumber" placeholder="رقم القطعة">
                                            <label for="partNumber">رقم القطعة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="productDescription" placeholder="وصف المنتج" style="height: 100px"></textarea>
                                    <label for="productDescription">وصف المنتج</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productPrice" placeholder="السعر" step="0.01" min="0" required>
                                            <label for="productPrice">السعر (ريال) *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productQuantity" placeholder="الكمية" min="0" required>
                                            <label for="productQuantity">الكمية المتوفرة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <select class="form-select" id="productCategory">
                                                <option value="">اختر الفئة</option>
                                                <option value="فرامل">فرامل</option>
                                                <option value="محرك">محرك</option>
                                                <option value="تكييف">تكييف</option>
                                                <option value="كهرباء">كهرباء</option>
                                                <option value="عجلات">عجلات</option>
                                                <option value="زيوت">زيوت</option>
                                                <option value="فلاتر">فلاتر</option>
                                            </select>
                                            <label for="productCategory">الفئة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="productBrand">
                                                <option value="">اختر الماركة</option>
                                                <option value="تويوتا">تويوتا</option>
                                                <option value="هوندا">هوندا</option>
                                                <option value="نيسان">نيسان</option>
                                                <option value="هيونداي">هيونداي</option>
                                                <option value="كيا">كيا</option>
                                                <option value="مازدا">مازدا</option>
                                                <option value="ميتسوبيشي">ميتسوبيشي</option>
                                            </select>
                                            <label for="productBrand">ماركة السيارة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productModel" placeholder="موديل السيارة">
                                            <label for="productModel">موديل السيارة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="productImage" placeholder="رابط الصورة">
                                    <label for="productImage">رابط صورة المنتج</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shop')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Shop Page -->
    <div id="edit-shop" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h4 class="mb-0">
                                <i class="fas fa-edit me-2"></i>
                                تعديل بيانات المحل
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="editShopForm" class="product-form">
                                <input type="hidden" id="editShopId">
                                <input type="hidden" id="editShopUsername">

                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                                    <p class="mb-0">تعديل بيانات المحل سيؤثر على جميع المنتجات المرتبطة به.</p>
                                </div>

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    بيانات تسجيل الدخول
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="editShopUsernameDisplay" disabled>
                                            <label for="editShopUsernameDisplay">اسم المستخدم (لا يمكن تغييره)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="editShopPassword" placeholder="كلمة المرور الجديدة">
                                            <label for="editShopPassword">كلمة المرور الجديدة</label>
                                            <div class="form-text">اتركها فارغة إذا كنت لا تريد تغييرها</div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات المحل
                                </h5>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="editShopName" placeholder="اسم المحل" required>
                                    <label for="editShopName">اسم المحل *</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="editShopDescription" placeholder="وصف المحل" style="height: 100px" required></textarea>
                                    <label for="editShopDescription">وصف المحل *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="editShopPhone" placeholder="رقم الهاتف" required>
                                            <label for="editShopPhone">رقم الهاتف *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="editShopEmail" placeholder="البريد الإلكتروني">
                                            <label for="editShopEmail">البريد الإلكتروني</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="editShopAddress" placeholder="العنوان" required>
                                    <label for="editShopAddress">العنوان *</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="editShopCity" required>
                                                <option value="">اختر المدينة</option>
                                                <option value="الرياض">الرياض</option>
                                                <option value="جدة">جدة</option>
                                                <option value="الدمام">الدمام</option>
                                                <option value="مكة المكرمة">مكة المكرمة</option>
                                                <option value="المدينة المنورة">المدينة المنورة</option>
                                                <option value="الطائف">الطائف</option>
                                                <option value="تبوك">تبوك</option>
                                                <option value="بريدة">بريدة</option>
                                                <option value="خميس مشيط">خميس مشيط</option>
                                                <option value="حائل">حائل</option>
                                            </select>
                                            <label for="editShopCity">المدينة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="editShopSpecialty">
                                                <option value="">اختر التخصص</option>
                                                <option value="قطع غيار يابانية">قطع غيار يابانية</option>
                                                <option value="قطع غيار كورية">قطع غيار كورية</option>
                                                <option value="قطع غيار أوروبية">قطع غيار أوروبية</option>
                                                <option value="قطع غيار أمريكية">قطع غيار أمريكية</option>
                                                <option value="قطع غيار صينية">قطع غيار صينية</option>
                                                <option value="جميع الأنواع">جميع الأنواع</option>
                                            </select>
                                            <label for="editShopSpecialty">تخصص المحل</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="editShopOpenTime">
                                            <label for="editShopOpenTime">وقت الفتح</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="time" class="form-control" id="editShopCloseTime">
                                            <label for="editShopCloseTime">وقت الإغلاق</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="editShopWebsite" placeholder="موقع المحل الإلكتروني">
                                    <label for="editShopWebsite">موقع المحل الإلكتروني</label>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="editShopNotes" placeholder="ملاحظات إضافية" style="height: 80px"></textarea>
                                    <label for="editShopNotes">ملاحظات إضافية</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shops')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التعديلات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal fade" id="productDetailModal" tabindex="-1">
        <div class="modal-dialog product-detail-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailTitle">تفاصيل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="productDetailImage" class="product-image mb-3">
                                <i class="fas fa-cog fa-3x text-muted"></i>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4 id="productDetailName"></h4>
                            <p id="productDetailDescription" class="text-muted"></p>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>السعر:</strong>
                                    <span id="productDetailPrice" class="text-primary fs-5"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الكمية:</strong>
                                    <span id="productDetailQuantity"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>رقم القطعة:</strong>
                                    <span id="productDetailPartNumber"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الفئة:</strong>
                                    <span id="productDetailCategory"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>الماركة:</strong>
                                    <span id="productDetailBrand"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الموديل:</strong>
                                    <span id="productDetailModel"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6><i class="fas fa-store me-2"></i>المحلات التي يوجد بها هذا المنتج:</h6>
                        <div id="productShopsList">
                            <!-- Shops list will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="contactShop()">
                        <i class="fas fa-phone me-1"></i>
                        اتصل بالمحل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample products data with shop-specific quantities and prices
        let products = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020، يضمن تنقية الهواء الداخل للمحرك',
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 85,
                        quantity: 15
                    },
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 90,
                        quantity: 8
                    }
                ]
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد، يوفر أداء فرملة ممتاز وآمن',
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 120,
                        quantity: 8
                    }
                ]
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات، يوفر حماية فائقة للمحرك',
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 95,
                        quantity: 25
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 92,
                        quantity: 18
                    }
                ]
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير، ضمان سنتين',
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 280,
                        quantity: 5
                    }
                ]
            },
            {
                id: 5,
                name: 'إطار ميشلان 205/55R16',
                description: 'إطار ميشلان عالي الجودة، مقاس 205/55R16، مناسب للسيارات المتوسطة',
                partNumber: 'MICH-TIRE-006',
                category: 'عجلات',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 320,
                        quantity: 12
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 315,
                        quantity: 8
                    }
                ]
            },
            {
                id: 6,
                name: 'مكيف هواء هيونداي إلنترا',
                description: 'كمبروسر مكيف هواء لسيارة هيونداي إلنترا، يوفر تبريد فعال',
                partNumber: 'HYU-AC-005',
                category: 'تكييف',
                brand: 'هيونداي',
                model: 'إلنترا',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 450,
                        quantity: 3
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 440,
                        quantity: 2
                    }
                ]
            },
            {
                id: 7,
                name: 'شمعات إشعال NGK',
                description: 'شمعات إشعال عالية الجودة من NGK، مناسبة لمعظم السيارات',
                partNumber: 'NGK-SP-007',
                category: 'محرك',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 45,
                        quantity: 30
                    },
                    {
                        shopName: 'محل النجمة للسيارات',
                        price: 48,
                        quantity: 25
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 42,
                        quantity: 35
                    }
                ]
            },
            {
                id: 8,
                name: 'مساحات زجاج بوش',
                description: 'مساحات زجاج أمامي من بوش، مقاس 24 بوصة، مقاومة للعوامل الجوية',
                partNumber: 'BOSCH-WB-008',
                category: 'كهرباء',
                brand: '',
                model: '',
                image: '',
                shopDetails: [
                    {
                        shopName: 'محل الأصيل لقطع غيار السيارات',
                        price: 75,
                        quantity: 20
                    },
                    {
                        shopName: 'محل السرعة للسيارات',
                        price: 72,
                        quantity: 15
                    }
                ]
            }
        ];

        // Sample shops data
        let shops = [
            {
                id: 1,
                name: 'محل الأصيل لقطع غيار السيارات',
                description: 'محل متخصص في قطع غيار السيارات اليابانية والكورية مع خبرة 15 سنة',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'شارع الملك فهد، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار يابانية',
                openTime: '08:00',
                closeTime: '22:00',
                website: '',
                notes: 'خدمة توصيل مجاني داخل الرياض',
                rating: 4.5,
                productsCount: 15,
                joinDate: '2020-01-15'
            },
            {
                id: 2,
                name: 'محل النجمة للسيارات',
                description: 'محل شامل لجميع أنواع قطع غيار السيارات الأوروبية والأمريكية',
                phone: '+966507654321',
                email: '<EMAIL>',
                address: 'شارع العليا، الرياض',
                city: 'الرياض',
                specialty: 'قطع غيار أوروبية',
                openTime: '09:00',
                closeTime: '23:00',
                website: '',
                notes: 'ضمان سنة على جميع القطع',
                rating: 4.2,
                productsCount: 8,
                joinDate: '2021-03-20'
            },
            {
                id: 3,
                name: 'محل السرعة للسيارات',
                description: 'متخصص في قطع غيار السيارات الرياضية والفاخرة',
                phone: '+966509876543',
                email: '<EMAIL>',
                address: 'شارع التحلية، جدة',
                city: 'جدة',
                specialty: 'جميع الأنواع',
                openTime: '10:00',
                closeTime: '24:00',
                website: 'www.alsuraa.com',
                notes: 'متخصص في السيارات الفاخرة والرياضية',
                rating: 4.7,
                productsCount: 12,
                joinDate: '2019-11-10'
            }
        ];

        // Users data
        let users = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل لقطع غيار السيارات', shopId: 1 }
        };

        let nextShopId = 4;
        let nextUserId = 3;

        // Current user
        let currentUser = null;
        let nextProductId = 9;

        // Auto-refresh system
        let autoRefreshEnabled = true;
        let refreshInterval = null;

        // Show page function
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Load content based on page
            setTimeout(() => {
                if (pageId === 'home') {
                    loadProducts();
                } else if (pageId === 'shops') {
                    loadShops();
                } else if (pageId === 'shop') {
                    loadShopProducts();
                    updateShopStats();
                } else if (pageId === 'admin') {
                    updateAdminStats();
                }
            }, 100);
        }

        // Show dashboard based on user role
        function showDashboard() {
            if (currentUser) {
                if (currentUser.role === 'admin') {
                    showPage('admin');
                } else {
                    showPage('shop');
                }
            }
        }

        // Auto-refresh functions
        function refreshCurrentPage() {
            const activePage = document.querySelector('.page-content.active');
            if (!activePage) return;

            const pageId = activePage.id;

            console.log('Refreshing page:', pageId); // Debug log

            switch(pageId) {
                case 'home':
                    loadProducts();
                    break;
                case 'shops':
                    loadShops();
                    break;
                case 'shop':
                    loadShopProducts();
                    updateShopStats();
                    break;
                case 'admin':
                    updateAdminStats();
                    break;
                default:
                    break;
            }
        }

        function forceRefreshAll() {
            console.log('Force refreshing all data...');
            console.log('Current user:', currentUser);
            console.log('Total products:', products.length);
            console.log('Total shops:', shops.length);

            // Force refresh all data
            loadProducts();
            loadShops();
            if (currentUser && currentUser.role === 'shop') {
                console.log('Refreshing shop data for:', currentUser.name);
                loadShopProducts();
                updateShopStats();
            }
            if (currentUser && currentUser.role === 'admin') {
                updateAdminStats();
            }
        }

        // Clear and reload all data (for debugging)
        function debugReloadData() {
            console.log('=== DEBUG DATA RELOAD ===');
            console.log('Current user:', currentUser);
            console.log('Users:', users);
            console.log('Shops:', shops);
            console.log('Products:', products);

            if (currentUser && currentUser.role === 'shop') {
                const shopProducts = products.filter(product =>
                    product.shopDetails.some(shop => shop.shopName === currentUser.name)
                );
                console.log('Products for current shop:', shopProducts);
            }

            forceRefreshAll();
        }

        function updateShopStats() {
            if (!currentUser || currentUser.role !== 'shop') return;

            // Update shop products count in dashboard
            const shopProducts = products.filter(product =>
                product.shopDetails.some(shop => shop.shopName === currentUser.name)
            );

            const activeProducts = shopProducts.filter(product => {
                const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
                return shopDetail && shopDetail.quantity > 0;
            });

            // Calculate total quantity and average rating
            let totalQuantity = 0;
            shopProducts.forEach(product => {
                const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
                if (shopDetail) {
                    totalQuantity += shopDetail.quantity;
                }
            });

            // Update stats cards if they exist
            const statsCards = document.querySelectorAll('#shop .stat-card h4');
            if (statsCards.length >= 3) {
                statsCards[0].textContent = shopProducts.length; // Total products
                statsCards[1].textContent = activeProducts.length; // Active products
                statsCards[2].textContent = '4.5'; // Shop rating (static for now)
            }

            // Update shop info in shops array
            const currentShop = shops.find(shop => shop.name === currentUser.name);
            if (currentShop) {
                currentShop.productsCount = shopProducts.length;
            }
        }

        function updateAdminStats() {
            if (!currentUser || currentUser.role !== 'admin') return;

            // Update admin stats
            const totalShopsEl = document.querySelector('#admin .stat-card h4');
            const totalProductsEl = document.querySelector('#admin .stat-card:nth-child(2) h4');
            const totalUsersEl = document.querySelector('#admin .stat-card:nth-child(3) h4');

            if (totalShopsEl) totalShopsEl.textContent = shops.length;
            if (totalProductsEl) totalProductsEl.textContent = products.length;
            if (totalUsersEl) totalUsersEl.textContent = Object.keys(users).length;
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);

            refreshInterval = setInterval(() => {
                if (autoRefreshEnabled) {
                    refreshCurrentPage();
                }
            }, 2000); // Refresh every 2 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;

            const toggleBtn = document.getElementById('autoRefreshToggle');
            const statusIndicator = document.getElementById('refreshStatus');

            if (toggleBtn) {
                toggleBtn.innerHTML = autoRefreshEnabled ?
                    '<i class="fas fa-pause me-1"></i>إيقاف التحديث' :
                    '<i class="fas fa-play me-1"></i>تشغيل التحديث';
                toggleBtn.className = autoRefreshEnabled ?
                    'btn btn-warning btn-sm me-2' :
                    'btn btn-success btn-sm me-2';
            }

            if (statusIndicator) {
                if (autoRefreshEnabled) {
                    statusIndicator.style.display = 'inline-block';
                    statusIndicator.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-sync-alt fa-spin me-1"></i>
                            التحديث التلقائي نشط
                        </span>
                    `;
                } else {
                    statusIndicator.innerHTML = `
                        <span class="badge bg-secondary">
                            <i class="fas fa-pause me-1"></i>
                            التحديث التلقائي متوقف
                        </span>
                    `;
                }
            }

            if (autoRefreshEnabled) {
                startAutoRefresh();
                showNotification('تم تشغيل التحديث التلقائي', 'success');
            } else {
                stopAutoRefresh();
                showNotification('تم إيقاف التحديث التلقائي', 'warning');
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.opacity = '1';
            }, 100);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function triggerDataUpdate(type, action, data) {
            // Show notification about the update
            let message = '';
            switch(type) {
                case 'product':
                    message = action === 'add' ? `تم إضافة منتج: ${data.name}` :
                             action === 'edit' ? `تم تعديل منتج: ${data.name}` :
                             action === 'delete' ? `تم حذف منتج: ${data.name}` : 'تم تحديث منتج';
                    break;
                case 'shop':
                    message = action === 'add' ? `تم إضافة محل: ${data.name}` :
                             action === 'edit' ? `تم تعديل محل: ${data.name}` :
                             action === 'delete' ? `تم حذف محل: ${data.name}` : 'تم تحديث محل';
                    break;
                case 'user':
                    message = action === 'add' ? `تم إضافة مستخدم جديد` :
                             action === 'edit' ? `تم تعديل بيانات مستخدم` :
                             action === 'delete' ? `تم حذف مستخدم` : 'تم تحديث مستخدم';
                    break;
                default:
                    message = 'تم تحديث البيانات';
            }

            // Don't show duplicate notification for shops (already shown in addShop function)
            if (!(type === 'shop' && action === 'add')) {
                showNotification(message, 'success');
            }

            // Force immediate refresh
            setTimeout(() => {
                refreshCurrentPage();

                // Also update admin stats if admin is logged in
                if (currentUser && currentUser.role === 'admin') {
                    updateAdminStats();
                }

                // Update shop stats if shop owner is logged in
                if (currentUser && currentUser.role === 'shop') {
                    updateShopStats();
                }
            }, 300);
        }

        // Load products table
        function loadProducts() {
            const tableBody = document.getElementById('productsTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            let totalQuantity = 0;
            let allPrices = [];

            products.forEach(product => {
                // Calculate totals from shop details
                const productTotalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                const minPrice = Math.min(...product.shopDetails.map(shop => shop.price));
                const maxPrice = Math.max(...product.shopDetails.map(shop => shop.price));

                totalQuantity += productTotalQuantity;
                product.shopDetails.forEach(shop => allPrices.push(shop.price));

                // Create shops list with prices and quantities
                const shopsList = product.shopDetails.map(shopDetail => {
                    const shop = shops.find(s => s.name === shopDetail.shopName);
                    const phone = shop ? shop.phone : '';
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                            <button class="btn btn-outline-primary btn-sm ms-2" onclick="contactShop('${shopDetail.shopName}', '${phone}')" title="اتصل بالمحل">
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                    `;
                }).join('');

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    ${product.image ?
                                        `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">` :
                                        '<div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;"><i class="fas fa-cog text-muted"></i></div>'
                                    }
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل |
                                إجمالي الكمية: <span class="fw-bold">${productTotalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                                ${minPrice !== maxPrice ? `<br><small class="text-muted">أعلى: ${maxPrice} ريال</small>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="contactBestPriceShop(${product.id})" title="اتصل بأفضل سعر">
                                    <i class="fas fa-phone"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });

            // Update summary cards
            updateSummaryCards(totalQuantity, allPrices);
        }

        // Update summary cards
        function updateSummaryCards(totalQuantity, allPrices) {
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalShops').textContent = shops.length;
            document.getElementById('totalQuantity').textContent = totalQuantity;

            if (allPrices.length > 0) {
                const averagePrice = allPrices.reduce((sum, price) => sum + price, 0) / allPrices.length;
                document.getElementById('averagePrice').textContent = Math.round(averagePrice) + ' ريال';
            }
        }

        // Contact shop with best price
        function contactBestPriceShop(productId) {
            const product = products.find(p => p.id === productId);
            if (!product || !product.shopDetails.length) return;

            // Find shop with minimum price
            const bestPriceShop = product.shopDetails.reduce((min, shop) =>
                shop.price < min.price ? shop : min
            );

            const shop = shops.find(s => s.name === bestPriceShop.shopName);
            const phone = shop ? shop.phone : '';

            contactShop(bestPriceShop.shopName, phone);
        }

        // Load shops
        function loadShops() {
            const container = document.getElementById('shopsContainer');
            container.innerHTML = '';

            // Show add shop button for admin
            const addShopBtn = document.getElementById('addShopBtn');
            if (currentUser && currentUser.role === 'admin') {
                addShopBtn.style.display = 'inline-block';
            } else {
                addShopBtn.style.display = 'none';
            }

            shops.forEach(shop => {
                // Check if current user is admin for admin controls
                const isAdmin = currentUser && currentUser.role === 'admin';

                const shopCard = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <i class="fas fa-store fa-3x text-primary mb-2"></i>
                                    <h5 class="card-title">${shop.name}</h5>
                                </div>

                                <p class="text-muted small">${shop.description}</p>

                                <div class="mb-2">
                                    <div class="rating mb-1">
                                        ${generateStars(shop.rating)}
                                        <span class="ms-2 fw-bold">(${shop.rating})</span>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-phone me-1 text-primary"></i>${shop.phone}
                                    </small>
                                    ${shop.email ? `<small class="text-muted d-block"><i class="fas fa-envelope me-1 text-primary"></i>${shop.email}</small>` : ''}
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>${shop.address}
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-city me-1 text-primary"></i>${shop.city}
                                    </small>
                                </div>

                                ${shop.specialty ? `
                                <div class="mb-2">
                                    <span class="badge bg-secondary">${shop.specialty}</span>
                                </div>
                                ` : ''}

                                ${shop.openTime && shop.closeTime ? `
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1 text-primary"></i>
                                        ${shop.openTime} - ${shop.closeTime}
                                    </small>
                                </div>
                                ` : ''}

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-info">${shop.productsCount} منتج</span>
                                    <small class="text-muted">انضم: ${new Date(shop.joinDate).toLocaleDateString('ar-SA')}</small>
                                </div>

                                ${shop.notes ? `
                                <div class="alert alert-light p-2 mb-2">
                                    <small><i class="fas fa-info-circle me-1"></i>${shop.notes}</small>
                                </div>
                                ` : ''}
                            </div>

                            <div class="card-footer bg-transparent">
                                ${isAdmin ? `
                                <!-- Admin controls -->
                                <div class="row g-1 mb-2">
                                    <div class="col-4">
                                        <button class="btn btn-warning btn-sm w-100" onclick="editShop(${shop.id})" title="تعديل المحل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        <button class="btn btn-danger btn-sm w-100" onclick="deleteShop(${shop.id})" title="حذف المحل">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        <button class="btn btn-info btn-sm w-100" onclick="showShopDetails(${shop.id})" title="تفاصيل">
                                            <i class="fas fa-info"></i>
                                        </button>
                                    </div>
                                </div>
                                ` : ''}

                                <!-- Public controls -->
                                <div class="row g-1">
                                    <div class="col-8">
                                        <button class="btn btn-primary btn-sm w-100" onclick="contactShop('${shop.name}', '${shop.phone}')">
                                            <i class="fas fa-phone me-1"></i>
                                            اتصل
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        ${shop.website ? `
                                        <button class="btn btn-outline-primary btn-sm w-100" onclick="window.open('${shop.website.startsWith('http') ? shop.website : 'http://' + shop.website}', '_blank')" title="زيارة الموقع">
                                            <i class="fas fa-globe"></i>
                                        </button>
                                        ` : `
                                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="showShopDetails(${shop.id})" title="تفاصيل أكثر">
                                            <i class="fas fa-info"></i>
                                        </button>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += shopCard;
            });
        }

        // Load shop products (for shop dashboard)
        function loadShopProducts() {
            if (!currentUser || currentUser.role !== 'shop') {
                console.log('Not a shop user or not logged in');
                return;
            }

            const container = document.getElementById('shopProductsContainer');
            if (!container) {
                console.log('Shop products container not found');
                return;
            }

            console.log('Loading products for shop:', currentUser.name);
            console.log('Total products in system:', products.length);

            container.innerHTML = '';

            // Filter products for current shop only - be very specific about the name match
            const shopProducts = products.filter(product => {
                const hasShop = product.shopDetails.some(shop => {
                    const match = shop.shopName === currentUser.name;
                    if (match) {
                        console.log('Found product for shop:', product.name, 'in shop:', shop.shopName);
                    }
                    return match;
                });
                return hasShop;
            });

            console.log('Filtered products for current shop:', shopProducts.length);

            if (shopProducts.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>لا توجد منتجات</h5>
                            <p class="mb-0">لم تقم بإضافة أي منتجات بعد في محل "${currentUser.name}".</p>
                            <p class="mb-0">اضغط "إضافة منتج جديد" لبدء إضافة منتجاتك.</p>
                        </div>
                    </div>
                `;
                return;
            }

            shopProducts.forEach(product => {
                // Find this shop's details for the product
                const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
                if (!shopDetail) {
                    console.log('Shop detail not found for product:', product.name);
                    return;
                }

                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description.substring(0, 40)}...</p>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold text-primary">${shopDetail.price} ريال</span>
                                    <span class="badge bg-success">الكمية: ${shopDetail.quantity}</span>
                                </div>
                                ${product.category ? `<span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                ${product.partNumber ? `<br><small class="text-muted">رقم القطعة: ${product.partNumber}</small>` : ''}
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm ms-1" onclick="editShopProduct(${product.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm ms-1" onclick="removeProductFromShop(${product.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Generate stars for rating
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star text-warning"></i>';
                } else if (i - 0.5 <= rating) {
                    stars += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    stars += '<i class="far fa-star text-warning"></i>';
                }
            }
            return stars;
        }

        // Show product detail
        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            // Calculate price range and total quantity
            const prices = product.shopDetails.map(shop => shop.price);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);

            // Fill modal with product data
            document.getElementById('productDetailTitle').textContent = product.name;
            document.getElementById('productDetailName').textContent = product.name;
            document.getElementById('productDetailDescription').textContent = product.description;

            // Show price range
            const priceText = minPrice === maxPrice ?
                `${minPrice} ريال` :
                `${minPrice} - ${maxPrice} ريال`;
            document.getElementById('productDetailPrice').textContent = priceText;

            document.getElementById('productDetailQuantity').textContent = totalQuantity;
            document.getElementById('productDetailPartNumber').textContent = product.partNumber || '-';
            document.getElementById('productDetailCategory').textContent = product.category || '-';
            document.getElementById('productDetailBrand').textContent = product.brand || '-';
            document.getElementById('productDetailModel').textContent = product.model || '-';

            // Handle image
            const imageContainer = document.getElementById('productDetailImage');
            if (product.image) {
                imageContainer.innerHTML = `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">`;
            } else {
                imageContainer.innerHTML = '<i class="fas fa-cog fa-3x text-muted"></i>';
            }

            // Load shops list with individual prices and quantities
            const shopsList = document.getElementById('productShopsList');
            shopsList.innerHTML = '';

            if (product.shopDetails.length === 0) {
                shopsList.innerHTML = `
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هذا المنتج غير متوفر حالياً في أي محل
                    </div>
                `;
                return;
            }

            // Sort shops by price (lowest first)
            const sortedShopDetails = [...product.shopDetails].sort((a, b) => a.price - b.price);

            sortedShopDetails.forEach((shopDetail, index) => {
                const shop = shops.find(s => s.name === shopDetail.shopName);
                if (shop) {
                    const isLowestPrice = shopDetail.price === minPrice;
                    const isAvailable = shopDetail.quantity > 0;

                    shopsList.innerHTML += `
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded ${isLowestPrice ? 'border-success bg-light' : ''} ${!isAvailable ? 'opacity-75' : ''}">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <strong class="text-primary">${shop.name}</strong>
                                    <div>
                                        ${isLowestPrice ? '<span class="badge bg-success me-1">أفضل سعر</span>' : ''}
                                        ${!isAvailable ? '<span class="badge bg-danger">غير متوفر</span>' : '<span class="badge bg-success">متوفر</span>'}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted d-block">السعر:</small>
                                        <span class="fw-bold text-success">${shopDetail.price} ريال</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">الكمية المتوفرة:</small>
                                        <span class="badge ${isAvailable ? 'bg-info' : 'bg-secondary'}">${shopDetail.quantity}</span>
                                    </div>
                                </div>
                                <small class="text-muted d-block mt-1">
                                    <i class="fas fa-phone me-1"></i>${shop.phone}
                                    <i class="fas fa-map-marker-alt me-1 ms-2"></i>${shop.address}
                                </small>
                                <div class="rating mt-1">
                                    ${generateStars(shop.rating)}
                                    <small class="text-muted ms-1">(${shop.rating})</small>
                                </div>
                                ${shop.specialty ? `<small class="text-muted d-block"><i class="fas fa-tag me-1"></i>${shop.specialty}</small>` : ''}
                            </div>
                            <div class="ms-3">
                                <button class="btn btn-primary ${!isAvailable ? 'disabled' : ''}" onclick="contactShop('${shop.name}', '${shop.phone}')" ${!isAvailable ? 'disabled' : ''}>
                                    <i class="fas fa-phone me-1"></i>اتصل
                                </button>
                            </div>
                        </div>
                    `;
                }
            });

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('productDetailModal'));
            modal.show();
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('loginAlert');

            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };

                // Debug log for shop users
                if (currentUser.role === 'shop') {
                    console.log('Shop user logged in:', currentUser.name);
                    console.log('Shop ID:', currentUser.shopId);

                    // Find the actual shop data
                    const shopData = shops.find(shop => shop.id === currentUser.shopId);
                    if (shopData) {
                        console.log('Shop data found:', shopData.name);
                        // Update current user name to match shop data
                        currentUser.name = shopData.name;
                    } else {
                        console.log('Shop data not found for ID:', currentUser.shopId);
                    }
                }

                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('dashboardLink').style.display = 'inline';
                document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;

                // Show auto-refresh toggle button
                document.getElementById('autoRefreshToggle').style.display = 'inline-block';

                // Start auto-refresh
                startAutoRefresh();

                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';

                // Show notification
                showNotification(`مرحباً ${currentUser.name}! تم تشغيل التحديث التلقائي.`, 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    if (currentUser.role === 'admin') {
                        showPage('admin');
                    } else {
                        showPage('shop');
                    }
                }, 1000);

            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Add new product
        function addProduct(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'shop') {
                showNotification('غير مصرح لك بإضافة منتجات', 'error');
                return;
            }

            console.log('Adding product for shop:', currentUser.name);
            console.log('Current user data:', currentUser);

            const productName = document.getElementById('productName').value.trim();
            const partNumber = document.getElementById('partNumber').value.trim();
            const price = parseFloat(document.getElementById('productPrice').value);
            const quantity = parseInt(document.getElementById('productQuantity').value);

            // Validation
            if (!productName) {
                showNotification('يرجى إدخال اسم المنتج', 'warning');
                return;
            }

            if (isNaN(price) || price <= 0) {
                showNotification('يرجى إدخال سعر صحيح', 'warning');
                return;
            }

            if (isNaN(quantity) || quantity < 0) {
                showNotification('يرجى إدخال كمية صحيحة', 'warning');
                return;
            }

            // Check if this shop already has this exact product
            const existingProduct = products.find(p => {
                if (partNumber) {
                    // If part number is provided, check by part number
                    return p.partNumber === partNumber &&
                           p.shopDetails.some(shop => shop.shopName === currentUser.name);
                } else {
                    // If no part number, check by name
                    return p.name.toLowerCase() === productName.toLowerCase() &&
                           p.shopDetails.some(shop => shop.shopName === currentUser.name);
                }
            });

            if (existingProduct) {
                showNotification('هذا المنتج موجود بالفعل في محلك. يمكنك تعديله من قائمة منتجاتك.', 'warning');
                return;
            }

            // Check if product exists in other shops (to add this shop to it)
            const productInOtherShops = products.find(p => {
                if (partNumber) {
                    return p.partNumber === partNumber && p.name.toLowerCase() === productName.toLowerCase();
                } else {
                    return p.name.toLowerCase() === productName.toLowerCase();
                }
            });

            if (productInOtherShops) {
                // Add this shop to existing product
                console.log('Adding shop to existing product:', productInOtherShops.name);
                productInOtherShops.shopDetails.push({
                    shopName: currentUser.name,
                    price: price,
                    quantity: quantity
                });

                console.log('Updated product shop details:', productInOtherShops.shopDetails);
                showNotification(`تم إضافة المنتج "${productName}" إلى محلك بنجاح!`, 'success');
                triggerDataUpdate('product', 'edit', productInOtherShops);
            } else {
                // Create completely new product
                console.log('Creating new product for shop:', currentUser.name);
                const newProduct = {
                    id: nextProductId++,
                    name: productName,
                    description: document.getElementById('productDescription').value.trim(),
                    partNumber: partNumber,
                    category: document.getElementById('productCategory').value,
                    brand: document.getElementById('productBrand').value,
                    model: document.getElementById('productModel').value,
                    image: document.getElementById('productImage').value.trim(),
                    shopDetails: [{
                        shopName: currentUser.name,
                        price: price,
                        quantity: quantity
                    }]
                };

                products.push(newProduct);
                console.log('Added new product:', newProduct);
                console.log('Total products now:', products.length);

                showNotification(`تم إضافة المنتج الجديد "${productName}" بنجاح!`, 'success');
                triggerDataUpdate('product', 'add', newProduct);
            }

            // Reset form
            document.getElementById('addProductForm').reset();

            // Redirect to shop dashboard
            showPage('shop');
        }

        // Add new shop
        function addShop(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بإضافة محلات', 'error');
                return;
            }

            // Show loading state
            const submitBtn = document.getElementById('addShopSubmitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
            submitBtn.disabled = true;

            const username = document.getElementById('shopUsername').value.trim();
            const password = document.getElementById('shopPassword').value.trim();
            const shopName = document.getElementById('shopName').value.trim();
            const shopDescription = document.getElementById('shopDescription').value.trim();
            const shopPhone = document.getElementById('shopPhone').value.trim();
            const shopAddress = document.getElementById('shopAddress').value.trim();
            const shopCity = document.getElementById('shopCity').value;

            // Helper function to reset button
            const resetButton = () => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            };

            // Validation
            if (!username) {
                showNotification('يرجى إدخال اسم المستخدم', 'warning');
                resetButton();
                return;
            }

            if (!password) {
                showNotification('يرجى إدخال كلمة المرور', 'warning');
                resetButton();
                return;
            }

            if (!shopName) {
                showNotification('يرجى إدخال اسم المحل', 'warning');
                resetButton();
                return;
            }

            if (!shopDescription) {
                showNotification('يرجى إدخال وصف المحل', 'warning');
                resetButton();
                return;
            }

            if (!shopPhone) {
                showNotification('يرجى إدخال رقم الهاتف', 'warning');
                resetButton();
                return;
            }

            if (!shopAddress) {
                showNotification('يرجى إدخال العنوان', 'warning');
                resetButton();
                return;
            }

            if (!shopCity) {
                showNotification('يرجى اختيار المدينة', 'warning');
                resetButton();
                return;
            }

            // Check if username already exists
            if (users[username]) {
                showNotification('اسم المستخدم موجود بالفعل، اختر اسم مستخدم آخر', 'warning');
                resetButton();
                return;
            }

            // Check if shop name already exists
            if (shops.find(shop => shop.name.toLowerCase() === shopName.toLowerCase())) {
                showNotification('اسم المحل موجود بالفعل، اختر اسم محل آخر', 'warning');
                resetButton();
                return;
            }

            // Create new shop
            const newShop = {
                id: nextShopId++,
                name: shopName,
                description: shopDescription,
                phone: shopPhone,
                email: document.getElementById('shopEmail').value.trim(),
                address: shopAddress,
                city: shopCity,
                specialty: document.getElementById('shopSpecialty').value,
                openTime: document.getElementById('shopOpenTime').value,
                closeTime: document.getElementById('shopCloseTime').value,
                website: document.getElementById('shopWebsite').value.trim(),
                notes: document.getElementById('shopNotes').value.trim(),
                rating: 0,
                productsCount: 0,
                joinDate: new Date().toISOString().split('T')[0]
            };

            // Create new user account
            users[username] = {
                password: password,
                role: 'shop',
                name: shopName,
                shopId: newShop.id
            };

            // Add shop to shops array
            shops.push(newShop);

            console.log('Added new shop:', newShop);
            console.log('Added new user:', users[username]);
            console.log('Total shops now:', shops.length);

            // Reset form
            document.getElementById('addShopForm').reset();

            // Reset button
            resetButton();

            // Show success message first
            showNotification(`تم إضافة المحل "${shopName}" بنجاح!`, 'success');

            // Show login details in a separate notification
            setTimeout(() => {
                showNotification(`بيانات تسجيل الدخول: ${username} / ${password}`, 'info');
            }, 1000);

            // Force immediate refresh of all data
            forceRefreshAll();

            // Redirect to shops page and refresh it
            setTimeout(() => {
                showPage('shops');
                // Force another refresh after page change
                setTimeout(() => {
                    loadShops();
                    console.log('Shops after addition:', shops.length); // Debug log
                }, 200);
            }, 1500);
        }

        // Edit shop
        function editShop(shopId) {
            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بتعديل المحلات');
                return;
            }

            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                alert('المحل غير موجود');
                return;
            }

            // Find username for this shop
            const username = Object.keys(users).find(key =>
                users[key].role === 'shop' && users[key].shopId === shopId
            );

            // Fill form with shop data
            document.getElementById('editShopId').value = shop.id;
            document.getElementById('editShopUsername').value = username || '';
            document.getElementById('editShopUsernameDisplay').value = username || 'غير محدد';
            document.getElementById('editShopName').value = shop.name;
            document.getElementById('editShopDescription').value = shop.description;
            document.getElementById('editShopPhone').value = shop.phone;
            document.getElementById('editShopEmail').value = shop.email || '';
            document.getElementById('editShopAddress').value = shop.address;
            document.getElementById('editShopCity').value = shop.city;
            document.getElementById('editShopSpecialty').value = shop.specialty || '';
            document.getElementById('editShopOpenTime').value = shop.openTime || '';
            document.getElementById('editShopCloseTime').value = shop.closeTime || '';
            document.getElementById('editShopWebsite').value = shop.website || '';
            document.getElementById('editShopNotes').value = shop.notes || '';

            // Show edit page
            showPage('edit-shop');
        }

        // Update shop
        function updateShop(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بتعديل المحلات');
                return;
            }

            const shopId = parseInt(document.getElementById('editShopId').value);
            const username = document.getElementById('editShopUsername').value;
            const newPassword = document.getElementById('editShopPassword').value;
            const newShopName = document.getElementById('editShopName').value;

            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                alert('المحل غير موجود');
                return;
            }

            // Check if new shop name conflicts with existing shops (excluding current shop)
            const existingShop = shops.find(s =>
                s.id !== shopId && s.name.toLowerCase() === newShopName.toLowerCase()
            );
            if (existingShop) {
                alert('اسم المحل موجود بالفعل، اختر اسم محل آخر');
                return;
            }

            const oldShopName = shop.name;

            // Update shop data
            shop.name = newShopName;
            shop.description = document.getElementById('editShopDescription').value;
            shop.phone = document.getElementById('editShopPhone').value;
            shop.email = document.getElementById('editShopEmail').value;
            shop.address = document.getElementById('editShopAddress').value;
            shop.city = document.getElementById('editShopCity').value;
            shop.specialty = document.getElementById('editShopSpecialty').value;
            shop.openTime = document.getElementById('editShopOpenTime').value;
            shop.closeTime = document.getElementById('editShopCloseTime').value;
            shop.website = document.getElementById('editShopWebsite').value;
            shop.notes = document.getElementById('editShopNotes').value;

            // Update user data
            if (username && users[username]) {
                users[username].name = newShopName;
                if (newPassword.trim()) {
                    users[username].password = newPassword;
                }
                console.log('Updated user data:', users[username]);
            }

            // Update product references if shop name changed
            if (oldShopName !== newShopName) {
                console.log('Updating product references from', oldShopName, 'to', newShopName);
                products.forEach(product => {
                    product.shopDetails.forEach(shopDetail => {
                        if (shopDetail.shopName === oldShopName) {
                            shopDetail.shopName = newShopName;
                            console.log('Updated product:', product.name, 'shop name to:', newShopName);
                        }
                    });
                });

                // Update current user name if they are the shop being edited
                if (currentUser && currentUser.role === 'shop' && currentUser.name === oldShopName) {
                    currentUser.name = newShopName;
                    document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;
                    console.log('Updated current user name to:', newShopName);
                }
            }

            // Show success message
            let message = `تم تحديث بيانات المحل "${newShopName}" بنجاح!`;
            if (newPassword.trim()) {
                message += ` تم تغيير كلمة المرور أيضاً.`;
            }
            if (oldShopName !== newShopName) {
                message += ` تم تحديث اسم المحل في جميع المنتجات المرتبطة.`;
            }

            showNotification(message, 'success');

            // Force refresh all data
            forceRefreshAll();

            // Redirect to shops page
            setTimeout(() => {
                showPage('shops');
                // Force another refresh after page change
                setTimeout(() => {
                    loadShops();
                }, 200);
            }, 1000);
        }

        // Delete shop
        function deleteShop(shopId) {
            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بحذف المحلات');
                return;
            }

            const shop = shops.find(s => s.id === shopId);
            if (!shop) {
                alert('المحل غير موجود');
                return;
            }

            // Count products that will be affected
            const affectedProducts = products.filter(product =>
                product.shopDetails.some(shopDetail => shopDetail.shopName === shop.name)
            );

            const productsToDelete = products.filter(product =>
                product.shopDetails.length === 1 && product.shopDetails[0].shopName === shop.name
            );

            const productsToUpdate = affectedProducts.filter(product =>
                product.shopDetails.length > 1 && product.shopDetails.some(shopDetail => shopDetail.shopName === shop.name)
            );

            // Confirmation message
            let confirmMessage = `هل أنت متأكد من حذف المحل "${shop.name}"؟\n\n`;
            confirmMessage += `سيتم حذف:\n`;
            confirmMessage += `- المحل وجميع بياناته\n`;
            confirmMessage += `- حساب تسجيل الدخول الخاص بالمحل\n`;

            if (productsToDelete.length > 0) {
                confirmMessage += `- ${productsToDelete.length} منتج (متوفر في هذا المحل فقط)\n`;
            }

            if (productsToUpdate.length > 0) {
                confirmMessage += `\nسيتم تحديث ${productsToUpdate.length} منتج (إزالة هذا المحل من قائمة المحلات المتوفرة)\n`;
            }

            confirmMessage += `\nهذا الإجراء لا يمكن التراجع عنه!`;

            if (!confirm(confirmMessage)) {
                return;
            }

            // Find and delete user account
            const username = Object.keys(users).find(key =>
                users[key].role === 'shop' && users[key].shopId === shopId
            );
            if (username) {
                delete users[username];
            }

            // Remove shop from products
            products.forEach(product => {
                product.shopDetails = product.shopDetails.filter(shopDetail =>
                    shopDetail.shopName !== shop.name
                );
            });

            // Remove products that have no shops left
            for (let i = products.length - 1; i >= 0; i--) {
                if (products[i].shopDetails.length === 0) {
                    products.splice(i, 1);
                }
            }

            // Remove shop from shops array
            const shopIndex = shops.findIndex(s => s.id === shopId);
            if (shopIndex > -1) {
                shops.splice(shopIndex, 1);
            }

            // Show success message
            let successMessage = `تم حذف المحل "${shop.name}" بنجاح!`;
            if (productsToDelete.length > 0) {
                successMessage += ` تم حذف ${productsToDelete.length} منتج.`;
            }
            if (productsToUpdate.length > 0) {
                successMessage += ` تم تحديث ${productsToUpdate.length} منتج.`;
            }

            showNotification(successMessage, 'success');

            // Force refresh all data
            forceRefreshAll();

            // Reload shops page
            setTimeout(() => {
                showPage('shops');
                // Force another refresh after page change
                setTimeout(() => {
                    loadShops();
                }, 200);
            }, 1000);
        }

        // Show shop details
        function showShopDetails(shopId) {
            const shop = shops.find(s => s.id === shopId);
            if (!shop) return;

            let details = `تفاصيل ${shop.name}\n\n`;
            details += `الوصف: ${shop.description}\n`;
            details += `الهاتف: ${shop.phone}\n`;
            if (shop.email) details += `البريد: ${shop.email}\n`;
            details += `العنوان: ${shop.address}\n`;
            details += `المدينة: ${shop.city}\n`;
            if (shop.specialty) details += `التخصص: ${shop.specialty}\n`;
            if (shop.openTime && shop.closeTime) details += `أوقات العمل: ${shop.openTime} - ${shop.closeTime}\n`;
            details += `التقييم: ${shop.rating}/5\n`;
            details += `عدد المنتجات: ${shop.productsCount}\n`;
            details += `تاريخ الانضمام: ${new Date(shop.joinDate).toLocaleDateString('ar-SA')}\n`;
            if (shop.notes) details += `ملاحظات: ${shop.notes}\n`;

            alert(details);
        }

        // Edit shop product
        function editShopProduct(productId) {
            if (!currentUser || currentUser.role !== 'shop') {
                alert('غير مصرح لك بتعديل المنتجات');
                return;
            }

            const product = products.find(p => p.id === productId);
            if (!product) {
                alert('المنتج غير موجود');
                return;
            }

            const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
            if (!shopDetail) {
                alert('هذا المنتج غير موجود في محلك');
                return;
            }

            // Create edit form modal
            const editModal = `
                <div class="modal fade" id="editProductModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل منتج: ${product.name}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editProductForm">
                                    <div class="mb-3">
                                        <label class="form-label">السعر في محلك *</label>
                                        <input type="number" class="form-control" id="editPrice" value="${shopDetail.price}" step="0.01" min="0" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الكمية المتوفرة *</label>
                                        <input type="number" class="form-control" id="editQuantity" value="${shopDetail.quantity}" min="0" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="saveProductEdit(${productId})">حفظ التعديلات</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('editProductModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', editModal);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
            modal.show();
        }

        // Save product edit
        function saveProductEdit(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const shopDetail = product.shopDetails.find(shop => shop.shopName === currentUser.name);
            if (!shopDetail) return;

            const newPrice = parseFloat(document.getElementById('editPrice').value);
            const newQuantity = parseInt(document.getElementById('editQuantity').value);

            if (isNaN(newPrice) || isNaN(newQuantity) || newPrice < 0 || newQuantity < 0) {
                alert('يرجى إدخال قيم صحيحة للسعر والكمية');
                return;
            }

            // Update shop details
            shopDetail.price = newPrice;
            shopDetail.quantity = newQuantity;

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            modal.hide();

            // Trigger update notification
            triggerDataUpdate('product', 'edit', product);

            showNotification(`تم تحديث بيانات المنتج "${product.name}" في محلك`, 'success');
        }

        // Remove product from shop
        function removeProductFromShop(productId) {
            if (!currentUser || currentUser.role !== 'shop') {
                alert('غير مصرح لك بحذف المنتجات');
                return;
            }

            const product = products.find(p => p.id === productId);
            if (!product) {
                alert('المنتج غير موجود');
                return;
            }

            const shopDetailIndex = product.shopDetails.findIndex(shop => shop.shopName === currentUser.name);
            if (shopDetailIndex === -1) {
                alert('هذا المنتج غير موجود في محلك');
                return;
            }

            // Confirmation
            if (!confirm(`هل أنت متأكد من حذف المنتج "${product.name}" من محلك؟\n\nإذا كان هذا المنتج متوفر في محلك فقط، سيتم حذفه نهائياً من النظام.`)) {
                return;
            }

            // Remove shop from product
            product.shopDetails.splice(shopDetailIndex, 1);

            // If no shops left, remove product entirely
            if (product.shopDetails.length === 0) {
                const productIndex = products.findIndex(p => p.id === productId);
                if (productIndex > -1) {
                    products.splice(productIndex, 1);
                }
                showNotification(`تم حذف المنتج "${product.name}" نهائياً من النظام`, 'success');
            } else {
                showNotification(`تم حذف المنتج "${product.name}" من محلك`, 'success');
            }

            // Trigger update notification
            triggerDataUpdate('product', 'delete', product);
        }

        // Edit product (placeholder for general use)
        function editProduct(productId) {
            if (currentUser && currentUser.role === 'shop') {
                editShopProduct(productId);
            } else {
                alert('ميزة تعديل المنتج متوفرة لأصحاب المحلات فقط');
            }
        }

        // Logout
        function logout() {
            // Stop auto-refresh
            stopAutoRefresh();

            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('autoRefreshToggle').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';

            // Show notification
            showNotification('تم تسجيل الخروج بنجاح', 'info');

            showPage('home');
        }

        // Contact shop
        function contactShop(shopName, phone) {
            if (phone) {
                if (confirm(`هل تريد الاتصال بـ ${shopName} على الرقم ${phone}؟`)) {
                    window.open(`tel:${phone}`, '_self');
                }
            } else {
                alert(`للتواصل مع ${shopName || 'المحل'}`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();

            // Add event listener for add product form
            const addProductForm = document.getElementById('addProductForm');
            if (addProductForm) {
                addProductForm.addEventListener('submit', addProduct);
            }

            // Add event listener for add shop form
            const addShopForm = document.getElementById('addShopForm');
            if (addShopForm) {
                addShopForm.addEventListener('submit', addShop);
            }

            // Add event listener for edit shop form
            const editShopForm = document.getElementById('editShopForm');
            if (editShopForm) {
                editShopForm.addEventListener('submit', updateShop);
            }

            // Show welcome notification
            showNotification('مرحباً بك في تطبيق قطع غيار السيارات! التحديث التلقائي متوفر بعد تسجيل الدخول.', 'info');

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl + R to toggle auto-refresh
                if (e.ctrlKey && e.key === 'r' && currentUser) {
                    e.preventDefault();
                    toggleAutoRefresh();
                }

                // Ctrl + H to go home
                if (e.ctrlKey && e.key === 'h') {
                    e.preventDefault();
                    showPage('home');
                }
            });
        });
    </script>
</body>
</html>
