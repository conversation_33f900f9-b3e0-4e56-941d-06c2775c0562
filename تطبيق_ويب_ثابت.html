<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
        
        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c3e50 !important;
        }
        
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
        }

        .product-detail-modal .modal-dialog {
            max-width: 800px;
        }

        .shop-badge {
            display: inline-block;
            margin: 2px;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .product-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('search')">البحث</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showDashboard()" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">
                            أفضل قطع غيار السيارات
                        </h1>
                        <p class="lead mb-4">
                            اكتشف أفضل قطع غيار السيارات من محلات موثوقة ومعتمدة
                        </p>
                        <button class="btn btn-light btn-lg" onclick="showPage('search')">
                            <i class="fas fa-search me-2"></i>
                            ابحث الآن
                        </button>
                    </div>
                    <div class="col-lg-6 text-center">
                        <i class="fas fa-car fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section class="py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 class="fw-bold">أحدث القطع</h2>
                        <p class="text-muted">اكتشف أحدث قطع الغيار المضافة</p>
                    </div>
                </div>
                
                <div class="row" id="productsContainer">
                    <!-- Products will be loaded here -->
                </div>
            </div>
        </section>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h3 class="fw-bold">تسجيل الدخول</h3>
                            </div>
                            
                            <div id="loginAlert" class="alert" style="display: none;"></div>
                            
                            <form id="loginForm" onsubmit="handleLogin(event)">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">حسابات تجريبية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>المدير:</strong><br>
                                            <small>admin / admin123</small>
                                        </div>
                                        <div class="col-6">
                                            <strong>المحل:</strong><br>
                                            <small>shop1 / shop123</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة تحكم المدير
                            </h2>
                            <p class="mb-0">مرحباً بك في لوحة التحكم الرئيسية</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">5</h4>
                        <p class="mb-0">إجمالي المحلات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">24</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">8</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">التقييمات</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المدير تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة النظام بالكامل - إضافة محلات، إدارة مستخدمين، مراقبة الإحصائيات</p>
            </div>
        </div>
    </div>

    <!-- Shop Dashboard -->
    <div id="shop" class="page-content">
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card card text-white">
                        <div class="card-body">
                            <h2 class="fw-bold">
                                <i class="fas fa-store me-2"></i>
                                لوحة تحكم المحل
                            </h2>
                            <p class="mb-0">محل الأصيل لقطع غيار السيارات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-primary text-white stat-card">
                        <h4 class="fw-bold">15</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-success text-white stat-card">
                        <h4 class="fw-bold">12</h4>
                        <p class="mb-0">المنتجات النشطة</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card bg-info text-white stat-card">
                        <h4 class="fw-bold">4.5</h4>
                        <p class="mb-0">تقييم المحل</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أحدث المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>رقم القطعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>فلتر هواء تويوتا كامري</td>
                                    <td>85 ريال</td>
                                    <td>15</td>
                                    <td>TOY-AF-001</td>
                                </tr>
                                <tr>
                                    <td>تيل فرامل هوندا أكورد</td>
                                    <td>120 ريال</td>
                                    <td>8</td>
                                    <td>HON-BP-002</td>
                                </tr>
                                <tr>
                                    <td>زيت محرك موبيل 1</td>
                                    <td>95 ريال</td>
                                    <td>25</td>
                                    <td>MOB-OIL-003</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">إدارة المنتجات</h5>
                            <button class="btn btn-success" onclick="showPage('add-product')">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row" id="shopProductsContainer">
                                <!-- Shop products will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-success mt-4">
                <h5><i class="fas fa-check-circle me-2"></i>لوحة تحكم المحل تعمل بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إدارة منتجاتك - إضافة، تعديل، حذف المنتجات وإدارة الأسعار والكميات</p>
            </div>
        </div>
    </div>

    <!-- Search Page -->
    <div id="search" class="page-content">
        <div class="container py-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث المتقدم</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="اسم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select">
                                <option>جميع الماركات</option>
                                <option>تويوتا</option>
                                <option>هوندا</option>
                                <option>نيسان</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="رقم القطعة...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100">بحث</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-search me-2"></i>نظام البحث المتقدم</h5>
                <p class="mb-0">يمكنك البحث عن قطع الغيار حسب الاسم، الماركة، أو رقم القطعة</p>
            </div>
        </div>
    </div>

    <!-- Shops Page -->
    <div id="shops" class="page-content">
        <div class="container py-4">
            <h2 class="text-center mb-4">جميع المحلات</h2>

            <div class="row" id="shopsContainer">
                <!-- Shops will be loaded here -->
            </div>

            <div class="alert alert-info">
                <h5><i class="fas fa-store me-2"></i>المحلات المعتمدة</h5>
                <p class="mb-0">جميع المحلات مراجعة ومعتمدة لضمان جودة المنتجات والخدمة</p>
            </div>
        </div>
    </div>

    <!-- Add Product Page -->
    <div id="add-product" class="page-content">
        <div class="container py-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="addProductForm" class="product-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productName" placeholder="اسم المنتج" required>
                                            <label for="productName">اسم المنتج *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="partNumber" placeholder="رقم القطعة">
                                            <label for="partNumber">رقم القطعة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <textarea class="form-control" id="productDescription" placeholder="وصف المنتج" style="height: 100px"></textarea>
                                    <label for="productDescription">وصف المنتج</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productPrice" placeholder="السعر" step="0.01" min="0" required>
                                            <label for="productPrice">السعر (ريال) *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="productQuantity" placeholder="الكمية" min="0" required>
                                            <label for="productQuantity">الكمية المتوفرة *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <select class="form-select" id="productCategory">
                                                <option value="">اختر الفئة</option>
                                                <option value="فرامل">فرامل</option>
                                                <option value="محرك">محرك</option>
                                                <option value="تكييف">تكييف</option>
                                                <option value="كهرباء">كهرباء</option>
                                                <option value="عجلات">عجلات</option>
                                                <option value="زيوت">زيوت</option>
                                                <option value="فلاتر">فلاتر</option>
                                            </select>
                                            <label for="productCategory">الفئة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="productBrand">
                                                <option value="">اختر الماركة</option>
                                                <option value="تويوتا">تويوتا</option>
                                                <option value="هوندا">هوندا</option>
                                                <option value="نيسان">نيسان</option>
                                                <option value="هيونداي">هيونداي</option>
                                                <option value="كيا">كيا</option>
                                                <option value="مازدا">مازدا</option>
                                                <option value="ميتسوبيشي">ميتسوبيشي</option>
                                            </select>
                                            <label for="productBrand">ماركة السيارة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="productModel" placeholder="موديل السيارة">
                                            <label for="productModel">موديل السيارة</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="url" class="form-control" id="productImage" placeholder="رابط الصورة">
                                    <label for="productImage">رابط صورة المنتج</label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="showPage('shop')">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal fade" id="productDetailModal" tabindex="-1">
        <div class="modal-dialog product-detail-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailTitle">تفاصيل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="productDetailImage" class="product-image mb-3">
                                <i class="fas fa-cog fa-3x text-muted"></i>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4 id="productDetailName"></h4>
                            <p id="productDetailDescription" class="text-muted"></p>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>السعر:</strong>
                                    <span id="productDetailPrice" class="text-primary fs-5"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الكمية:</strong>
                                    <span id="productDetailQuantity"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>رقم القطعة:</strong>
                                    <span id="productDetailPartNumber"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الفئة:</strong>
                                    <span id="productDetailCategory"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>الماركة:</strong>
                                    <span id="productDetailBrand"></span>
                                </div>
                                <div class="col-6">
                                    <strong>الموديل:</strong>
                                    <span id="productDetailModel"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6><i class="fas fa-store me-2"></i>المحلات التي يوجد بها هذا المنتج:</h6>
                        <div id="productShopsList">
                            <!-- Shops list will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="contactShop()">
                        <i class="fas fa-phone me-1"></i>
                        اتصل بالمحل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample products data
        let products = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020، يضمن تنقية الهواء الداخل للمحرك',
                price: 85,
                quantity: 15,
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري',
                image: '',
                shops: ['محل الأصيل لقطع غيار السيارات', 'محل النجمة للسيارات']
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد، يوفر أداء فرملة ممتاز وآمن',
                price: 120,
                quantity: 8,
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد',
                image: '',
                shops: ['محل الأصيل لقطع غيار السيارات']
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات، يوفر حماية فائقة للمحرك',
                price: 95,
                quantity: 25,
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: '',
                model: '',
                image: '',
                shops: ['محل الأصيل لقطع غيار السيارات', 'محل السرعة للسيارات']
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير، ضمان سنتين',
                price: 280,
                quantity: 5,
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما',
                image: '',
                shops: ['محل النجمة للسيارات']
            }
        ];

        // Sample shops data
        const shops = [
            {
                id: 1,
                name: 'محل الأصيل لقطع غيار السيارات',
                description: 'محل متخصص في قطع غيار السيارات اليابانية والكورية مع خبرة 15 سنة',
                phone: '+966501234567',
                address: 'شارع الملك فهد، الرياض',
                rating: 4.5,
                productsCount: 15
            },
            {
                id: 2,
                name: 'محل النجمة للسيارات',
                description: 'محل شامل لجميع أنواع قطع غيار السيارات الأوروبية والأمريكية',
                phone: '+966507654321',
                address: 'شارع العليا، الرياض',
                rating: 4.2,
                productsCount: 8
            },
            {
                id: 3,
                name: 'محل السرعة للسيارات',
                description: 'متخصص في قطع غيار السيارات الرياضية والفاخرة',
                phone: '+966509876543',
                address: 'شارع التحلية، جدة',
                rating: 4.7,
                productsCount: 12
            }
        ];

        // Users data
        const users = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير' },
            'shop1': { password: 'shop123', role: 'shop', name: 'محل الأصيل' }
        };

        // Current user
        let currentUser = null;
        let nextProductId = 5;

        // Show page function
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Load content based on page
            if (pageId === 'home') {
                loadProducts();
            } else if (pageId === 'shops') {
                loadShops();
            } else if (pageId === 'shop') {
                loadShopProducts();
            }
        }

        // Show dashboard based on user role
        function showDashboard() {
            if (currentUser) {
                if (currentUser.role === 'admin') {
                    showPage('admin');
                } else {
                    showPage('shop');
                }
            }
        }

        // Load products
        function loadProducts() {
            const container = document.getElementById('productsContainer');
            container.innerHTML = '';

            products.forEach(product => {
                const productCard = `
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card product-card h-100">
                            <div class="product-image">
                                ${product.image ? `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">` : '<i class="fas fa-cog fa-3x text-muted"></i>'}
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="card-text text-muted small">${product.description.substring(0, 50)}...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold text-primary">${product.price} ريال</span>
                                    <small class="text-muted">${product.shops.length} محل</small>
                                </div>
                                ${product.partNumber ? `<small class="text-muted d-block mt-1">رقم القطعة: ${product.partNumber}</small>` : ''}
                                <div class="mt-2">
                                    <span class="badge bg-success">متوفر (${product.quantity})</span>
                                    ${product.category ? `<span class="badge bg-info ms-1">${product.category}</span>` : ''}
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="row g-1">
                                    <div class="col-6">
                                        <button class="btn btn-primary btn-sm w-100" onclick="showProductDetail(${product.id})">
                                            <i class="fas fa-eye me-1"></i>
                                            تفاصيل
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-success btn-sm w-100" onclick="contactShop('${product.shops[0]}')">
                                            <i class="fas fa-phone me-1"></i>
                                            اتصل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Load shops
        function loadShops() {
            const container = document.getElementById('shopsContainer');
            container.innerHTML = '';

            shops.forEach(shop => {
                const shopCard = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-store fa-3x text-primary mb-3"></i>
                                <h5>${shop.name}</h5>
                                <p class="text-muted">${shop.description}</p>
                                <div class="rating mb-2">
                                    ${generateStars(shop.rating)}
                                    <span class="ms-2">(${shop.rating})</span>
                                </div>
                                <p class="text-muted"><i class="fas fa-phone me-1"></i> ${shop.phone}</p>
                                <p class="text-muted"><i class="fas fa-map-marker-alt me-1"></i> ${shop.address}</p>
                                <div class="mt-2">
                                    <span class="badge bg-info">${shop.productsCount} منتج</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <button class="btn btn-primary btn-sm w-100" onclick="contactShop('${shop.name}', '${shop.phone}')">
                                    <i class="fas fa-phone me-1"></i>
                                    اتصل بالمحل
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += shopCard;
            });
        }

        // Load shop products (for shop dashboard)
        function loadShopProducts() {
            if (!currentUser || currentUser.role !== 'shop') return;

            const container = document.getElementById('shopProductsContainer');
            if (!container) return;

            container.innerHTML = '';

            // Filter products for current shop
            const shopProducts = products.filter(product =>
                product.shops.includes(currentUser.name)
            );

            shopProducts.forEach(product => {
                const productCard = `
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <p class="text-muted small">${product.description.substring(0, 40)}...</p>
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold text-primary">${product.price} ريال</span>
                                    <span class="badge bg-success">${product.quantity}</span>
                                </div>
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm ms-1" onclick="editProduct(${product.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productCard;
            });
        }

        // Generate stars for rating
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star text-warning"></i>';
                } else if (i - 0.5 <= rating) {
                    stars += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    stars += '<i class="far fa-star text-warning"></i>';
                }
            }
            return stars;
        }

        // Show product detail
        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            // Fill modal with product data
            document.getElementById('productDetailTitle').textContent = product.name;
            document.getElementById('productDetailName').textContent = product.name;
            document.getElementById('productDetailDescription').textContent = product.description;
            document.getElementById('productDetailPrice').textContent = product.price + ' ريال';
            document.getElementById('productDetailQuantity').textContent = product.quantity;
            document.getElementById('productDetailPartNumber').textContent = product.partNumber || '-';
            document.getElementById('productDetailCategory').textContent = product.category || '-';
            document.getElementById('productDetailBrand').textContent = product.brand || '-';
            document.getElementById('productDetailModel').textContent = product.model || '-';

            // Handle image
            const imageContainer = document.getElementById('productDetailImage');
            if (product.image) {
                imageContainer.innerHTML = `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">`;
            } else {
                imageContainer.innerHTML = '<i class="fas fa-cog fa-3x text-muted"></i>';
            }

            // Load shops list
            const shopsList = document.getElementById('productShopsList');
            shopsList.innerHTML = '';
            product.shops.forEach(shopName => {
                const shop = shops.find(s => s.name === shopName);
                if (shop) {
                    shopsList.innerHTML += `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <strong>${shop.name}</strong><br>
                                <small class="text-muted">
                                    <i class="fas fa-phone me-1"></i>${shop.phone}
                                    <i class="fas fa-map-marker-alt me-1 ms-2"></i>${shop.address}
                                </small>
                            </div>
                            <div>
                                <div class="rating mb-1">
                                    ${generateStars(shop.rating)}
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="contactShop('${shop.name}', '${shop.phone}')">
                                    <i class="fas fa-phone me-1"></i>اتصل
                                </button>
                            </div>
                        </div>
                    `;
                }
            });

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('productDetailModal'));
            modal.show();
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('loginAlert');

            if (users[username] && users[username].password === password) {
                currentUser = { username, ...users[username] };

                // Update UI
                document.getElementById('loginLink').style.display = 'none';
                document.getElementById('logoutLink').style.display = 'inline';
                document.getElementById('dashboardLink').style.display = 'inline';
                document.getElementById('dashboardLink').textContent = currentUser.role === 'admin' ? 'لوحة المدير' : 'لوحة المحل';
                document.getElementById('userWelcome').style.display = 'inline';
                document.getElementById('userWelcome').textContent = `مرحباً ${currentUser.name}`;

                // Show success message
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                alertDiv.style.display = 'block';

                // Redirect to dashboard
                setTimeout(() => {
                    if (currentUser.role === 'admin') {
                        showPage('admin');
                    } else {
                        showPage('shop');
                    }
                }, 1000);

            } else {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                alertDiv.style.display = 'block';
            }
        }

        // Add new product
        function addProduct(event) {
            event.preventDefault();

            if (!currentUser || currentUser.role !== 'shop') {
                alert('غير مصرح لك بإضافة منتجات');
                return;
            }

            const newProduct = {
                id: nextProductId++,
                name: document.getElementById('productName').value,
                description: document.getElementById('productDescription').value,
                price: parseFloat(document.getElementById('productPrice').value),
                quantity: parseInt(document.getElementById('productQuantity').value),
                partNumber: document.getElementById('partNumber').value,
                category: document.getElementById('productCategory').value,
                brand: document.getElementById('productBrand').value,
                model: document.getElementById('productModel').value,
                image: document.getElementById('productImage').value,
                shops: [currentUser.name]
            };

            products.push(newProduct);

            // Reset form
            document.getElementById('addProductForm').reset();

            // Show success message
            alert('تم إضافة المنتج بنجاح!');

            // Redirect to shop dashboard
            showPage('shop');
        }

        // Edit product (placeholder)
        function editProduct(productId) {
            alert('ميزة تعديل المنتج ستكون متوفرة قريباً');
        }

        // Logout
        function logout() {
            currentUser = null;
            document.getElementById('loginLink').style.display = 'inline';
            document.getElementById('logoutLink').style.display = 'none';
            document.getElementById('dashboardLink').style.display = 'none';
            document.getElementById('userWelcome').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginAlert').style.display = 'none';
            showPage('home');
        }

        // Contact shop
        function contactShop(shopName, phone) {
            if (phone) {
                if (confirm(`هل تريد الاتصال بـ ${shopName} على الرقم ${phone}؟`)) {
                    window.open(`tel:${phone}`, '_self');
                }
            } else {
                alert(`للتواصل مع ${shopName || 'المحل'}`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();

            // Add event listener for add product form
            const addProductForm = document.getElementById('addProductForm');
            if (addProductForm) {
                addProductForm.addEventListener('submit', addProduct);
            }
        });
    </script>
</body>
</html>
