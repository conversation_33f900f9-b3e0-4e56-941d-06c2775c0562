<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق قطع غيار السيارات - نسخة سريعة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
        }
        
        .badge-custom {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-car me-2"></i>
                قطع غيار السيارات - تفاعلي
            </a>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showPage('home')">الرئيسية</a>
                <a class="nav-link" href="#" onclick="showPage('shops')">المحلات</a>
                <a class="nav-link" href="#" onclick="showPage('login')" id="loginLink">تسجيل الدخول</a>
                <a class="nav-link" href="#" onclick="logout()" id="logoutLink" style="display: none;">تسجيل الخروج</a>
                <a class="nav-link" href="#" onclick="showPage('dashboard')" id="dashboardLink" style="display: none;"></a>
                <span class="nav-link" id="userWelcome" style="display: none;"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
            <p class="lead mb-4">نسخة سريعة مع بيانات فورية - لا انتظار!</p>
            <div class="alert alert-light d-inline-block">
                <h5><i class="fas fa-bolt me-2"></i>تحميل فوري!</h5>
                <p class="mb-0">جميع البيانات محملة مسبقاً</p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Home Page -->
        <div id="home" class="page-content">
            <!-- Products Table -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول قطع الغيار والمحلات
                    </h5>
                    <button class="btn btn-light btn-sm" onclick="loadData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 25%;">اسم القطعة</th>
                                <th style="width: 15%;">رقم القطعة</th>
                                <th style="width: 35%;">المحلات والأسعار</th>
                                <th style="width: 15%;">أقل سعر</th>
                                <th style="width: 10%;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- البيانات ستحمل هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h4 id="totalProducts">4</h4>
                        <p class="mb-0">إجمالي القطع</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <h4 id="totalShops">1</h4>
                        <p class="mb-0">إجمالي المحلات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <h4 id="totalQuantity">53</h4>
                        <p class="mb-0">إجمالي الكميات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white text-center">
                    <div class="card-body">
                        <h4 id="averagePrice">145 ريال</h4>
                        <p class="mb-0">متوسط الأسعار</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إدارة النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100 mb-2" onclick="showAddShopModal()">
                            <i class="fas fa-store me-2"></i>
                            إضافة محل جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="showAddProductModal()">
                            <i class="fas fa-box me-2"></i>
                            إضافة منتج جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="showShopsList()">
                            <i class="fas fa-list me-2"></i>
                            عرض المحلات
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="showStatistics()">
                            <i class="fas fa-chart-bar me-2"></i>
                            الإحصائيات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shops List -->
        <div class="card mt-4" id="shopsCard" style="display: none;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-store me-2"></i>
                    قائمة المحلات
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="shopsList">
                    <!-- المحلات ستظهر هنا -->
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card mt-4" id="statisticsCard" style="display: none;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات مفصلة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📊 إحصائيات المنتجات:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>فلاتر:</span>
                                <span class="badge bg-primary">1 منتج</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>فرامل:</span>
                                <span class="badge bg-primary">1 منتج</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>زيوت:</span>
                                <span class="badge bg-primary">1 منتج</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>كهرباء:</span>
                                <span class="badge bg-primary">1 منتج</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🏪 إحصائيات المحلات:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>محلات نشطة:</span>
                                <span class="badge bg-success">1</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إجمالي المنتجات:</span>
                                <span class="badge bg-info">4</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إجمالي القيمة:</span>
                                <span class="badge bg-warning">7,675 ريال</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>متوسط التقييم:</span>
                                <span class="badge bg-danger">4.5 ⭐</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات سريعة
        const quickData = [
            {
                id: 1,
                name: 'فلتر هواء تويوتا كامري',
                description: 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020',
                partNumber: 'TOY-AF-001',
                category: 'فلاتر',
                brand: 'تويوتا',
                model: 'كامري',
                shopDetails: [{
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    shopId: 1,
                    price: 85.0,
                    quantity: 15
                }]
            },
            {
                id: 2,
                name: 'تيل فرامل هوندا أكورد',
                description: 'تيل فرامل عالي الجودة لسيارة هوندا أكورد',
                partNumber: 'HON-BP-002',
                category: 'فرامل',
                brand: 'هوندا',
                model: 'أكورد',
                shopDetails: [{
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    shopId: 1,
                    price: 120.0,
                    quantity: 8
                }]
            },
            {
                id: 3,
                name: 'زيت محرك موبيل 1',
                description: 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات',
                partNumber: 'MOB-OIL-003',
                category: 'زيوت',
                brand: 'موبيل',
                model: '',
                shopDetails: [{
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    shopId: 1,
                    price: 95.0,
                    quantity: 25
                }]
            },
            {
                id: 4,
                name: 'بطارية نيسان التيما',
                description: 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير',
                partNumber: 'NIS-BAT-004',
                category: 'كهرباء',
                brand: 'نيسان',
                model: 'التيما',
                shopDetails: [{
                    shopName: 'محل الأصيل لقطع غيار السيارات',
                    shopId: 1,
                    price: 280.0,
                    quantity: 5
                }]
            }
        ];
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
            `;
            
            container.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // Load data
        function loadData() {
            const tableBody = document.getElementById('productsTableBody');
            tableBody.innerHTML = '';
            
            quickData.forEach(product => {
                const prices = product.shopDetails.map(shop => shop.price);
                const minPrice = Math.min(...prices);
                const totalQuantity = product.shopDetails.reduce((sum, shop) => sum + shop.quantity, 0);
                
                const shopsList = product.shopDetails.map(shopDetail => {
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 border rounded bg-light">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary" style="font-size: 0.9rem;">${shopDetail.shopName}</div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <span class="badge bg-success">${shopDetail.price} ريال</span>
                                    <span class="badge bg-info">الكمية: ${shopDetail.quantity}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                </div>
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.category ? `<br><span class="badge bg-secondary badge-sm">${product.category}</span>` : ''}
                                    ${product.brand ? `<span class="badge bg-info badge-sm ms-1">${product.brand}</span>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="text-primary">${product.partNumber || '-'}</code>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${shopsList}
                            </div>
                            <small class="text-muted">
                                ${product.shopDetails.length} محل | 
                                إجمالي الكمية: <span class="fw-bold">${totalQuantity}</span>
                            </small>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="fw-bold text-success fs-6">${minPrice} ريال</span>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
            
            showNotification('تم تحميل البيانات بنجاح!', 'success');
        }
        
        // Test functions
        function testAddShop() {
            showNotification('تم إضافة محل "محل الاختبار الجديد" بنجاح! (محاكاة)', 'success');
            setTimeout(() => {
                showNotification('بيانات تسجيل الدخول: testshop / test123', 'info');
            }, 1000);
        }
        
        function testAddProduct() {
            showNotification('تم إضافة منتج "منتج تجريبي جديد" بنجاح! (محاكاة)', 'success');
        }
        
        function testLogin() {
            showNotification('تم تسجيل الدخول بنجاح! (محاكاة)', 'success');
            setTimeout(() => {
                showNotification('مرحباً بك في لوحة التحكم!', 'info');
            }, 1000);
        }
        
        function showProductDetail(productId) {
            const product = quickData.find(p => p.id === productId);
            if (product) {
                showNotification(`عرض تفاصيل: ${product.name}`, 'info');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            showNotification('مرحباً بك في النسخة السريعة من تطبيق قطع غيار السيارات!', 'success');
        });
    </script>
</body>
</html>
