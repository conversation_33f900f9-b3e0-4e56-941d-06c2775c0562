#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق قطع غيار السيارات
Quick Start for Auto Parts System
"""

import os
import sys
import webbrowser
import time
from threading import Timer

def open_browser():
    """فتح المتصفح تلقائياً بعد 3 ثوان"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح تلقائياً")
    except:
        print("⚠️ لم يتم فتح المتصفح تلقائياً، افتحه يدوياً على: http://localhost:5000")

def main():
    print("🚗 تطبيق ربط محلات قطع غيار السيارات")
    print("=" * 50)
    print()
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['app.py', 'requirements.txt']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        print("تأكد من وجود جميع ملفات التطبيق")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الملفات موجودة")
    print()
    
    # تشغيل المتصفح في خيط منفصل
    browser_timer = Timer(3.0, open_browser)
    browser_timer.start()
    
    print("🚀 جاري تشغيل التطبيق...")
    print("🌐 سيتم فتح المتصفح تلقائياً على: http://localhost:5000")
    print()
    print("👤 الحسابات المتوفرة:")
    print("   🔑 المدير: admin / admin123")
    print("   🏪 محل تجريبي: shop1 / shop123")
    print()
    print("🛑 لإيقاف التطبيق: اضغط Ctrl+C")
    print("=" * 50)
    print()
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من تثبيت المتطلبات: python -m pip install -r requirements.txt")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ المنفذ 5000 مستخدم بالفعل")
            print("أغلق التطبيق الآخر أو استخدم منفذ مختلف")
        else:
            print(f"❌ خطأ في الشبكة: {e}")
            
    except KeyboardInterrupt:
        print("\n\n✅ تم إيقاف التطبيق بنجاح")
        print("شكراً لاستخدام تطبيق ربط محلات قطع غيار السيارات! 🚗")
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("راجع ملف README.md للمساعدة")
    
    finally:
        browser_timer.cancel()  # إلغاء مؤقت المتصفح
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
