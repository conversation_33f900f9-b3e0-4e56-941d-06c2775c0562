<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
        
        .alert-warning {
            border-right: 4px solid #ffc107;
        }
        
        .alert-info {
            border-right: 4px solid #0dcaf0;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-car me-2 text-primary"></i>
                قطع غيار السيارات
            </a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4 fw-bold mb-4">
                        🚗 تطبيق ربط محلات قطع غيار السيارات
                    </h1>
                    <p class="lead mb-4">
                        منصة شاملة لربط محلات قطع غيار السيارات مع العملاء
                    </p>
                    <div class="alert alert-warning d-inline-block">
                        <h5 class="mb-2">⚠️ ملاحظة مهمة</h5>
                        <p class="mb-0">هذه نسخة ثابتة من التطبيق. لتشغيل التطبيق الكامل، استخدم الملفات التنفيذية المرفقة.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Status Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                حالة التطبيق
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <h5><i class="fas fa-check-circle me-2"></i>تم إنشاء التطبيق بنجاح</h5>
                                        <ul class="mb-0">
                                            <li>✅ جميع الملفات موجودة</li>
                                            <li>✅ قاعدة البيانات جاهزة</li>
                                            <li>✅ الواجهات مكتملة</li>
                                            <li>✅ النظام جاهز للتشغيل</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-warning">
                                        <h5><i class="fas fa-exclamation-triangle me-2"></i>لتشغيل التطبيق</h5>
                                        <ol class="mb-0">
                                            <li>تأكد من تثبيت Python</li>
                                            <li>شغل ملف <code>تشخيص_المشكلة.bat</code></li>
                                            <li>أو شغل <code>تثبيت_وتشغيل.bat</code></li>
                                            <li>أو استخدم <code>python app.py</code></li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Accounts Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-4">
                    <h2 class="fw-bold">الحسابات الافتراضية</h2>
                    <p class="text-muted">استخدم هذه الحسابات عند تشغيل التطبيق</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-5 mb-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-user-shield feature-icon"></i>
                            <h4>حساب المدير</h4>
                            <hr class="bg-white">
                            <p class="mb-1"><strong>اسم المستخدم:</strong> admin</p>
                            <p class="mb-1"><strong>كلمة المرور:</strong> admin123</p>
                            <p class="mb-0"><small>إدارة كاملة للنظام</small></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-5 mb-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-store feature-icon"></i>
                            <h4>محل تجريبي</h4>
                            <hr class="bg-white">
                            <p class="mb-1"><strong>اسم المستخدم:</strong> shop1</p>
                            <p class="mb-1"><strong>كلمة المرور:</strong> shop123</p>
                            <p class="mb-0"><small>إدارة منتجات المحل</small></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="fw-bold">ميزات التطبيق</h2>
                    <p class="text-muted">جميع الميزات المطلوبة متوفرة في التطبيق</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-home feature-icon text-primary"></i>
                    <h4>صفحة رئيسية</h4>
                    <p class="text-muted">تعرض أحدث قطع الغيار مع تصميم جذاب</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-search feature-icon text-success"></i>
                    <h4>بحث متقدم</h4>
                    <p class="text-muted">بحث حسب الماركة والموديل ورقم القطعة</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-star feature-icon text-warning"></i>
                    <h4>نظام تقييم</h4>
                    <p class="text-muted">تقييم المحلات والمنتجات من العملاء</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-tachometer-alt feature-icon text-info"></i>
                    <h4>لوحة تحكم المحلات</h4>
                    <p class="text-muted">إدارة شاملة للمنتجات والأسعار</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-users-cog feature-icon text-danger"></i>
                    <h4>لوحة تحكم المدير</h4>
                    <p class="text-muted">إدارة المحلات والمستخدمين</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <i class="fas fa-phone feature-icon text-secondary"></i>
                    <h4>تواصل مباشر</h4>
                    <p class="text-muted">اتصال مباشر مع المحلات</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Instructions Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-rocket me-2"></i>
                                تعليمات التشغيل
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-primary">الطريقة الأولى (الأسهل):</h5>
                                    <ol>
                                        <li>انقر على <code>تثبيت_وتشغيل.bat</code></li>
                                        <li>انتظر حتى يكتمل التثبيت</li>
                                        <li>افتح المتصفح على localhost:5000</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-success">الطريقة الثانية:</h5>
                                    <ol>
                                        <li>شغل <code>تشخيص_المشكلة.bat</code></li>
                                        <li>اتبع التعليمات المعروضة</li>
                                        <li>شغل <code>python app.py</code></li>
                                    </ol>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <h6 class="text-muted">الملفات المتوفرة:</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small>
                                            📄 app.py<br>
                                            📄 run.py<br>
                                            📄 requirements.txt<br>
                                            📄 README.md
                                        </small>
                                    </div>
                                    <div class="col-6">
                                        <small>
                                            📁 templates/<br>
                                            📁 static/<br>
                                            🔧 تشخيص_المشكلة.bat<br>
                                            🚀 تثبيت_وتشغيل.bat
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-2">
                <i class="fas fa-car me-2"></i>
                تطبيق ربط محلات قطع غيار السيارات
            </p>
            <p class="mb-0 text-muted">
                تم تطوير هذا التطبيق بواسطة Augment Agent 🤖
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
