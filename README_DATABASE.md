# 🚗 تطبيق قطع غيار السيارات مع قاعدة البيانات

## 📋 نظرة عامة
تطبيق ويب متكامل لإدارة محلات قطع غيار السيارات مع قاعدة بيانات SQLite حقيقية لحفظ البيانات بشكل دائم.

## ✨ الميزات الجديدة
- 🗄️ **قاعدة بيانات SQLite** - حفظ دائم للبيانات
- 🔄 **Flask API** - واجهة برمجة تطبيقات قوية
- 💾 **حفظ تلقائي** - جميع التغييرات محفوظة فوراً
- 🔒 **أمان البيانات** - لا فقدان للبيانات عند إعادة التشغيل

## 🛠️ متطلبات التشغيل
- Python 3.7 أو أحدث
- Flask
- Flask-CORS
- SQLite (مدمج مع Python)

## 🚀 طريقة التشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python start_app.py
```

### 3. فتح التطبيق
افتح المتصفح واذهب إلى:
- **التطبيق الرئيسي**: http://localhost:5000/static/app.html
- **الصفحة الرئيسية**: http://localhost:5000
- **API المنتجات**: http://localhost:5000/api/products
- **API المحلات**: http://localhost:5000/api/shops

## 👤 حسابات تجريبية
- **المدير**: admin / admin123
- **المحل**: shop1 / shop123

## 📁 هيكل المشروع
```
sales_system_project/
├── database.py          # إدارة قاعدة البيانات
├── api.py              # Flask API
├── start_app.py        # ملف التشغيل
├── requirements.txt    # المتطلبات
├── static/
│   └── app.html       # التطبيق الرئيسي
├── auto_parts.db      # قاعدة البيانات (تُنشأ تلقائياً)
└── README_DATABASE.md # هذا الملف
```

## 🗄️ قاعدة البيانات

### الجداول
1. **users** - المستخدمين (مدير ومحلات)
2. **shops** - بيانات المحلات
3. **products** - المنتجات
4. **shop_products** - ربط المنتجات بالمحلات مع الأسعار والكميات

### البيانات الأولية
- مدير النظام
- محل تجريبي مع 4 منتجات

## 🔗 API Endpoints

### المستخدمين
- `POST /api/login` - تسجيل الدخول

### المحلات
- `GET /api/shops` - جميع المحلات
- `POST /api/shops` - إضافة محل جديد
- `PUT /api/shops/<id>` - تحديث محل
- `DELETE /api/shops/<id>` - حذف محل

### المنتجات
- `GET /api/products` - جميع المنتجات مع تفاصيل المحلات
- `POST /api/products` - إضافة منتج جديد

## 🎯 الاختلافات عن النسخة الثابتة
- ✅ **حفظ دائم** - البيانات لا تضيع عند إعادة التشغيل
- ✅ **قاعدة بيانات حقيقية** - SQLite بدلاً من JavaScript
- ✅ **API متكامل** - واجهة برمجة تطبيقات RESTful
- ✅ **أمان أفضل** - تشفير كلمات المرور
- ✅ **أداء محسن** - استعلامات قاعدة بيانات محسنة

## 🧪 اختبار التطبيق

### 1. اختبار إضافة محل جديد
1. سجل دخول كمدير (admin / admin123)
2. أضف محل جديد من API أو التطبيق
3. تحقق من حفظ البيانات في قاعدة البيانات

### 2. اختبار إضافة منتج
1. سجل دخول كصاحب محل
2. أضف منتج جديد
3. تحقق من ظهوره في قائمة المنتجات

### 3. اختبار الحفظ الدائم
1. أضف بيانات جديدة
2. أوقف الخادم (Ctrl+C)
3. شغل الخادم مرة أخرى
4. تحقق من وجود البيانات

## 🔧 استكشاف الأخطاء

### خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm auto_parts.db
python start_app.py
```

### خطأ في المتطلبات
```bash
# إعادة تثبيت المتطلبات
pip install --upgrade -r requirements.txt
```

### خطأ في المنفذ
```bash
# تغيير المنفذ في api.py
app.run(debug=True, host='0.0.0.0', port=5001)
```

## 📊 مراقبة قاعدة البيانات
يمكنك فتح قاعدة البيانات باستخدام أي أداة SQLite مثل:
- DB Browser for SQLite
- SQLite Studio
- أو من سطر الأوامر: `sqlite3 auto_parts.db`

## 🎉 الخلاصة
الآن لديك تطبيق قطع غيار السيارات مع قاعدة بيانات حقيقية!
جميع البيانات محفوظة بشكل دائم ولن تضيع عند إعادة التشغيل.

---
**تم إنشاؤه بواسطة Augment Agent** 🤖
