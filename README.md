# 🚗 تطبيق ربط محلات قطع غيار السيارات
## Auto Parts Shops Connection System

تطبيق ويب متكامل لربط محلات قطع غيار السيارات مع العملاء، متوفر بعدة إصدارات للتوافق مع جميع البيئات.

## ✨ الميزات الرئيسية

### 🏠 للعملاء
- **صفحة رئيسية** تعرض أحدث قطع الغيار
- **بحث متقدم** حسب الماركة، الموديل، ورقم القطعة
- **عرض المحلات** مع التقييمات والمعلومات
- **نظام تقييم** للمحلات والمنتجات
- **تواصل مباشر** مع المحلات عبر الهاتف

### 🏪 للمحلات
- **لوحة تحكم شاملة** لإدارة المحل
- **إدارة المنتجات** (إضافة، تعديل، حذف)
- **إدارة الأسعار والكميات** لكل منتج
- **تحديث بيانات المحل** والمعلومات
- **عرض الإحصائيات** والتقييمات

### 👨‍💼 للمدير
- **لوحة تحكم المدير** الشاملة
- **إدارة المحلات** (إضافة، تعديل، حذف)
- **إدارة المستخدمين** وصلاحياتهم
- **إدارة الفئات والماركات**
- **مراقبة النظام** والإحصائيات

## 🚀 طرق التشغيل المتوفرة

### 🎯 الطريقة الأولى: التشغيل الشامل (الأسهل)
انقر نقراً مزدوجاً على ملف **`تشغيل_شامل.bat`**
- يفحص النظام تلقائياً
- يختار أفضل طريقة تشغيل
- يعمل مع أو بدون Python

### 🐍 الطريقة الثانية: Python (إذا كان مثبتاً)
```bash
# الخادم البسيط (بدون مكتبات خارجية)
python simple_server.py

# أو التطبيق الكامل (يحتاج Flask)
pip install -r requirements.txt
python app.py
```

### 📱 الطريقة الثالثة: التطبيق الثابت (يعمل دائماً)
انقر نقراً مزدوجاً على ملف **`تطبيق_ويب_ثابت.html`**
- يعمل بدون Python
- جميع الميزات متوفرة
- تسجيل دخول تفاعلي

## 🔐 الحسابات الافتراضية

### حساب المدير
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### إضافة محلات جديدة
يمكن للمدير إضافة محلات جديدة من لوحة التحكم

## 📱 الوصول للتطبيق

بعد تشغيل التطبيق، يمكنك الوصول إليه عبر:
- **محلياً:** http://localhost:5000
- **الشبكة المحلية:** http://[عنوان-IP]:5000

## 🗂️ هيكل المشروع

```
sales_system_project/
├── app.py                 # الملف الرئيسي للتطبيق
├── run.py                 # ملف تشغيل التطبيق
├── start_app.bat          # ملف تنفيذي للويندوز
├── requirements.txt       # متطلبات Python
├── README.md             # هذا الملف
├── auto_parts.db         # قاعدة البيانات (تُنشأ تلقائياً)
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── login.html
│   ├── search.html
│   ├── shops.html
│   ├── shop_products.html
│   ├── admin/           # قوالب المدير
│   │   ├── dashboard.html
│   │   └── add_shop.html
│   └── shop/            # قوالب المحلات
│       ├── dashboard.html
│       ├── products.html
│       ├── add_product.html
│       └── edit_product.html
└── static/              # الملفات الثابتة
    ├── css/
    ├── js/
    └── uploads/         # مجلد رفع الصور
```

## 🛠️ التقنيات المستخدمة

- **Backend:** Python Flask
- **Database:** SQLite
- **Frontend:** HTML5, CSS3, JavaScript
- **UI Framework:** Bootstrap 5
- **Icons:** Font Awesome
- **Fonts:** Google Fonts (Cairo)

## 📋 المتطلبات

- Python 3.7 أو أحدث
- Flask 2.3.3
- Flask-SQLAlchemy 3.0.5
- Werkzeug 2.3.7

## 🔧 إعدادات إضافية

### تغيير المنفذ (Port)
لتغيير منفذ التطبيق، عدّل الملف `run.py`:
```python
app.run(debug=True, host='0.0.0.0', port=8080)  # غيّر 5000 إلى المنفذ المطلوب
```

### إعداد قاعدة البيانات
قاعدة البيانات تُنشأ تلقائياً عند أول تشغيل مع:
- حساب المدير الافتراضي
- فئات المنتجات الأساسية
- ماركات السيارات الشائعة

## 🐛 استكشاف الأخطاء

### خطأ في تثبيت المتطلبات
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### خطأ في الوصول للتطبيق
- تأكد من أن المنفذ 5000 غير مستخدم
- جرب الوصول عبر 127.0.0.1:5000 بدلاً من localhost

### مشاكل في قاعدة البيانات
احذف ملف `auto_parts.db` وأعد تشغيل التطبيق لإنشاء قاعدة بيانات جديدة

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملف `README.md`
- راجع رسائل الخطأ في وحدة التحكم
- تأكد من تثبيت جميع المتطلبات بشكل صحيح

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

---

**تم تطوير هذا التطبيق بواسطة Augment Agent** 🤖
