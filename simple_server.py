#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب بسيط لتطبيق قطع غيار السيارات
باستخدام مكتبات Python الأساسية فقط
"""

import http.server
import socketserver
import urllib.parse
import json
import os
import webbrowser
import threading
import time

# إعدادات الخادم
PORT = 5000
HOST = 'localhost'

# بيانات تجريبية
users = {
    'admin': {'password': 'admin123', 'role': 'admin', 'name': 'المدير'},
    'shop1': {'password': 'shop123', 'role': 'shop', 'name': 'محل الأصيل'}
}

products = [
    {
        'id': 1,
        'name': 'فلتر هواء تويوتا كامري',
        'description': 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020',
        'price': 85.0,
        'quantity': 15,
        'part_number': 'TOY-AF-001',
        'shop': 'محل الأصيل'
    },
    {
        'id': 2,
        'name': 'تيل فرامل هوندا أكورد',
        'description': 'تيل فرامل عالي الجودة لسيارة هوندا أكورد',
        'price': 120.0,
        'quantity': 8,
        'part_number': 'HON-BP-002',
        'shop': 'محل الأصيل'
    },
    {
        'id': 3,
        'name': 'زيت محرك موبيل 1',
        'description': 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات',
        'price': 95.0,
        'quantity': 25,
        'part_number': 'MOB-OIL-003',
        'shop': 'محل الأصيل'
    },
    {
        'id': 4,
        'name': 'بطارية نيسان التيما',
        'description': 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير',
        'price': 280.0,
        'quantity': 5,
        'part_number': 'NIS-BAT-004',
        'shop': 'محل الأصيل'
    }
]

class AutoPartsHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_home_page()
        elif self.path == '/api/products':
            self.send_json_response(products)
        elif self.path == '/login':
            self.send_login_page()
        elif self.path == '/admin':
            self.send_admin_page()
        elif self.path == '/shop':
            self.send_shop_page()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/api/login':
            self.handle_login()
        else:
            self.send_error(404)
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def handle_login(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = urllib.parse.parse_qs(post_data.decode('utf-8'))
        
        username = data.get('username', [''])[0]
        password = data.get('password', [''])[0]
        
        if username in users and users[username]['password'] == password:
            response = {'success': True, 'role': users[username]['role'], 'name': users[username]['name']}
        else:
            response = {'success': False, 'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'}
        
        self.send_json_response(response)
    
    def send_home_page(self):
        html = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق ربط محلات قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 60px 0; }
        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.3s; }
        .card:hover { transform: translateY(-5px); }
        .product-image { height: 150px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-car me-2 text-primary"></i>
                قطع غيار السيارات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/login">تسجيل الدخول</a>
            </div>
        </div>
    </nav>

    <section class="hero text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
            <p class="lead mb-4">يعمل بنجاح باستخدام Python!</p>
            <div class="alert alert-success d-inline-block">
                <h5><i class="fas fa-check-circle me-2"></i>الخادم يعمل بنجاح!</h5>
                <p class="mb-0">Python HTTP Server على المنفذ ''' + str(PORT) + '''</p>
            </div>
        </div>
    </section>

    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">أحدث القطع</h2>
            <div class="row" id="productsContainer">
                <!-- Products will be loaded here -->
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load products
        fetch('/api/products')
            .then(response => response.json())
            .then(products => {
                const container = document.getElementById('productsContainer');
                products.forEach(product => {
                    const productCard = `
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <div class="product-image">
                                    <i class="fas fa-cog fa-3x text-muted"></i>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">${product.name}</h6>
                                    <p class="card-text text-muted small">${product.description.substring(0, 50)}...</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold text-primary">${product.price} ريال</span>
                                        <small class="text-muted">${product.shop}</small>
                                    </div>
                                    <small class="text-muted d-block mt-1">رقم القطعة: ${product.part_number}</small>
                                    <div class="mt-2">
                                        <span class="badge bg-success">متوفر (${product.quantity})</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += productCard;
                });
            })
            .catch(error => {
                console.error('Error loading products:', error);
                document.getElementById('productsContainer').innerHTML = 
                    '<div class="col-12 text-center"><p class="text-muted">خطأ في تحميل المنتجات</p></div>';
            });
    </script>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_login_page(self):
        html = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <h3 class="text-center mb-4">تسجيل الدخول</h3>
                        <div id="alert" class="alert" style="display: none;"></div>
                        <form id="loginForm">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
                        </form>
                        <div class="mt-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>الحسابات المتوفرة:</h6>
                                    <p class="mb-1"><strong>المدير:</strong> admin / admin123</p>
                                    <p class="mb-0"><strong>المحل:</strong> shop1 / shop123</p>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <a href="/" class="btn btn-outline-secondary">العودة للرئيسية</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertDiv = document.getElementById('alert');
            
            fetch('/api/login', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `username=${username}&password=${password}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alertDiv.className = 'alert alert-success';
                    alertDiv.textContent = 'تم تسجيل الدخول بنجاح!';
                    alertDiv.style.display = 'block';
                    
                    setTimeout(() => {
                        if (data.role === 'admin') {
                            window.location.href = '/admin';
                        } else {
                            window.location.href = '/shop';
                        }
                    }, 1000);
                } else {
                    alertDiv.className = 'alert alert-danger';
                    alertDiv.textContent = data.message;
                    alertDiv.style.display = 'block';
                }
            })
            .catch(error => {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = 'خطأ في الاتصال';
                alertDiv.style.display = 'block';
            });
        });
    </script>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

def open_browser():
    """فتح المتصفح بعد 2 ثانية"""
    time.sleep(2)
    try:
        webbrowser.open(f'http://{HOST}:{PORT}')
        print(f"🌐 تم فتح المتصفح على: http://{HOST}:{PORT}")
    except:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً، افتحه يدوياً على: http://{HOST}:{PORT}")

def main():
    print("🚗 تطبيق ربط محلات قطع غيار السيارات")
    print("=" * 50)
    print("🐍 يعمل باستخدام Python HTTP Server")
    print(f"🌐 الرابط: http://{HOST}:{PORT}")
    print("👤 المدير: admin / admin123")
    print("🏪 المحل: shop1 / shop123")
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 50)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer((HOST, PORT), AutoPartsHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الخادم بنجاح")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("جرب منفذ آخر أو أغلق التطبيق الذي يستخدم المنفذ")
        else:
            print(f"❌ خطأ في الشبكة: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == '__main__':
    main()
