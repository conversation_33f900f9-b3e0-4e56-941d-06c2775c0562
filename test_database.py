#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات
Database Test
"""

import sqlite3
import json

def create_test_database():
    """إنشاء قاعدة بيانات تجريبية"""
    print("🗄️ إنشاء قاعدة البيانات...")
    
    conn = sqlite3.connect('auto_parts_simple.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # حذف الجداول الموجودة
    cursor.execute('DROP TABLE IF EXISTS shop_products')
    cursor.execute('DROP TABLE IF EXISTS products')
    cursor.execute('DROP TABLE IF EXISTS shops')
    cursor.execute('DROP TABLE IF EXISTS users')
    
    # إنشاء الجداول
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            name TEXT NOT NULL,
            shop_id INTEGER
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE shops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT NOT NULL,
            phone TEXT NOT NULL,
            email TEXT,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            specialty TEXT,
            rating REAL DEFAULT 0.0
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            part_number TEXT,
            category TEXT,
            brand TEXT,
            model TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE shop_products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shop_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 0,
            UNIQUE(shop_id, product_id)
        )
    ''')
    
    # إدراج البيانات
    print("📝 إدراج البيانات...")
    
    # المدير
    cursor.execute('''
        INSERT INTO users (username, password, role, name)
        VALUES (?, ?, ?, ?)
    ''', ('admin', 'admin123', 'admin', 'المدير'))
    
    # المحل
    cursor.execute('''
        INSERT INTO shops (name, description, phone, email, address, city, specialty, rating)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        'محل الأصيل لقطع غيار السيارات',
        'محل متخصص في قطع غيار السيارات اليابانية والكورية',
        '+966501234567',
        '<EMAIL>',
        'شارع الملك فهد، الرياض',
        'الرياض',
        'قطع غيار يابانية',
        4.5
    ))
    
    shop_id = cursor.lastrowid
    
    # مستخدم المحل
    cursor.execute('''
        INSERT INTO users (username, password, role, name, shop_id)
        VALUES (?, ?, ?, ?, ?)
    ''', ('shop1', 'shop123', 'shop', 'محل الأصيل لقطع غيار السيارات', shop_id))
    
    # المنتجات
    products_data = [
        ('فلتر هواء تويوتا كامري', 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020', 'TOY-AF-001', 'فلاتر', 'تويوتا', 'كامري'),
        ('تيل فرامل هوندا أكورد', 'تيل فرامل عالي الجودة لسيارة هوندا أكورد', 'HON-BP-002', 'فرامل', 'هوندا', 'أكورد'),
        ('زيت محرك موبيل 1', 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات', 'MOB-OIL-003', 'زيوت', 'موبيل', ''),
        ('بطارية نيسان التيما', 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير', 'NIS-BAT-004', 'كهرباء', 'نيسان', 'التيما')
    ]
    
    prices = [85.0, 120.0, 95.0, 280.0]
    quantities = [15, 8, 25, 5]
    
    for i, product_data in enumerate(products_data):
        cursor.execute('''
            INSERT INTO products (name, description, part_number, category, brand, model)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', product_data)
        
        product_id = cursor.lastrowid
        
        cursor.execute('''
            INSERT INTO shop_products (shop_id, product_id, price, quantity)
            VALUES (?, ?, ?, ?)
        ''', (shop_id, product_id, prices[i], quantities[i]))
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🧪 اختبار قاعدة البيانات...")
    
    conn = sqlite3.connect('auto_parts_simple.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # اختبار المستخدمين
    cursor.execute('SELECT * FROM users')
    users = cursor.fetchall()
    print(f"👤 المستخدمين: {len(users)}")
    for user in users:
        print(f"  - {user['username']} ({user['role']})")
    
    # اختبار المحلات
    cursor.execute('SELECT * FROM shops')
    shops = cursor.fetchall()
    print(f"🏪 المحلات: {len(shops)}")
    for shop in shops:
        print(f"  - {shop['name']}")
    
    # اختبار المنتجات
    cursor.execute('''
        SELECT p.*, sp.price, sp.quantity, s.name as shop_name
        FROM products p
        JOIN shop_products sp ON p.id = sp.product_id
        JOIN shops s ON sp.shop_id = s.id
    ''')
    products = cursor.fetchall()
    print(f"📦 المنتجات: {len(products)}")
    for product in products:
        print(f"  - {product['name']}: {product['price']} ريال (الكمية: {product['quantity']}) - {product['shop_name']}")
    
    conn.close()

def create_json_data():
    """إنشاء ملف JSON للبيانات"""
    print("\n📄 إنشاء ملف JSON...")
    
    conn = sqlite3.connect('auto_parts_simple.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # جمع البيانات
    cursor.execute('''
        SELECT p.*, sp.price, sp.quantity, s.name as shop_name, s.id as shop_id
        FROM products p
        JOIN shop_products sp ON p.id = sp.product_id
        JOIN shops s ON sp.shop_id = s.id
        ORDER BY p.id, s.name
    ''')
    
    products_dict = {}
    for row in cursor.fetchall():
        product_id = row['id']
        if product_id not in products_dict:
            products_dict[product_id] = {
                'id': row['id'],
                'name': row['name'],
                'description': row['description'],
                'partNumber': row['part_number'],
                'category': row['category'],
                'brand': row['brand'],
                'model': row['model'],
                'shopDetails': []
            }
        
        products_dict[product_id]['shopDetails'].append({
            'shopName': row['shop_name'],
            'shopId': row['shop_id'],
            'price': row['price'],
            'quantity': row['quantity']
        })
    
    products = list(products_dict.values())
    
    # حفظ في ملف JSON
    with open('products_data.json', 'w', encoding='utf-8') as f:
        json.dump(products, f, ensure_ascii=False, indent=2)
    
    print(f"✅ تم حفظ {len(products)} منتج في ملف products_data.json")
    
    conn.close()

def main():
    print("=" * 60)
    print("🚗 اختبار قاعدة بيانات تطبيق قطع غيار السيارات")
    print("=" * 60)
    
    try:
        create_test_database()
        test_database()
        create_json_data()
        
        print("\n" + "=" * 60)
        print("✅ تم إنشاء واختبار قاعدة البيانات بنجاح!")
        print("📁 الملفات المنشأة:")
        print("  - auto_parts_simple.db (قاعدة البيانات)")
        print("  - products_data.json (بيانات JSON)")
        print("\n🚀 يمكنك الآن تشغيل التطبيق باستخدام:")
        print("  - تشغيل_سريع.bat")
        print("  - python fast_api.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
    input("\nاضغط Enter للخروج...")
