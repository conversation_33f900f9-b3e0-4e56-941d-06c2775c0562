@echo off
chcp 65001 >nul
title تطبيق قطع غيار السيارات - تشغيل شامل

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █  🚗 تطبيق ربط محلات قطع غيار السيارات                      █
echo █     تشغيل شامل - جميع الخيارات المتوفرة                    █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 فحص الخيارات المتوفرة...
echo.

REM فحص Python
set PYTHON_AVAILABLE=0
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python متوفر
    set PYTHON_AVAILABLE=1
    set PYTHON_CMD=python
) else (
    py --version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Python متوفر (py)
        set PYTHON_AVAILABLE=1
        set PYTHON_CMD=py
    ) else (
        echo ❌ Python غير متوفر
    )
)

REM فحص التطبيق الثابت
if exist "تطبيق_ويب_ثابت.html" (
    echo ✅ التطبيق الثابت متوفر
    set STATIC_AVAILABLE=1
) else (
    echo ❌ التطبيق الثابت غير متوفر
    set STATIC_AVAILABLE=0
)

echo.
echo ████████████████████████████████████████████████████████████████
echo █                      اختر طريقة التشغيل                     █
echo ████████████████████████████████████████████████████████████████
echo.

if %PYTHON_AVAILABLE%==1 (
    echo 1. 🐍 تشغيل بـ Python ^(خادم HTTP بسيط^)
)
if %STATIC_AVAILABLE%==1 (
    echo 2. 📱 تشغيل التطبيق الثابت ^(يعمل بدون Python^)
)
echo 3. 🔧 تشخيص المشاكل
echo 4. ❌ خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" (
    if %PYTHON_AVAILABLE%==1 (
        goto :run_python
    ) else (
        echo ❌ Python غير متوفر
        pause
        goto :menu
    )
)

if "%choice%"=="2" (
    if %STATIC_AVAILABLE%==1 (
        goto :run_static
    ) else (
        echo ❌ التطبيق الثابت غير متوفر
        pause
        goto :menu
    )
)

if "%choice%"=="3" (
    goto :diagnose
)

if "%choice%"=="4" (
    exit /b 0
)

echo ❌ خيار غير صحيح
pause
goto :menu

:run_python
cls
echo.
echo 🐍 تشغيل التطبيق بـ Python...
echo.
echo 🌐 الرابط: http://localhost:5000
echo 👤 المدير: admin / admin123
echo 🏪 المحل: shop1 / shop123
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo.

%PYTHON_CMD% simple_server.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل الخادم
    echo 🔄 جاري التبديل للتطبيق الثابت...
    timeout /t 3 >nul
    goto :run_static
)

goto :end

:run_static
cls
echo.
echo 📱 تشغيل التطبيق الثابت...
echo.

start "" "تطبيق_ويب_ثابت.html"

echo ✅ تم فتح التطبيق الثابت في المتصفح
echo.
echo 📋 الميزات المتوفرة:
echo    ✅ جميع الصفحات تعمل
echo    ✅ تسجيل دخول تفاعلي
echo    ✅ لوحات تحكم كاملة
echo    ✅ عرض المنتجات
echo    ✅ بحث متقدم
echo.
echo 🔐 الحسابات:
echo    👤 المدير: admin / admin123
echo    🏪 المحل: shop1 / shop123
echo.
goto :end

:diagnose
cls
echo.
echo 🔧 تشخيص المشاكل...
echo.

echo 🔍 فحص Python:
python --version 2>nul
if errorlevel 1 (
    echo ❌ python غير متوفر
    py --version 2>nul
    if errorlevel 1 (
        echo ❌ py غير متوفر
        echo.
        echo 📥 لتثبيت Python:
        echo    1. اذهب إلى: https://python.org/downloads
        echo    2. حمل أحدث إصدار
        echo    3. تأكد من تحديد "Add Python to PATH"
        echo    4. أعد تشغيل الكمبيوتر
    ) else (
        echo ✅ py متوفر
    )
) else (
    echo ✅ python متوفر
)

echo.
echo 🔍 فحص الملفات:
if exist "simple_server.py" (
    echo ✅ simple_server.py موجود
) else (
    echo ❌ simple_server.py مفقود
)

if exist "تطبيق_ويب_ثابت.html" (
    echo ✅ تطبيق_ويب_ثابت.html موجود
) else (
    echo ❌ تطبيق_ويب_ثابت.html مفقود
)

echo.
echo 🔍 فحص المنفذ 5000:
netstat -an | findstr :5000 >nul
if not errorlevel 1 (
    echo ⚠️ المنفذ 5000 مستخدم
) else (
    echo ✅ المنفذ 5000 متاح
)

echo.
pause
goto :menu

:end
echo.
echo ✅ انتهى التشغيل
pause

:menu
goto :start
