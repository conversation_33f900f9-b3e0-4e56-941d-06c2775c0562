#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API لتطبيق قطع غيار السيارات
API module for Auto Parts Shop System
"""

from flask import Flask, request, jsonify, render_template_string
from database import Database

app = Flask(__name__)

# إضافة CORS headers يدوياً
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# إنشاء مثيل من قاعدة البيانات
db = Database()

# ===== API للمستخدمين =====

@app.route('/api/login', methods=['POST'])
def login():
    """تسجيل الدخول"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': 'اسم المستخدم وكلمة المرور مطلوبان'})
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT u.*, s.name as shop_name 
        FROM users u 
        LEFT JOIN shops s ON u.shop_id = s.id 
        WHERE u.username = ? AND u.password = ?
    ''', (username, password))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        user_data = {
            'id': user['id'],
            'username': user['username'],
            'role': user['role'],
            'name': user['shop_name'] if user['role'] == 'shop' else user['name'],
            'shop_id': user['shop_id']
        }
        return jsonify({'success': True, 'user': user_data})
    else:
        return jsonify({'success': False, 'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'})

# ===== API للمحلات =====

@app.route('/api/shops', methods=['GET'])
def get_shops():
    """الحصول على جميع المحلات"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT s.*, COUNT(sp.product_id) as products_count
        FROM shops s
        LEFT JOIN shop_products sp ON s.id = sp.shop_id
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ''')
    
    shops = []
    for row in cursor.fetchall():
        shop = dict(row)
        shop['joinDate'] = shop['created_at'][:10]  # تاريخ فقط
        shops.append(shop)
    
    conn.close()
    return jsonify(shops)

@app.route('/api/shops', methods=['POST'])
def add_shop():
    """إضافة محل جديد"""
    data = request.get_json()
    
    required_fields = ['username', 'password', 'name', 'description', 'phone', 'address', 'city']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'})
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # التحقق من عدم تكرار اسم المستخدم
        cursor.execute('SELECT id FROM users WHERE username = ?', (data['username'],))
        if cursor.fetchone():
            return jsonify({'success': False, 'message': 'اسم المستخدم موجود بالفعل'})
        
        # التحقق من عدم تكرار اسم المحل
        cursor.execute('SELECT id FROM shops WHERE name = ?', (data['name'],))
        if cursor.fetchone():
            return jsonify({'success': False, 'message': 'اسم المحل موجود بالفعل'})
        
        # إدراج المحل
        cursor.execute('''
            INSERT INTO shops (name, description, phone, email, address, city, specialty, open_time, close_time, website, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['name'], data['description'], data['phone'], data.get('email', ''),
            data['address'], data['city'], data.get('specialty', ''),
            data.get('openTime', ''), data.get('closeTime', ''),
            data.get('website', ''), data.get('notes', '')
        ))
        
        shop_id = cursor.lastrowid
        
        # إدراج المستخدم
        cursor.execute('''
            INSERT INTO users (username, password, role, name, shop_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (data['username'], data['password'], 'shop', data['name'], shop_id))
        
        conn.commit()
        return jsonify({'success': True, 'message': 'تم إضافة المحل بنجاح', 'shop_id': shop_id})
        
    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة المحل: {str(e)}'})
    finally:
        conn.close()

@app.route('/api/shops/<int:shop_id>', methods=['PUT'])
def update_shop(shop_id):
    """تحديث بيانات محل"""
    data = request.get_json()
    
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # تحديث بيانات المحل
        cursor.execute('''
            UPDATE shops SET 
                name = ?, description = ?, phone = ?, email = ?, 
                address = ?, city = ?, specialty = ?, open_time = ?, 
                close_time = ?, website = ?, notes = ?
            WHERE id = ?
        ''', (
            data['name'], data['description'], data['phone'], data.get('email', ''),
            data['address'], data['city'], data.get('specialty', ''),
            data.get('openTime', ''), data.get('closeTime', ''),
            data.get('website', ''), data.get('notes', ''), shop_id
        ))
        
        # تحديث بيانات المستخدم إذا تم تغيير كلمة المرور
        if data.get('password'):
            cursor.execute('''
                UPDATE users SET password = ?, name = ? WHERE shop_id = ?
            ''', (data['password'], data['name'], shop_id))
        else:
            cursor.execute('''
                UPDATE users SET name = ? WHERE shop_id = ?
            ''', (data['name'], shop_id))
        
        conn.commit()
        return jsonify({'success': True, 'message': 'تم تحديث المحل بنجاح'})
        
    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تحديث المحل: {str(e)}'})
    finally:
        conn.close()

@app.route('/api/shops/<int:shop_id>', methods=['DELETE'])
def delete_shop(shop_id):
    """حذف محل"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # حذف المستخدم المرتبط بالمحل
        cursor.execute('DELETE FROM users WHERE shop_id = ?', (shop_id,))
        
        # حذف المحل (سيحذف تلقائياً shop_products بسبب CASCADE)
        cursor.execute('DELETE FROM shops WHERE id = ?', (shop_id,))
        
        conn.commit()
        return jsonify({'success': True, 'message': 'تم حذف المحل بنجاح'})
        
    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في حذف المحل: {str(e)}'})
    finally:
        conn.close()

# ===== API للمنتجات =====

@app.route('/api/products', methods=['GET'])
def get_products():
    """الحصول على جميع المنتجات مع تفاصيل المحلات"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT p.*, sp.price, sp.quantity, s.name as shop_name, s.id as shop_id
        FROM products p
        JOIN shop_products sp ON p.id = sp.product_id
        JOIN shops s ON sp.shop_id = s.id
        ORDER BY p.id, s.name
    ''')
    
    products_dict = {}
    for row in cursor.fetchall():
        product_id = row['id']
        if product_id not in products_dict:
            products_dict[product_id] = {
                'id': row['id'],
                'name': row['name'],
                'description': row['description'],
                'partNumber': row['part_number'],
                'category': row['category'],
                'brand': row['brand'],
                'model': row['model'],
                'image': row['image'],
                'shopDetails': []
            }
        
        products_dict[product_id]['shopDetails'].append({
            'shopName': row['shop_name'],
            'shopId': row['shop_id'],
            'price': row['price'],
            'quantity': row['quantity']
        })
    
    products = list(products_dict.values())
    conn.close()
    return jsonify(products)

@app.route('/api/products', methods=['POST'])
def add_product():
    """إضافة منتج جديد"""
    data = request.get_json()

    if not data.get('name') or not data.get('shopId') or not data.get('price'):
        return jsonify({'success': False, 'message': 'البيانات المطلوبة ناقصة'})

    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # البحث عن منتج موجود بنفس الاسم ورقم القطعة
        cursor.execute('''
            SELECT id FROM products
            WHERE name = ? AND (part_number = ? OR (part_number IS NULL AND ? IS NULL))
        ''', (data['name'], data.get('partNumber'), data.get('partNumber')))

        existing_product = cursor.fetchone()

        if existing_product:
            product_id = existing_product['id']
            # التحقق من عدم وجود المنتج في نفس المحل
            cursor.execute('''
                SELECT id FROM shop_products WHERE shop_id = ? AND product_id = ?
            ''', (data['shopId'], product_id))

            if cursor.fetchone():
                return jsonify({'success': False, 'message': 'المنتج موجود بالفعل في محلك'})

            # إضافة المنتج للمحل
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (data['shopId'], product_id, data['price'], data.get('quantity', 0)))
        else:
            # إنشاء منتج جديد
            cursor.execute('''
                INSERT INTO products (name, description, part_number, category, brand, model, image)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['name'], data.get('description', ''), data.get('partNumber', ''),
                data.get('category', ''), data.get('brand', ''), data.get('model', ''),
                data.get('image', '')
            ))

            product_id = cursor.lastrowid

            # ربط المنتج بالمحل
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (data['shopId'], product_id, data['price'], data.get('quantity', 0)))

        conn.commit()
        return jsonify({'success': True, 'message': 'تم إضافة المنتج بنجاح', 'product_id': product_id})

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة المنتج: {str(e)}'})
    finally:
        conn.close()

@app.route('/api/shop-products/<int:shop_id>', methods=['GET'])
def get_shop_products(shop_id):
    """الحصول على منتجات محل معين"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT p.*, sp.price, sp.quantity, sp.id as shop_product_id
        FROM products p
        JOIN shop_products sp ON p.id = sp.product_id
        WHERE sp.shop_id = ?
        ORDER BY p.name
    ''', (shop_id,))

    products = []
    for row in cursor.fetchall():
        product = {
            'id': row['id'],
            'name': row['name'],
            'description': row['description'],
            'partNumber': row['part_number'],
            'category': row['category'],
            'brand': row['brand'],
            'model': row['model'],
            'price': row['price'],
            'quantity': row['quantity'],
            'shopProductId': row['shop_product_id']
        }
        products.append(product)

    conn.close()
    return jsonify(products)

@app.route('/api/shop-products/<int:shop_product_id>', methods=['PUT'])
def update_shop_product(shop_product_id):
    """تحديث منتج في محل"""
    data = request.get_json()

    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            UPDATE shop_products
            SET price = ?, quantity = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (data['price'], data['quantity'], shop_product_id))

        conn.commit()
        return jsonify({'success': True, 'message': 'تم تحديث المنتج بنجاح'})

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تحديث المنتج: {str(e)}'})
    finally:
        conn.close()

@app.route('/api/shop-products/<int:shop_product_id>', methods=['DELETE'])
def delete_shop_product(shop_product_id):
    """حذف منتج من محل"""
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # الحصول على معرف المنتج قبل الحذف
        cursor.execute('SELECT product_id FROM shop_products WHERE id = ?', (shop_product_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'message': 'المنتج غير موجود'})

        product_id = result['product_id']

        # حذف المنتج من المحل
        cursor.execute('DELETE FROM shop_products WHERE id = ?', (shop_product_id,))

        # التحقق من وجود المنتج في محلات أخرى
        cursor.execute('SELECT COUNT(*) FROM shop_products WHERE product_id = ?', (product_id,))
        count = cursor.fetchone()[0]

        # إذا لم يعد المنتج موجود في أي محل، احذفه نهائياً
        if count == 0:
            cursor.execute('DELETE FROM products WHERE id = ?', (product_id,))

        conn.commit()
        return jsonify({'success': True, 'message': 'تم حذف المنتج بنجاح'})

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'خطأ في حذف المنتج: {str(e)}'})
    finally:
        conn.close()

# ===== الصفحة الرئيسية =====

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تطبيق قطع غيار السيارات - مع قاعدة البيانات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body { font-family: 'Cairo', sans-serif; }
            .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 60px 0; }
        </style>
    </head>
    <body>
        <div class="hero text-center">
            <div class="container">
                <h1 class="display-4 fw-bold mb-4">🚗 تطبيق قطع غيار السيارات</h1>
                <p class="lead mb-4">الآن مع قاعدة بيانات حقيقية!</p>
                <div class="alert alert-success d-inline-block">
                    <h5><i class="fas fa-database me-2"></i>قاعدة البيانات تعمل!</h5>
                    <p class="mb-0">Flask + SQLite على المنفذ 5000</p>
                </div>
                <div class="mt-4">
                    <a href="/static/app.html" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-play me-2"></i>
                        تشغيل التطبيق
                    </a>
                    <a href="/api/products" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-code me-2"></i>
                        API المنتجات
                    </a>
                </div>
            </div>
        </div>
        
        <div class="container py-5">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-database fa-3x text-primary mb-3"></i>
                            <h5>قاعدة بيانات SQLite</h5>
                            <p>حفظ دائم للبيانات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-server fa-3x text-success mb-3"></i>
                            <h5>Flask API</h5>
                            <p>واجهة برمجة تطبيقات قوية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-sync-alt fa-3x text-info mb-3"></i>
                            <h5>تحديث فوري</h5>
                            <p>جميع التغييرات محفوظة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://kit.fontawesome.com/your-kit-id.js"></script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل خادم Flask...")
    print("🌐 الرابط: http://localhost:5000")
    print("📊 API: http://localhost:5000/api/products")
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    app.run(debug=True, host='0.0.0.0', port=5000)
