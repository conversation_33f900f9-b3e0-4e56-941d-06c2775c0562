#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق قطع غيار السيارات البسيط مع قاعدة البيانات
Simple Auto Parts Shop System with Database
"""

import sqlite3
import os
from datetime import datetime

# إنشاء قاعدة البيانات
def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect('auto_parts_simple.db')
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            name TEXT NOT NULL,
            shop_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول المحلات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS shops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT NOT NULL,
            phone TEXT NOT NULL,
            email TEXT,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            specialty TEXT,
            rating REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول المنتجات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            part_number TEXT,
            category TEXT,
            brand TEXT,
            model TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول تفاصيل المنتجات في المحلات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS shop_products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shop_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 0,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shop_id) REFERENCES shops (id),
            FOREIGN KEY (product_id) REFERENCES products (id),
            UNIQUE(shop_id, product_id)
        )
    ''')
    
    # التحقق من وجود بيانات
    cursor.execute("SELECT COUNT(*) FROM users")
    if cursor.fetchone()[0] == 0:
        # إدراج البيانات الأولية
        print("إدراج البيانات الأولية...")
        
        # إدراج المدير
        cursor.execute('''
            INSERT INTO users (username, password, role, name)
            VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin123', 'admin', 'المدير'))
        
        # إدراج محل تجريبي
        cursor.execute('''
            INSERT INTO shops (name, description, phone, email, address, city, specialty, rating)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'محل الأصيل لقطع غيار السيارات',
            'محل متخصص في قطع غيار السيارات اليابانية والكورية',
            '+966501234567',
            '<EMAIL>',
            'شارع الملك فهد، الرياض',
            'الرياض',
            'قطع غيار يابانية',
            4.5
        ))
        
        shop_id = cursor.lastrowid
        
        # إدراج مستخدم المحل
        cursor.execute('''
            INSERT INTO users (username, password, role, name, shop_id)
            VALUES (?, ?, ?, ?, ?)
        ''', ('shop1', 'shop123', 'shop', 'محل الأصيل لقطع غيار السيارات', shop_id))
        
        # إدراج منتجات تجريبية
        products_data = [
            ('فلتر هواء تويوتا كامري', 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2015-2020', 'TOY-AF-001', 'فلاتر', 'تويوتا', 'كامري'),
            ('تيل فرامل هوندا أكورد', 'تيل فرامل عالي الجودة لسيارة هوندا أكورد', 'HON-BP-002', 'فرامل', 'هوندا', 'أكورد'),
            ('زيت محرك موبيل 1', 'زيت محرك صناعي بالكامل، مناسب لجميع أنواع السيارات', 'MOB-OIL-003', 'زيوت', 'موبيل', ''),
            ('بطارية نيسان التيما', 'بطارية أصلية لسيارة نيسان التيما، 12 فولت 70 أمبير', 'NIS-BAT-004', 'كهرباء', 'نيسان', 'التيما')
        ]
        
        prices = [85.0, 120.0, 95.0, 280.0]
        quantities = [15, 8, 25, 5]
        
        for i, product_data in enumerate(products_data):
            cursor.execute('''
                INSERT INTO products (name, description, part_number, category, brand, model)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', product_data)
            
            product_id = cursor.lastrowid
            
            # ربط المنتج بالمحل
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (shop_id, product_id, prices[i], quantities[i]))
        
        print("تم إدراج البيانات الأولية بنجاح!")
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")

def get_all_products():
    """الحصول على جميع المنتجات مع تفاصيل المحلات"""
    conn = sqlite3.connect('auto_parts_simple.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT p.*, sp.price, sp.quantity, s.name as shop_name, s.id as shop_id
        FROM products p
        JOIN shop_products sp ON p.id = sp.product_id
        JOIN shops s ON sp.shop_id = s.id
        ORDER BY p.id, s.name
    ''')
    
    products_dict = {}
    for row in cursor.fetchall():
        product_id = row['id']
        if product_id not in products_dict:
            products_dict[product_id] = {
                'id': row['id'],
                'name': row['name'],
                'description': row['description'],
                'partNumber': row['part_number'],
                'category': row['category'],
                'brand': row['brand'],
                'model': row['model'],
                'shopDetails': []
            }
        
        products_dict[product_id]['shopDetails'].append({
            'shopName': row['shop_name'],
            'shopId': row['shop_id'],
            'price': row['price'],
            'quantity': row['quantity']
        })
    
    products = list(products_dict.values())
    conn.close()
    return products

def get_all_shops():
    """الحصول على جميع المحلات"""
    conn = sqlite3.connect('auto_parts_simple.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT s.*, COUNT(sp.product_id) as products_count
        FROM shops s
        LEFT JOIN shop_products sp ON s.id = sp.shop_id
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ''')
    
    shops = []
    for row in cursor.fetchall():
        shop = dict(row)
        shop['joinDate'] = shop['created_at'][:10]
        shops.append(shop)
    
    conn.close()
    return shops

def add_new_shop(shop_data):
    """إضافة محل جديد"""
    conn = sqlite3.connect('auto_parts_simple.db')
    cursor = conn.cursor()
    
    try:
        # التحقق من عدم تكرار اسم المستخدم
        cursor.execute('SELECT id FROM users WHERE username = ?', (shop_data['username'],))
        if cursor.fetchone():
            return {'success': False, 'message': 'اسم المستخدم موجود بالفعل'}
        
        # التحقق من عدم تكرار اسم المحل
        cursor.execute('SELECT id FROM shops WHERE name = ?', (shop_data['name'],))
        if cursor.fetchone():
            return {'success': False, 'message': 'اسم المحل موجود بالفعل'}
        
        # إدراج المحل
        cursor.execute('''
            INSERT INTO shops (name, description, phone, email, address, city, specialty, rating)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            shop_data['name'], shop_data['description'], shop_data['phone'], 
            shop_data.get('email', ''), shop_data['address'], shop_data['city'], 
            shop_data.get('specialty', ''), 0.0
        ))
        
        shop_id = cursor.lastrowid
        
        # إدراج المستخدم
        cursor.execute('''
            INSERT INTO users (username, password, role, name, shop_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (shop_data['username'], shop_data['password'], 'shop', shop_data['name'], shop_id))
        
        conn.commit()
        return {'success': True, 'message': 'تم إضافة المحل بنجاح', 'shop_id': shop_id}
        
    except Exception as e:
        conn.rollback()
        return {'success': False, 'message': f'خطأ في إضافة المحل: {str(e)}'}
    finally:
        conn.close()

def add_new_product(product_data):
    """إضافة منتج جديد"""
    conn = sqlite3.connect('auto_parts_simple.db')
    cursor = conn.cursor()
    
    try:
        # البحث عن منتج موجود بنفس الاسم ورقم القطعة
        cursor.execute('''
            SELECT id FROM products 
            WHERE name = ? AND (part_number = ? OR (part_number IS NULL AND ? IS NULL))
        ''', (product_data['name'], product_data.get('partNumber'), product_data.get('partNumber')))
        
        existing_product = cursor.fetchone()
        
        if existing_product:
            product_id = existing_product[0]
            # التحقق من عدم وجود المنتج في نفس المحل
            cursor.execute('''
                SELECT id FROM shop_products WHERE shop_id = ? AND product_id = ?
            ''', (product_data['shopId'], product_id))
            
            if cursor.fetchone():
                return {'success': False, 'message': 'المنتج موجود بالفعل في محلك'}
            
            # إضافة المنتج للمحل
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (product_data['shopId'], product_id, product_data['price'], product_data.get('quantity', 0)))
        else:
            # إنشاء منتج جديد
            cursor.execute('''
                INSERT INTO products (name, description, part_number, category, brand, model)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                product_data['name'], product_data.get('description', ''), 
                product_data.get('partNumber', ''), product_data.get('category', ''), 
                product_data.get('brand', ''), product_data.get('model', '')
            ))
            
            product_id = cursor.lastrowid
            
            # ربط المنتج بالمحل
            cursor.execute('''
                INSERT INTO shop_products (shop_id, product_id, price, quantity)
                VALUES (?, ?, ?, ?)
            ''', (product_data['shopId'], product_id, product_data['price'], product_data.get('quantity', 0)))
        
        conn.commit()
        return {'success': True, 'message': 'تم إضافة المنتج بنجاح', 'product_id': product_id}
        
    except Exception as e:
        conn.rollback()
        return {'success': False, 'message': f'خطأ في إضافة المنتج: {str(e)}'}
    finally:
        conn.close()

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🚗 تطبيق قطع غيار السيارات البسيط مع قاعدة البيانات")
    print("Simple Auto Parts Shop System with Database")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    print("🗄️ إنشاء قاعدة البيانات...")
    init_database()
    
    # عرض البيانات
    print("\n📊 عرض البيانات من قاعدة البيانات:")
    
    print("\n🏪 المحلات:")
    shops = get_all_shops()
    for shop in shops:
        print(f"- {shop['name']} ({shop['products_count']} منتج)")
    
    print("\n📦 المنتجات:")
    products = get_all_products()
    for product in products:
        print(f"- {product['name']} ({len(product['shopDetails'])} محل)")
        for shop_detail in product['shopDetails']:
            print(f"  * {shop_detail['shopName']}: {shop_detail['price']} ريال (الكمية: {shop_detail['quantity']})")
    
    print(f"\n📈 الإحصائيات:")
    print(f"- إجمالي المحلات: {len(shops)}")
    print(f"- إجمالي المنتجات: {len(products)}")
    
    # اختبار إضافة محل جديد
    print("\n🧪 اختبار إضافة محل جديد...")
    new_shop_data = {
        'username': 'testshop',
        'password': 'test123',
        'name': 'محل الاختبار',
        'description': 'محل تجريبي للاختبار',
        'phone': '+966501111111',
        'email': '<EMAIL>',
        'address': 'شارع الاختبار',
        'city': 'الرياض',
        'specialty': 'قطع غيار عامة'
    }
    
    result = add_new_shop(new_shop_data)
    print(f"نتيجة إضافة المحل: {result['message']}")
    
    if result['success']:
        # اختبار إضافة منتج للمحل الجديد
        print("\n🧪 اختبار إضافة منتج للمحل الجديد...")
        new_product_data = {
            'name': 'منتج تجريبي',
            'description': 'منتج للاختبار',
            'partNumber': 'TEST-001',
            'category': 'اختبار',
            'brand': 'تجريبي',
            'model': 'اختبار',
            'shopId': result['shop_id'],
            'price': 100.0,
            'quantity': 10
        }
        
        product_result = add_new_product(new_product_data)
        print(f"نتيجة إضافة المنتج: {product_result['message']}")
    
    print("\n✅ انتهى الاختبار!")
    print("📁 ملف قاعدة البيانات: auto_parts_simple.db")
    print("🔍 يمكنك فتح قاعدة البيانات باستخدام أي أداة SQLite")

if __name__ == '__main__':
    main()
