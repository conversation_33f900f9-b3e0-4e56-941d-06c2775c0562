#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🐍 اختبار Python...")
print("✅ Python يعمل بنجاح!")

try:
    import http.server
    print("✅ http.server متوفر")
    
    import socketserver
    print("✅ socketserver متوفر")
    
    import webbrowser
    print("✅ webbrowser متوفر")
    
    print("\n🚀 جميع المكتبات المطلوبة متوفرة!")
    print("يمكن تشغيل الخادم البسيط")
    
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبة: {e}")

input("\nاضغط Enter للخروج...")
