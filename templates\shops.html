{% extends "base.html" %}

{% block title %}المحلات - نظام ربط محلات قطع غيار السيارات{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="fw-bold">جميع المحلات</h2>
            <p class="text-muted">اكتشف محلات قطع غيار السيارات المعتمدة</p>
        </div>
    </div>
    
    {% if shops %}
    <div class="row">
        {% for shop in shops %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shop-card">
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-store fa-3x text-primary mb-2"></i>
                        <h5 class="card-title">{{ shop.name }}</h5>
                    </div>
                    
                    {% if shop.description %}
                    <p class="card-text text-muted">{{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}</p>
                    {% endif %}
                    
                    <div class="shop-info">
                        {% if shop.address %}
                        <p class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            <small>{{ shop.address }}</small>
                        </p>
                        {% endif %}
                        
                        {% if shop.phone %}
                        <p class="mb-2">
                            <i class="fas fa-phone text-muted me-2"></i>
                            <small>{{ shop.phone }}</small>
                        </p>
                        {% endif %}
                        
                        {% if shop.email %}
                        <p class="mb-2">
                            <i class="fas fa-envelope text-muted me-2"></i>
                            <small>{{ shop.email }}</small>
                        </p>
                        {% endif %}
                    </div>
                    
                    <div class="rating text-center mb-3">
                        {% for i in range(5) %}
                            {% if i < shop.rating %}
                                <i class="fas fa-star text-warning"></i>
                            {% else %}
                                <i class="far fa-star text-warning"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="ms-2 text-muted">({{ "%.1f"|format(shop.rating) }})</span>
                    </div>
                    
                    <div class="shop-stats row text-center mb-3">
                        <div class="col-6">
                            <small class="text-muted">المنتجات</small>
                            <div class="fw-bold">{{ shop.products|length }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">التقييمات</small>
                            <div class="fw-bold">{{ shop.reviews|length }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="{{ url_for('shop_products', shop_id=shop.id) }}" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-eye me-1"></i>
                                عرض المنتجات
                            </a>
                        </div>
                        <div class="col-6">
                            {% if shop.phone %}
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="contactShop('{{ shop.phone }}', '{{ shop.name }}')">
                                <i class="fas fa-phone me-1"></i>
                                اتصل
                            </button>
                            {% else %}
                            <button class="btn btn-outline-secondary btn-sm w-100" disabled>
                                <i class="fas fa-phone me-1"></i>
                                لا يوجد رقم
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-store fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد محلات مسجلة حالياً</h4>
        <p class="text-muted">سيتم عرض المحلات هنا عند تسجيلها في النظام</p>
    </div>
    {% endif %}
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تواصل مع المحل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                <h4 id="shopName"></h4>
                <p class="lead" id="shopPhone"></p>
                <p class="text-muted">يمكنك الاتصال مباشرة بالمحل للاستفسار</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a id="callButton" href="#" class="btn btn-primary">
                    <i class="fas fa-phone me-1"></i>
                    اتصل الآن
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.shop-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.shop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.shop-info p {
    margin-bottom: 0.5rem;
}

.shop-stats {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function contactShop(phone, shopName) {
    document.getElementById('shopName').textContent = shopName;
    document.getElementById('shopPhone').textContent = phone;
    document.getElementById('callButton').href = 'tel:' + phone;
    
    var modal = new bootstrap.Modal(document.getElementById('contactModal'));
    modal.show();
}
</script>
{% endblock %}
